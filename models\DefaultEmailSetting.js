const mongoose = require("mongoose");

const defaultEmailSettingSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: true,
      match: [/.+\@.+\..+/, "Please provide a valid email"],
      unique: true,
    },
    template: {
      type: String,
      required: true,
    },
    businessId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Business",
      required: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    default:{
      type: Boolean,
      default: false,
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model(
  "DefaultEmailSetting",
  defaultEmailSettingSchema
);
