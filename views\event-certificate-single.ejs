<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", { "title": "All Certificates Issued" }) %>

<!-- DataTables CSS for Bootstrap 5 -->
<link
  rel="stylesheet"
  href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css"
/>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<%- contentFor('body') %>
<%-include("partials/page-title",{"title":"Certificates Issued" , "pagetitle":"All" }) %>

<div class="row">
  <!-- Issued Certificates Starts -->
  <div class="col-xl-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title certificateEventName" id="">Loading...</h4>
        <p class="card-title-desc d-none">Loading...</p>
      </div>

      <div class="card-body">
        <!-- Nav tabs -->
        <ul class="nav nav-tabs nav-tabs-custom nav-justified" role="tablist">
          <li class="nav-item" role="presentation">
            <a
              class="nav-link active"
              data-bs-toggle="tab"
              href="#design"
              role="tab"
              aria-selected="true"
            >
              <span class="d-block d-sm-none"
                ><i class="fas fa-paint-brush"></i
              ></span>
              <span class="d-none d-sm-block">Overview</span>
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              data-bs-toggle="tab"
              href="#variables"
              role="tab"
              aria-selected="false"
            >
              <span class="d-block d-sm-none"><i class="fas fa-list"></i></span>
              <span class="d-none d-sm-block">Variables</span>
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              data-bs-toggle="tab"
              href="#candidates"
              role="tab"
              aria-selected="false"
            >
              <span class="d-block d-sm-none"
                ><i class="fas fa-users"></i
              ></span>
              <span class="d-none d-sm-block">Candidates</span>
            </a>
          </li>
          <li class="nav-item d-none" role="presentation">
            <a
              class="nav-link"
              data-bs-toggle="tab"
              href="#email"
              role="tab"
              aria-selected="false"
            >
              <span class="d-block d-sm-none"
                ><i class="fas fa-chart-bar"></i
              ></span>
              <span class="d-none d-sm-block">Mail</span>
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              data-bs-toggle="tab"
              href="#settings"
              role="tab"
              aria-selected="false"
            >
              <span class="d-block d-sm-none"><i class="fas fa-cog"></i></span>
              <span class="d-none d-sm-block">Settings</span>
            </a>
          </li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content p-3 text-muted">
          <!-- Design Tab -->
          <div class="tab-pane active" id="design" role="tabpanel">
            <p class="mb-3 d-none">
              Customize the design of your certificate. You can edit the
              existing design or create a new one.
            </p>
            <div class="row">
              <div class="col-md-6">
                <ul class="list-group">
                  <li class="list-group-item">
                    Name:
                    <span class="certificateEventName" id="">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Location: <span id="locationValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Date: <span id="dateRangeValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Expire Duration:
                    <span id="expireDurationValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Total Contacts:
                    <span id="totalContactsValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Certificates Generated:
                    <span id="certificatesGeneratedValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Emails Sent: <span id="emailsSentValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Emails Opened:
                    <span id="emailsOpenedValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Emails Clicked:
                    <span id="emailsClickedValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Cert Downloaded:
                    <span id="certDownloadedValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Cert Verified:
                    <span id="certVerifiedValue">Loading...</span>
                  </li>
                  <li class="list-group-item">
                    Status: <span class="" id="status-badge">Loading...</span>
                  </li>

                  <!-- action button starts -->
                  <li class="list-group-item">
                    <button
                      id="campaign-action-button"
                      class="btn btn-primary btn-lg"
                    >
                      <span id="button-label" class="">Loading...</span>
                      <span
                        id="loading-spinner"
                        class="spinner-border spinner-border-sm d-none"
                        role="status"
                      ></span>
                    </button>
                  </li>
                  <!-- action button ends -->
                </ul>
              </div>

              <!-- Mail Event Image -->
              <div
                class="col-md-6 d-flex flex-column justify-content-center align-items-center text-center"
              >
                <img
                  src="/assets/images/certificate/sample-certificate-horizontal.jpg"
                  id="event-thumbnail"
                  alt="Certificate Design"
                  class="img-fluid mb-3"
                />

                <a
                  id="edit-design"
                  href="/design/${designId}/"
                  target="_blank"
                  class="btn btn-primary disabled"
                  >Edit Design</a
                >
              </div>
            </div>
          </div>

          <!-- Variables Tab -->
          <div class="tab-pane" id="variables" role="tabpanel">
            <p class="mb-3 d-none">
              Manage the variables used in your certificates. You can add new
              variables or edit existing ones.
            </p>
            <ul class="list-group mb-3">
              <li class="list-group-item">[full-name]</li>
              <li class="list-group-item">[role]</li>
              <li class="list-group-item">[certificate-id]</li>
            </ul>
            <a
              id="add-new-variable"
              target="_blank"
              href="/design/${designId}/"
              class="btn btn-success"
              >Add New Variable</a
            >
          </div>

          <!-- Candidates Tab -->
          <div class="tab-pane" id="candidates" role="tabpanel">
            <p class="mb-3 d-none">
              View and manage the list of candidates associated with this
              certificate.
            </p>

            <div class="d-flex align-items-center gap-2">
              <div class="mb-3">
                <button
                  type="button"
                  class="btn btn-primary"
                  data-bs-toggle="modal"
                  data-bs-target="#bigContactModal"
                >
                  <i class="bx bx-plus me-1"></i> Add from Contacts
                </button>
              </div>
              <div class="mb-3">
                <button type="button" class="btn btn-primary" id="bulkEmails">
                  <i class="bx bx-plus me-1"></i> Send Bulk Emails
                </button>
              </div>
              <div class="mb-3">
                <button type="button" class="btn btn-primary" id="bulkDownload">
                  <i class="bx bx-plus me-1"></i> Bulk Download
                </button>
              </div>
            </div>

            <table
              id="candidatesTable"
              class="table table-striped table-bordered"
            >
              <thead>
                <tr>
                  <th>Name</th>
                  <th>E-Mail</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- DataTable content will be loaded here -->
              </tbody>
            </table>
          </div>

          <!-- email Tab -->
          <div class="tab-pane" id="email" role="tabpanel">
            <p class="mb-3 d-none">
              Set the email that will be sent to candidates
            </p>
            <div class="row">
              <h2>Coming Soon</h2>
            </div>
          </div>

          <!-- Settings Tab -->
          <div class="tab-pane" id="settings" role="tabpanel">
            <p class="mb-3 d-none">
              Adjust the settings for this certificate. You can enable or
              disable new certificate generation and delete this certificate.
            </p>

            <div class="mt-3">
              <div class="form-check form-switch mb-3">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="lockNewCertificateGeneration"
                />
                <label
                  class="form-check-label"
                  for="lockNewCertificateGeneration"
                  >Lock New Certificate Generation</label
                >
              </div>
            </div>

            <!-- Webhooks Section -->
            <div class="mt-4">
              <h5 class="mb-3">Webhooks</h5>
              
              <!-- Add Webhook Button -->
              <button class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#webhookModal">
                <i class="bx bx-plus me-1"></i> Add Webhook
              </button>

              <!-- Webhooks List -->
              <div class="table-responsive">
                <table class="table table-bordered" id="webhooksTable">
                  <thead>
                    <tr>
                      <th>URL</th>
                      <th>Triggers</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Webhooks will be loaded here dynamically -->
                  </tbody>
                </table>
              </div>
            </div>

            <div class="mt-5">
              <h5>Delete this Event</h5>
              <button
                class="btn btn-danger btn-sm mt-1"
                onclick="showDeleteModal('yourEventId')"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Candidate Modal -->
    <div
      class="modal fade"
      id="addCandidateModal"
      tabindex="-1"
      aria-labelledby="addCandidateModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="addCandidateModalLabel">
              Add New Candidate
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- Add candidate form -->
            <form id="addCandidateForm">
              <div class="mb-3">
                <label for="candidateName" class="form-label">Name</label>
                <input
                  type="text"
                  class="form-control"
                  id="candidateName"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="candidateEmail" class="form-label">E-Mail</label>
                <input
                  type="email"
                  class="form-control"
                  id="candidateEmail"
                  required
                />
              </div>
              <button type="submit" class="btn btn-primary">
                Add Candidate
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Issued Certificates Ends -->
</div>

<%- contentFor('FooterJs') %>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<!-- DataTables Bootstrap 5 Integration JS -->
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<!-- jquery-knob plugin  -->
<script src="/assets/libs/jquery-knob/jquery.knob.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Candidate table features-->
<script>
  $(function () {
    $("#addCandidateForm").on("submit", function (e) {
      e.preventDefault();
      // Handle form submission for adding a candidate
    });

    // Edit and Delete Button Actions
    $("#candidatesTable").on("click", ".edit-btn", function () {
      const id = $(this).data("id");
      // Handle edit action, e.g., open a modal for editing the candidate
    });

    $("#candidatesTable").on("click", ".delete-btn", function () {
      const id = $(this).data("id");
      // Handle delete action
    });
  });
</script>

<script>
  let intervalId = null;
  let lastCertificateCount = 0;

  // Define the polling functions globally outside of any document ready function
  function startPollingImpl() {
    if (!intervalId) {
      // Store the current certificate count before starting polling
      lastCertificateCount =
        parseInt($("#certificatesGeneratedValue").text()) || 0;

      intervalId = setInterval(fetchCertificatesBasedOnEvent, 5000);
      console.log(
        "Polling resumed. Last certificate count:",
        lastCertificateCount
      );
    }
  }

  function stopPollingImpl() {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
      console.log("Polling paused.");
    }
  }

  // Get the event ID from the URL - available globally
  let globalEventId = window.location.pathname.split("/").pop();

  // Define fetchCertificatesBasedOnEvent globally
  function fetchCertificatesBasedOnEvent() {
    $.ajax({
      url: `/api/events/certificate/${globalEventId}`,
      type: "GET",
      success: function (response) {
        console.log(response, "response");

        if (response) {
          const currentCertCount = response.certificates?.length || 0;
          $("#certificatesGeneratedValue").text(currentCertCount);
          $("#campaign-action-button").text(response.status);

          // IMPORTANT: Check status first and stop polling if completed
          if (response.status === "Completed") {
            stopPollingImpl();
            console.log("Event completed, polling stopped.");
            return; // Exit early to prevent further processing
          }

          // Check if new certificates have been generated
          if (currentCertCount > lastCertificateCount) {
            showToast(
              `${
                currentCertCount - lastCertificateCount
              } new certificate(s) generated successfully!`,
              "success"
            );
            lastCertificateCount = currentCertCount;

            // Refresh the candidates table to show updated status
            if (window.refreshContactTable) {
              window.refreshContactTable();
            }
          }
        } else {
          console.error("No data returned for event");
        }
      },
      error: function (xhr, status, error) {
        console.error(`Error fetching event data: ${error}`);
      },
    });
  }

  // Make the functions globally available
  window.startPolling = startPollingImpl;
  window.stopPolling = stopPollingImpl;

  $(document).ready(function () {
    // Check initial status before starting polling
    $.ajax({
      url: `/api/events/certificate/${globalEventId}`,
      type: "GET",
      success: function (response) {
        if (response && response.status === "Completed") {
          console.log("Event already completed, not starting polling.");
          // Don't start polling if already completed
        } else {
          // Start polling only if not completed
          startPollingImpl();
        }
      },
      error: function (xhr, status, error) {
        console.error("Error checking initial status:", error);
        // Start polling anyway in case of error
        startPollingImpl();
      },
    });

    // Handle tab visibility changes
    document.addEventListener("visibilitychange", function () {
      if (document.hidden) {
        stopPollingImpl();
      } else {
        // Check status before resuming polling
        $.ajax({
          url: `/api/events/certificate/${globalEventId}`,
          type: "GET",
          success: function (response) {
            if (response && response.status !== "Completed") {
              startPollingImpl();
            }
          },
        });
      }
    });

    // Functions are already made available globally
  });
</script>

<!-- fetch single certificate event data starts  -->
<script>
  $(document).ready(function () {
    // Fetch event ID from URL
    const eventId = window.location.pathname.split("/").pop();
    let contactListId = "";
    let newContactId = "";
    let recipientEmails = new Set();
    // Fetch data from API
    $.ajax({
      url: `/api/events/certificate/${eventId}`,
      type: "GET",
      success: function (response) {
        if (response) {
          populateEventData(response);
        } else {
          console.error("No data returned for event");
        }
      },
      error: function (xhr, status, error) {
        console.error(`Error fetching event data: ${error}`);
      },
    });

    // Improved search functionality with debounce
    let searchTimeout;
    $(document).on("input", "#email", function () {
      const value = $(this).val().trim();
      console.log(value, "value");

      // Clear any existing timeout
      clearTimeout(searchTimeout);

      if (value.length > 0) {
        // Show a "Typing..." message
        $("#searchResults").html(
          '<div class="list-group-item text-muted">Typing...</div>'
        );

        // Set a timeout to search after typing stops
        searchTimeout = setTimeout(function () {
          // Show loading indicator
          $("#loadingIndicator").removeClass("d-none");
          $("#searchResults").empty();

          // Log the search request for debugging
          console.log("Searching for contacts with email:", value);

          // Make the AJAX request
          $.ajax({
            url: `/api/contacts/search?email=${encodeURIComponent(value)}`,
            method: "GET",
            success: function (data) {
              console.log("Search results:", data);
              $("#searchResults").empty();

              if (data && data.contacts && data.contacts.length > 0) {
                data.contacts.forEach((contact) => {
                  $("#searchResults").append(`
                    <div class="list-group-item list-group-item-action" data-contact='${JSON.stringify(
                      contact
                    )}'>
                      <i class="bx bx-plus me-1"></i> <strong>${
                        contact.fullName
                      }</strong> - ${contact.businessEmail}
                    </div>
                  `);
                });
              } else {
                $("#searchResults").append(
                  '<div class="list-group-item">No contacts found</div>'
                );
              }
            },
            error: function (xhr, status, error) {
              console.error("Error searching contacts:", error);
              $("#searchResults")
                .empty()
                .append(
                  '<div class="list-group-item text-danger">Error searching for contacts</div>'
                );
            },
            complete: function () {
              // Hide loading indicator
              $("#loadingIndicator").addClass("d-none");
            },
          });
        }, 300); // 300ms debounce
      } else {
        $("#searchResults").empty();
      }
    });

    // Improved click handler for search results
    $(document).on("click", ".list-group-item-action", function () {
      if (!contactListId) {
        showToast("No Contact List Found.", "danger");
        return;
      }

      try {
        // Get contact data from the clicked item
        const contactData = $(this).attr("data-contact");
        console.log("Contact data:", contactData);

        if (!contactData) {
          console.error("No contact data found in clicked element");
          return;
        }

        const parsedContact = JSON.parse(contactData);
        console.log("Parsed contact:", parsedContact);

        if (!parsedContact || !parsedContact._id) {
          console.error("Invalid contact data or missing ID");
          return;
        }

        // Store the contact ID for later use
        newContactId = parsedContact._id;
        console.log("Selected contact ID:", newContactId);
        recipientEmails.add(parsedContact.businessEmail);

        // Highlight the selected contact
        $(".list-group-item-action").removeClass("active");
        $(this).addClass("active");

        // Add the contact to the list
        addContactToList(contactListId);
      } catch (error) {
        console.error("Error processing contact selection:", error);
        showToast("Error selecting contact. Please try again.", "danger");
      }
    });

    function addContactToList(contactListId) {
      // Confirm the action
      if (confirm("Are you sure you want to add this contact to the list?")) {
        // Show loading indicator
        $("#loadingIndicator").removeClass("d-none");
        $.ajax({
          url: `/api/contactlists/addcontact/${contactListId}`, // Endpoint to add the contact
          type: "PUT", // Using PUT method
          contentType: "application/json", // Specify JSON content type
          data: JSON.stringify({
            contactId: newContactId, // Send the contactId to add
          }),
          success: function (data) {
            // On success, alert the user
            showToast("Contact added successfully to the list.", "primary");

            // Generate certificate for the new contact
            generateCertificateForNewContact();

            // Close modals and clear input fields
            $("#bigContactModal").modal("hide");
            $("#email").val("");
            $("#searchResults").empty();

            // Refresh the DataTable to show the newly added contact
            window.refreshContactTable();
          },
          error: function (error) {
            console.log(error);
            showToast(error.responseJSON.message||"Failed to add the contact to the list.", "danger");
            // Hide loading indicator in case of error
            $("#loadingIndicator").addClass("d-none");
          },
          // Note: We don't hide the loading indicator here because generateCertificateForNewContact will handle it
        });
      }
    }

    // Function to refresh the candidates table - make it globally available
    
    window.refreshContactTable = function () {
      // Destroy existing DataTable
      if ($.fn.DataTable.isDataTable("#candidatesTable")) {
        $("#candidatesTable").DataTable().destroy();
      }

      // Fetch updated data and reinitialize the table
      $.ajax({
        url: `/api/events/certificate/${eventId}`,
        type: "GET",
        beforeSend: function () {
          // Show loading indicator if not already visible
          if ($("#loadingIndicator").hasClass("d-none")) {
            $("#loadingIndicator").removeClass("d-none");
          }
        },
        success: function (response) {
          if (
            response &&
            response.contactList &&
            response.contactList?.contacts
          ) {
            // Update the total contacts count
            $("#totalContactsValue").text(
              response.contactList.contacts.length || 0
            );

            // Reinitialize DataTable with updated data
            $("#candidatesTable").DataTable({
              buttons: ["copy", "excel", "pdf"],
              responsive: true,
              autoWidth: false,
              data: response.contactList.contacts,
              columns: [
                { data: "fullName", title: "Name" },
                { data: "businessEmail", title: "E-Mail" },
                { data: "createdAt", title: "Created" },
                {
                  data: null,
                  title: "Actions",
                  render: function (data, type, row) {
                   
                    return `<a href="/profile/${data._id}" target="_blank" class="btn btn-primary btn-sm mx-1">View</a><button class="btn btn-primary btn-sm mx-1">Download</button><button class="btn btn-primary btn-sm mx-1">Edit</button><button class="btn btn-primary btn-sm mx-1">Send Mail</button>
                    <button class="btn btn-primary btn-sm mx-1 deleteCandidate" data-details='${JSON.stringify(data)}'>Delete</button>`;
                  },
                },
              ],
            });
          } else {
            console.error("No data returned for event");
            showToast("Failed to refresh contact list.", "danger");
          }
        },
        error: function (xhr, status, error) {
          console.error(`Error fetching event data: ${error}`);
          showToast("Failed to refresh contact list.", "danger");
        },
        complete: function () {
          // Hide loading indicator
          $("#loadingIndicator").addClass("d-none");
        },
      });

      $(document).on("click", ".deleteCandidate", function () {
 const detailsStr = $(this).attr('data-details'); 
const details = JSON.parse(detailsStr);          
   
  $.ajax({
    method:"DELETE",
    url:"/api/events/delete/candidate",
    data:JSON.stringify({
      eventId,
      contactId:details._id
    }),
    contentType:"application/json",
    success:function(response){
      console.log(response,'delete response from table refresh')
      if(response.success){
        refreshCandidatesTable()
        showToast(response.message,'primary')
      }
    },
    error:function(error){
      console.log(error)
    }
  })

});
    };


    // Alias for backward compatibility
    function refreshCandidatesTable() {
      window.refreshContactTable();
    }

    async function generateCertificateForNewContact() {
      if (!newContactId) {
        // Hide loading indicator if no contact ID
        $("#loadingIndicator").addClass("d-none");
        console.error("No contact ID available for certificate generation");
        showToast("No contact ID available for certificate generation");
        return;
      }

      // Loading indicator is already shown by addContactToList
      showToast("Generating certificate for new contact...", "primary");
      const currentEventId = window.location.pathname.split("/").pop();
      console.log(
        "Generating certificate for contact ID:",
        newContactId,
        "in event:",
        currentEventId
      );
      $("#campaign-action-button").text("In Progress");
      $.ajax({
        method: "POST",
        url: `/api/events/new-contact-to-event/${currentEventId}`,
        contentType: "application/json",
        data: JSON.stringify({
          contactId: newContactId,
          eventId: currentEventId,
        }),
        success: function (response) {
          console.log("Certificate generation initiated:", response);
          showToast(
            "Certificate generation initiated successfully.",
            "success"
          );

          $("#campaign-action-button").text("Completed");
          // Start polling to check certificate status
          startPollingImpl();
          console.log("Started polling after certificate generation");
        },
        error: function (xhr, status, error) {
          console.error("Error generating certificate:", error);
          console.error("Error details:", xhr.responseText);
          showToast(
            "Failed to generate certificate. Please try again.",
            "danger"
          );
        },
        complete: function () {
          // Hide loading indicator
          $("#loadingIndicator").addClass("d-none");
        },
      });
    }
    // Populate data function
    function populateEventData(data) {
      // Update Header Section
      $(".certificateEventName").text(data.title);
      $(".card-title-desc").text(data.description);

      // Update each span by ID
      //$("#nameValue").text(data.name || "N/A");
      $("#locationValue").text(data.location || "N/A");

      const startDate = data.startDate
        ? new Date(data.startDate).toLocaleDateString()
        : "N/A";
      const endDate = data.endDate
        ? new Date(data.endDate).toLocaleDateString()
        : "N/A";
      $("#dateRangeValue").text(`${startDate} - ${endDate}`);

      $("#expireDurationValue").text(data.expireDuration || "N/A");
      $("#totalContactsValue").text(data.contactList?.contacts.length || 0);
      $("#certificatesGeneratedValue").text(data.certificates?.length || 0);
      $("#emailsSentValue").text(data.emailsSent || 0);
      $("#emailsOpenedValue").text(data.emailsOpened || 0);
      $("#emailsClickedValue").text(data.emailsClicked || 0);
      $("#certDownloadedValue").text(data.downloaded || 0);
      $("#certVerifiedValue").text(data.verified || 0);
      $("#status-badge").text(data.status || "NA");

      // Set button text
      $("#campaign-action-button").text(data.status);

      //console.log("fetched design:",data, data.designId)
      // Set design image and edit link
      $("#event-thumbnail").attr(
        "src",
        data.designId?.imageURL ||
          "/assets/images/certificate/sample-certificate-horizontal.jpg"
      );
      $("#edit-design").attr("href", `/design/${data.designId?._id}`);
      $("#add-new-variable").attr("href", `/design/${data.designId?._id}`);

      // Variables Tab

      const variablesHtml = Object.keys(
        data.designId && data.designId.variables ? data.designId.variables : {}
      )
        .map((variable) => `<li class="list-group-item">[${variable}]</li>`)
        .join("");

      $("#variables .list-group").html(
        variablesHtml || "<p>No variables found.</p>"
      );
      contactListId = data.contactList._id;

      recipientEmails = new Set(
        data.contactList.contacts.map((contact) => contact.businessEmail)
      );

      //    console.log("data.contactList", data.contactList)

      //render contact list associated with this event
      $("#candidatesTable").DataTable({
        buttons: ["copy", "excel", "pdf"],
        responsive: true,
        autoWidth: false,
        data: data.contactList.contacts,
        columns: [
          { data: "fullName", title: "Name" },
          { data: "businessEmail", title: "E-Mail" },
          { data: "createdAt", title: "Created" },
          {
            data: null,
            title: "Actions",
            render: function (data, type, row) {
              return `<a href="/profile/${data._id}" target="_blank" class="btn btn-primary btn-sm mx-1">View</a><button class="btn btn-primary btn-sm mx-1">Download</button><button class="btn btn-primary btn-sm mx-1">Edit</button><button class="btn btn-primary btn-sm mx-1">Send Mail</button><button class="btn btn-primary btn-sm mx-1 candidateDeleteBtn" data-details='${JSON.stringify(data)}'>Delete</button>
  `;
            },
          },
        ],
      });

      // email Tab (Example with some hardcoded values, update these based on data)
      $(".knob").each(function (index) {
        $(this).val(
          data[["emailsSent", "emailsOpened", "emailsClicked"][index]] || 0
        );
      });

      // Settings Tab
      $("#lockNewCertificateGeneration").prop("checked", data.locked);
    }

    $(document).on("click", "#bulkEmails", function () {
      const $btn = $(this);
      const originalHtml = $btn.html();

      $btn.prop("disabled", true);
      $btn.html(`
    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
    Sending...
  `);

      $.ajax({
        url: "/api/events/bulk-email-certificates",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({
          recipientEmails: Array.from(recipientEmails),
          eventId,
        }),
        success: function (response) {
          if (response.success) {
            showToast(
              "Certificates sent to recipients successfully",
              "primary"
            );
          } else {
            showToast("Something went wrong", "danger");
          }
        },
        error: function (error) {
          showToast("Internal Server Error! Please Try Again.", "danger");
        },
        complete: function () {
          $btn.prop("disabled", false);
          $btn.html(originalHtml);
        },
      });
    });

$(document).on("click", "#bulkDownload", function () {
  const $btn = $(this);
  const originalHtml = $btn.html();

  $btn.prop("disabled", true).html(`
    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
    Preparing...
  `);

 
  const link = document.createElement('a');
  link.href = `/api/events/certificates/bulk-download?eventId=${eventId}`;
   document.body.appendChild(link);
  link.click();
  link.remove();

  setTimeout(() => {
    $btn.prop("disabled", false).html(originalHtml);
  }, 3000);
});

$(document).on("click", ".candidateDeleteBtn", function () {
 const detailsStr = $(this).attr('data-details'); 
const details = JSON.parse(detailsStr);          
   
  $.ajax({
    method:"DELETE",
    url:"/api/events/delete/candidate",
    data:JSON.stringify({
      eventId,
      contactId:details._id
    }),
    contentType:"application/json",
    success:function(response){
      console.log(response,'delete response')
      if(response.success){
        refreshCandidatesTable()
        showToast(response.message,'primary')
      }
    },
    error:function(error){
      console.log(error)
    }
  })

});


  });
</script>
<!-- fetch single certificate event data ends  -->

<!-- Delete Confirmation Modal -->
<div
  class="modal fade"
  id="deleteEventModal"
  tabindex="-1"
  aria-labelledby="deleteEventModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this? You will not able able to recover
        this.
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDelete">
          Delete
        </button>
      </div>
    </div>
  </div>
</div>
<!-- search contact modal  -->
<div
  class="modal fade"
  id="bigContactModal"
  tabindex="-1"
  aria-labelledby="bigContactModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="bigContactModalLabel">
          Add Contact to Certificate
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <!-- Search Email Input -->
        <div class="mb-3">
          <label for="email" class="form-label" title="Type Email or Domain"
            >Search by Email</label
          >
          <div class="input-group">
            <span class="input-group-text"><i class="bx bx-search"></i></span>
            <input
              type="text"
              class="form-control"
              id="email"
              placeholder="Start typing to search for contacts..."
              autocomplete="off"
            />
          </div>
          <small class="form-text text-muted"
            >Type to search for contacts by email</small
          >
          <div id="searchResults" class="list-group mt-3"></div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center d-none">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Searching for contacts...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Function to open the modal and store the event ID
  function showDeleteModal(eventId) {
    $("#deleteEventModal").modal("show");
    $("#confirmDelete").data("eventId", eventId); // Store event ID in the button's data
  }

  // jQuery to handle AJAX delete request on confirm
  $("#confirmDelete").on("click", function () {
    const eventId = $(this).data("eventId"); // Retrieve stored event ID
    $.ajax({
      url: `/api/events/${eventId}`, // Adjust URL to match your endpoint
      type: "DELETE",
      success: function (response) {
        $("#deleteEventModal").modal("hide");
        // Optionally, remove the deleted event row from the table
        $(`#eventRow_${eventId}`).remove();
        showToast("Event Deleted", "danger");
        // alert('Event deleted successfully!');
      },
      error: function (xhr, status, error) {
        $("#deleteEventModal").modal("hide");
        // alert('Error deleting event: ' + xhr.responseText);
        console.log("Error deleting event: ", xhr.responseText);
        showToast("Error Deleting Event", "danger");
      },
    });
  });
</script>

<!-- action button status starts -->
<script>
  const eventId = window.location.pathname.split("/").pop();
  const eventURL = "/api/events/" + eventId;
  $(document).on("click", "#campaign-action-button", function () {
    let currentStatus = $("#status-badge").text();
    let newStatus;

    switch (currentStatus) {
      case "Scheduled":
        newStatus = "Running";
        break;
      case "Running":
        newStatus = "Paused";
        break;
      case "Paused":
        newStatus = "Completed";
        break;
      case "Completed":
        newStatus = "Completed";
        break;
      default:
        newStatus = "Scheduled";
    }

    //get appropriate button data and update the status
    getButtonStatus(newStatus);
  });

  function getStatusClass(status) {
    return {
      Scheduled: "secondary",
      Running: "success",
      Paused: "warning",
      Completed: "info",
      Cancelled: "danger",
    }[status];
  }

  function getButtonLabel(status) {
    return {
      Scheduled: "Start",
      Running: "Pause",
      Paused: "Complete",
      Completed: "Completed",
      Cancelled: "Restart",
    }[status];
  }

  function getButtonStatus(newStatus) {
    $("#loading-spinner").removeClass("d-none");
    $.ajax({
      url: eventURL,
      method: "PUT",
      data: { status: newStatus },
      success: function (eventData) {
        //console.log(eventData)
        //console.log(eventData.status)

        //if campaign is completed then disable then button
        if (eventData.status === "Completed") {
          $("#button-label").text("Completed").parent().attr("disabled", true);
        } else {
          $("#status-badge").text(newStatus);
          $("#status-badge")
            .removeClass()
            .addClass("" + getStatusClass(newStatus));
          $("#button-label").text(getButtonLabel(newStatus));
        }
      },
      complete: function () {
        $("#loading-spinner").addClass("d-none");
      },
    });
  }

  //calling to load the button by default
  getButtonStatus();
</script>
<!-- action button status ends -->

<!-- Locking event starts -->
<script>
  $(document).ready(function () {
    $("#lockNewCertificateGeneration").on("change", function () {
      const eventId = window.location.pathname.split("/").pop(); // Assuming event ID is in the URL
      const lockStatus = $(this).is(":checked");
      //alert(lockStatus)
      //alert(e)
      const eventURL = "/api/events/lock/" + eventId;
      //alert(eventURL)

      $.ajax({
        url: eventURL,
        method: "PUT",
        contentType: "application/json",
        data: JSON.stringify({ lockStatus }),
        success: function (response) {
          //alert("resp received")
          showToast(response.message);
          //alert(response.message); // Show confirmation message
        },
        error: function (xhr) {
          //alert('Error updating lock status');
          showToast("Error updating lock status", "danger");
          // Optionally, revert the checkbox state in case of error
          $("#lockNewCertificateGeneration").prop("checked", !lockStatus);
        },
      });
    });
  });
</script>
<!-- Locking event ends-->

<!-- Add/Edit Webhook Modal -->
<div class="modal fade" id="webhookModal" tabindex="-1" aria-labelledby="webhookModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="webhookModalLabel">Add Webhook</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="webhookForm">
          <input type="hidden" id="webhookId" value="">
          <div class="mb-3">
            <label for="webhookUrl" class="form-label">Webhook URL</label>
            <input type="url" class="form-control" id="webhookUrl" required placeholder="https://your-domain.com/webhook">
          </div>
          
          <div class="mb-3">
            <label for="webhookTriggers" class="form-label">Event Triggers</label>
            <select class="form-control" id="webhookTriggers" multiple="multiple" required>
              <option value="certificate.bulk_generated">Certificate Bulk Generated</option>
              <option value="candidate.added">Candidate Added</option>
              <option value="candidate.deleted">Candidate Deleted</option>
              <option value="certificate.issued">Certificate Issued</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="webhookSecret" class="form-label">Secret (Optional)</label>
            <div class="input-group">
              <input type="password" class="form-control" id="webhookSecret" placeholder="Enter webhook secret">
              <button class="btn btn-outline-secondary" type="button" id="generateSecret">
                Generate
              </button>
            </div>
            <small class="form-text text-muted">Used for webhook signature verification</small>
          </div>

          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="webhookEnabled" checked>
              <label class="form-check-label" for="webhookEnabled">Enable Webhook</label>
            </div>
          </div>

          <div class="text-end">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary" id="webhookSubmitBtn">Save Webhook</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Delete Webhook Modal -->
<div class="modal fade" id="deleteWebhookModal" tabindex="-1" aria-labelledby="deleteWebhookModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteWebhookModalLabel">Delete Webhook</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this webhook? This action cannot be undone.</p>
        <input type="hidden" id="deleteWebhookId">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteWebhook">Delete</button>
      </div>
    </div>
  </div>
</div>



<!-- Update the script section -->
<script>
  $(document).ready(function() {
    // Initialize Select2 for webhook triggers
    $('#webhookTriggers').select2({
      placeholder: 'Select triggers',
      width: '100%',
      dropdownParent: $('#webhookModal')
    });

    // Generate random secret
    $('#generateSecret').on('click', function() {
      const secret = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15);
      $('#webhookSecret').val(secret);
    });

    // Reset form when modal is closed
    $('#webhookModal').on('hidden.bs.modal', function() {
      $('#webhookForm')[0].reset();
      $('#webhookId').val('');
      $('#webhookTriggers').val(null).trigger('change');
      $('#webhookModalLabel').text('Add Webhook');
    });

    // Handle webhook form submission (Add or Edit)
    $('#webhookForm').on('submit', function(e) {
      e.preventDefault();
      const eventId = window.location.pathname.split('/').pop();
      const webhookId = $('#webhookId').val();
      const webhookData = {
        eventId: eventId,
        url: $('#webhookUrl').val(),
        triggers: $('#webhookTriggers').val(),
        secret: $('#webhookSecret').val() || undefined,
        isEnabled: $('#webhookEnabled').is(':checked')
      };
      const $submitBtn = $('#webhookSubmitBtn');
      const originalText = $submitBtn.text();
      $submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Saving...');
      if (webhookId) {
        // Edit existing webhook
        $.ajax({
          method: 'PUT',
          url: `/api/customWebhooks/${webhookId}`,
          data: JSON.stringify(webhookData),
          contentType: 'application/json',
          success: function(response) {
            showToast('Webhook updated successfully', 'success');
            $('#webhookModal').modal('hide');
            refreshWebhooksTable();
          },
          error: function(error) {
            showToast('Failed to update webhook', 'danger');
          },
          complete: function() {
            $submitBtn.prop('disabled', false).text(originalText);
          }
        });
      } else {
        // Add new webhook
        console.log(webhookData);
        $.ajax({
          method: 'POST',
          url: '/api/customWebhooks',
          data: JSON.stringify(webhookData),
          contentType: 'application/json',
          success: function(response) {
            showToast('Webhook added successfully', 'success');
            $('#webhookModal').modal('hide');
            refreshWebhooksTable();
          },
          error: function(error) {
            showToast('Failed to add webhook', 'danger');
          },
          complete: function() {
            $submitBtn.prop('disabled', false).text(originalText);
          }
        });
      }
    });

    // Edit webhook handler
    $('#webhooksTable').on('click', '.edit-webhook', function(e) {
      e.preventDefault();
      const webhookId = $(this).data('id');
      const eventId = window.location.pathname.split('/').pop();
      // Fetch the webhook data for this event and id
      $.ajax({
        url: `/api/customWebhooks/${eventId}`,
        method: 'GET',
        success: function(response) {
          if (response.webhooks && response.webhooks.length > 0) {
            const webhook = response.webhooks.find(w => w._id === webhookId);
            if (webhook) {
              $('#webhookId').val(webhook._id);
              $('#webhookUrl').val(webhook.url);
              $('#webhookTriggers').val(webhook.triggers).trigger('change');
              $('#webhookSecret').val(webhook.secret || '');
              $('#webhookEnabled').prop('checked', webhook.isEnabled);
              $('#webhookModalLabel').text('Edit Webhook');
              const webhookModal = new bootstrap.Modal(document.getElementById('webhookModal'));
              webhookModal.show();
            }
          }
        },
        error: function() {
          showToast('Failed to fetch webhook details', 'danger');
        }
      });
    });

    // Delete webhook
    $(document).on('click', '.delete-webhook', function() {
      const webhookId = $(this).data('id');
      $('#deleteWebhookId').val(webhookId);
      const deleteModal = new bootstrap.Modal(document.getElementById('deleteWebhookModal'));
      deleteModal.show();
    });

    // Confirm delete webhook
    $('#confirmDeleteWebhook').on('click', function() {
      const webhookId = $('#deleteWebhookId').val();
      const $btn = $(this);
      $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Deleting...');
      $.ajax({
        url: `/api/customWebhooks/${webhookId}`,
        method: 'DELETE',
        success: function() {
          const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteWebhookModal'));
          deleteModal.hide();
          showToast('Webhook deleted successfully', 'success');
          refreshWebhooksTable();
        },
        error: function() {
          showToast('Failed to delete webhook', 'danger');
        },
        complete: function() {
          $btn.prop('disabled', false).text('Delete');
        }
      });
    });

    // Toggle webhook status
    $(document).on('change', '.webhook-status', function() {
      const webhookId = $(this).data('id');
      const isEnabled = $(this).is(':checked');
      $.ajax({
        url: `/api/customWebhooks/${webhookId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ isEnabled }),
        success: function() {
          showToast(`Webhook ${isEnabled ? 'enabled' : 'disabled'} successfully`, 'success');
          refreshWebhooksTable();
        },
        error: function() {
          showToast('Failed to update webhook status', 'danger');
        }
      });
    });

    // Refresh webhooks table for the current event
    function refreshWebhooksTable() {
      const tbody = $('#webhooksTable tbody');
      tbody.empty();
      const eventId = window.location.pathname.split('/').pop();
      $.ajax({
        url: `/api/customWebhooks/${eventId}`,
        method: 'GET',
        success: function(response) {
          if (!response.webhooks || response.webhooks.length === 0) {
            tbody.append(`
              <tr>
                <td colspan="4" class="text-center">No webhooks configured</td>
              </tr>
            `);
            return;
          }
          response.webhooks.forEach(webhook => {
            tbody.append(`
              <tr>
                <td>${webhook.url}</td>
                <td>${webhook.triggers.join(', ')}</td>
                <td>
                  <div class="form-check form-switch">
                    <input class="form-check-input webhook-status" type="checkbox" 
                      data-id="${webhook._id}" ${webhook.isEnabled ? 'checked' : ''}>
                  </div>
                </td>
                <td>
                  <button class="btn btn-sm btn-primary me-1 edit-webhook" data-id="${webhook._id}">
                    <i class="bx bx-edit-alt"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-webhook" data-id="${webhook._id}">
                    <i class="bx bx-trash"></i>
                  </button>
                </td>
              </tr>
            `);
          });
        },
        error: function() {
          tbody.append(`
            <tr>
              <td colspan="4" class="text-center text-danger">Failed to load webhooks</td>
            </tr>
          `);
        }
      });
    }

    // Initial load of webhooks table
    refreshWebhooksTable();
  });
</script>
