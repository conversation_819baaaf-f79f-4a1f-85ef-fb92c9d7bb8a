const { v4: uuidv4 } = require("uuid");
const ApiKey = require("../models/ApiKey");
const User = require("../models/User");
const { createLog } = require("../controllers/logController");
const sendEmail = require("../utils/email");
const crypto = require("crypto");

// Create new API key
exports.createApiKey = async (req, res) => {
  try {
    const { name, scopes, expiresAt } = req.body;

    const newKey = new ApiKey({
      name,
      key: uuidv4(),
      scopes,
      expiresAt,
      businessId: req.user.businessId,
      createdBy: req.user._id,
      status:
        expiresAt && new Date(expiresAt) <= new Date() ? "expired" : "active",
    });

    await newKey.save();
    await createLog(
      {
        eventType: "API Key",
        action: "Created",
        target: newKey._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.status(201).json(newKey);
  } catch (error) {
    res.status(500).json({ message: "Failed to create API key", error });
  }
};

// Get all API keys for the current user's business (with pagination)
exports.getApiKeys = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const { businessId, _id: userId } = req.user;

    // Automatically mark expired keys (optional)
    await ApiKey.updateMany(
      {
        businessId,
        createdBy: userId,
        expiresAt: { $lte: new Date() },
        status: "active",
      },
      { $set: { status: "expired" } }
    );

    const totalCount = await ApiKey.countDocuments({
      businessId,
      createdBy: userId,
    });

    const keys = await ApiKey.find({ businessId, createdBy: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    res.json({
      data: keys,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API keys", error });
  }
};

// Get single API key by ID (only if user owns it)
exports.getApiKeyById = async (req, res) => {
  try {
    const key = await ApiKey.findOne({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });

    if (!key) return res.status(404).json({ message: "API key not found" });
    res.json(key);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API key", error });
  }
};

// Update API key (only if user owns it)
exports.updateApiKey = async (req, res) => {
  try {
    const updatedKey = await ApiKey.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
        createdBy: req.user._id,
      },
      req.body,
      { new: true }
    );

    if (!updatedKey)
      return res.status(404).json({ message: "API key not found" });
    res.json(updatedKey);
  } catch (error) {
    res.status(500).json({ message: "Failed to update API key", error });
  }
};

// Delete API key (only if user owns it)
exports.deleteApiKey = async (req, res) => {
  try {
    const deleted = await ApiKey.findOneAndDelete({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });

    if (!deleted) return res.status(404).json({ message: "API key not found" });
    res.json({ message: "API key deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete API key", error });
  }
};

// Dashboard: Get stats (business-wide)
exports.getApiKeyStats = async (req, res) => {
  try {
    const businessId = req.user.businessId;
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    await ApiKey.updateMany(
      { businessId, expiresAt: { $lte: new Date() }, status: "active" },
      { $set: { status: "expired" } }
    );

    const [total, active, expired, revoked, recentlyUsed] = await Promise.all([
      ApiKey.countDocuments({ businessId }),
      ApiKey.countDocuments({ businessId, status: "active" }),
      ApiKey.countDocuments({ businessId, status: "expired" }),
      ApiKey.countDocuments({ businessId, status: "revoked" }),
      ApiKey.countDocuments({
        businessId,
        lastUsedAt: { $gte: sevenDaysAgo },
      }),
    ]);

    res.json({
      totalKeys: total,
      activeKeys: active,
      expiredKeys: expired,
      revokedKeys: revoked,
      recentlyUsed,
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API key stats", error });
  }
};

// Admin: Revoke an API key
exports.revokeApiKey = async (req, res) => {
  try {
    const key = await ApiKey.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
      },
      { status: "revoked" },
      { new: true }
    );

    if (!key) return res.status(404).json({ message: "API key not found" });
    await createLog(
      {
        eventType: "API Key",
        action: "Revoked",
        target: key._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.json({ message: "API key revoked successfully", key });
  } catch (error) {
    res.status(500).json({ message: "Failed to revoke API key", error });
  }
};

function generateOtp() {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

  return { otp, expiresAt };
}
const hashOtp = (otp) => crypto.createHash("sha256").update(otp).digest("hex");

exports.sendOtpViaEmail = async (req, res) => {
  try {
    const { email, apiKeyId } = req.body;

    const user = await User.findOne({ email });
    const apiKey = await ApiKey.findById(apiKeyId);

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (!apiKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    const { otp, expiresAt } = generateOtp();
    apiKey.otp = hashOtp(otp);
    apiKey.otpExpiresAt = expiresAt;
    await apiKey.save();

    // Email HTML
    const htmlMessage = `
      <div style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
        <h2 style="color: #333;">MixCertificate - One-Time Password (OTP)</h2>
        <p>Dear ${user.name || "User"},</p>
        <p>We have received a request to access your MixCertificate account using an API key. For security reasons, we need to verify that you are the authorized user.</p>
        <p><strong>Your One-Time Password (OTP) is:</strong></p>
        <h3 style="color: #007bff;">${otp}</h3>
        <p>This OTP will expire in <strong>30 minutes</strong>.</p>
        <p>If you did not initiate this request, please ignore this email.</p>
        <br>
        <p>Thank you,<br>MixCertificate Team</p>
      </div>
    `;

    await sendEmail({
      email: user.email,
      subject: "Your MixCertificate OTP",
      html: htmlMessage,
    });

    return res.status(200).json({ message: "OTP sent successfully" });
  } catch (error) {
    console.error("OTP Send Error:", error);
    return res.status(500).json({ message: "Failed to send OTP", error });
  }
};

exports.verifyOtp = async (req, res) => {
  try {
    const { apiKeyId, otp } = req.body;

    if (!apiKeyId || !otp) {
      return res
        .status(400)
        .json({ message: "API Key ID and OTP are required" });
    }

    const apiKey = await ApiKey.findById(apiKeyId);

    if (!apiKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    if (!apiKey.otpExpiresAt || new Date() >= apiKey.otpExpiresAt) {
      return res.status(400).json({ message: "OTP has expired" });
    }

    // Compare hashed OTP
    const hashedInputOtp = hashOtp(otp);
    if (apiKey.otp !== hashedInputOtp) {
      return res.status(400).json({ message: "Invalid OTP" });
    }

    apiKey.tobeShown = true;
    apiKey.toBeShownDefaultToTrueAgainAfterExpiry = new Date(
      Date.now() + 60 * 60 * 1000
    );
    apiKey.otp = null;
    apiKey.otpExpiresAt = null;
    await apiKey.save();

    return res
      .status(200)
      .json({ message: "OTP verified. API key access is now allowed." });
  } catch (error) {
    console.error("verifyOtp error:", error);
    return res
      .status(500)
      .json({ message: "Failed to verify OTP", error: error.message });
  }
};

exports.resetToBeShownApiKey = async (req, res) => {
  try {
    const now = new Date();

    const apiKeys = await ApiKey.find({
      businessId: req.user.businessId,
      createdBy: req.user._id,
      toBeShownDefaultToTrueAgainAfterExpiry: { $lte: now },
    });

    if (apiKeys.length > 0) {
      const updatePromises = apiKeys.map(async (key) => {
        key.tobeShown = false;
        key.toBeShownDefaultToTrueAgainAfterExpiry = null;
        await key.save();
      });

      await Promise.all(updatePromises);
    }

    return res.status(200).json({
      message: `Reset completed. ${apiKeys.length} API key(s) updated.`,
    });
  } catch (error) {
    console.error("resetToBeShownApiKey error:", error);
    return res.status(500).json({
      message: "Failed to reset tobeShown flag",
      error: error.message,
    });
  }
};
