const { v4: uuidv4 } = require("uuid");
const ApiKey = require("../models/ApiKey");
const { createLog } = require("../controllers/logController");
// Create new API key
exports.createApiKey = async (req, res) => {
  try {
    const { name, scopes, expiresAt } = req.body;

    const newKey = new ApiKey({
      name,
      key: uuidv4(),
      scopes,
      expiresAt,
      businessId: req.user.businessId,
      createdBy: req.user._id,
      status:
        expiresAt && new Date(expiresAt) <= new Date() ? "expired" : "active",
    });

    await newKey.save();
    await createLog(
      {
        eventType: "API Key",
        action: "Created",
        target: newKey._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.status(201).json(newKey);
  } catch (error) {
    res.status(500).json({ message: "Failed to create API key", error });
  }
};

// Get all API keys for the current user's business (with pagination)
exports.getApiKeys = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const { businessId, _id: userId } = req.user;

    // Automatically mark expired keys (optional)
    await ApiKey.updateMany(
      {
        businessId,
        createdBy: userId,
        expiresAt: { $lte: new Date() },
        status: "active",
      },
      { $set: { status: "expired" } }
    );

    const totalCount = await ApiKey.countDocuments({
      businessId,
      createdBy: userId,
    });

    const keys = await ApiKey.find({ businessId, createdBy: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    res.json({
      data: keys,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API keys", error });
  }
};

// Get single API key by ID (only if user owns it)
exports.getApiKeyById = async (req, res) => {
  try {
    const key = await ApiKey.findOne({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });

    if (!key) return res.status(404).json({ message: "API key not found" });
    res.json(key);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API key", error });
  }
};

// Update API key (only if user owns it)
exports.updateApiKey = async (req, res) => {
  try {
    const updatedKey = await ApiKey.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
        createdBy: req.user._id,
      },
      req.body,
      { new: true }
    );

    if (!updatedKey)
      return res.status(404).json({ message: "API key not found" });
    res.json(updatedKey);
  } catch (error) {
    res.status(500).json({ message: "Failed to update API key", error });
  }
};

// Delete API key (only if user owns it)
exports.deleteApiKey = async (req, res) => {
  try {
    const deleted = await ApiKey.findOneAndDelete({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });

    if (!deleted) return res.status(404).json({ message: "API key not found" });
    res.json({ message: "API key deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete API key", error });
  }
};

// Dashboard: Get stats (business-wide)
exports.getApiKeyStats = async (req, res) => {
  try {
    const businessId = req.user.businessId;
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    await ApiKey.updateMany(
      { businessId, expiresAt: { $lte: new Date() }, status: "active" },
      { $set: { status: "expired" } }
    );

    const [total, active, expired, revoked, recentlyUsed] = await Promise.all([
      ApiKey.countDocuments({ businessId }),
      ApiKey.countDocuments({ businessId, status: "active" }),
      ApiKey.countDocuments({ businessId, status: "expired" }),
      ApiKey.countDocuments({ businessId, status: "revoked" }),
      ApiKey.countDocuments({
        businessId,
        lastUsedAt: { $gte: sevenDaysAgo },
      }),
    ]);

    res.json({
      totalKeys: total,
      activeKeys: active,
      expiredKeys: expired,
      revokedKeys: revoked,
      recentlyUsed,
    });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch API key stats", error });
  }
};

// Admin: Revoke an API key
exports.revokeApiKey = async (req, res) => {
  try {
    const key = await ApiKey.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
      },
      { status: "revoked" },
      { new: true }
    );

    if (!key) return res.status(404).json({ message: "API key not found" });
    await createLog(
      {
        eventType: "API Key",
        action: "Revoked",
        target: key._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.json({ message: "API key revoked successfully", key });
  } catch (error) {
    res.status(500).json({ message: "Failed to revoke API key", error });
  }
};
