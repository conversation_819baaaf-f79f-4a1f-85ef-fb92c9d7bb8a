<%- contentFor('HeaderCss') %>
  <%-include("partials/title-meta", { "title" : "Add New Contact" }) %>
    <!-- Responsive Table css -->
    <link href="/assets/libs/admin-resources/rwd-table/rwd-table.min.css" rel="stylesheet" type="text/css" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <%- contentFor('body') %>

        <div class="">
          <div class="card">
            <div class="card-header">
              <h4 class="card-title">Add New Contact</h4>
              <p class="card-title-desc"><small>
                <!-- <a class="text" data-bs-toggle="modal" data-bs-target="#businessCardModal"> a Card</a>   -->
                <a href="/contact-import" >or Bulk Import</a>
              </p>
          </div>
            <div class="card-body">
              <form id="createContactForm" method="post" action="">
                <div class="row">
                  <div class="col-lg-6 mb-3">
                    <label for="full-name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="full-name" name="full-name" required autofocus>
                  </div>
                  <div class="col-lg-6 mb-3">
                    <label for="business-email" class="form-label">Business Email</label>
                    <input type="email" class="form-control" id="business-email" name="business-email" required>
                  </div>



                </div>


                <div id="moreFields" style="display: none;">
                  <div class="row">



                  <!-- extra fields from section 2 -->
                   <div class="col-lg-6 mb-3">
                    <label for="first-name" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="first-name" name="first-name" >
                  </div>
                  <div class="col-lg-6 mb-3">
                    <label for="last-name" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="last-name" name="last-name" >
                  </div>

                  <div class="col-lg-6 mb-3">
                    <label for="job-title" class="form-label">Job Title</label>
                    <input type="text" class="form-control" id="job-title" name="job-title" >
                  </div>
                  <div class="col-lg-6 mb-3">
                    <label for="company-name" class="form-label">Company Name</label>
                    <input type="text" class="form-control" id="company-name" name="company-name">
                  </div>

                  <div class="col-lg-6 mb-3">
                    <label for="phone-number" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="phone-number" name="phone-number" >
                  </div>


                  <div class="col-lg-6 mb-3">
                    <label for="company-size" class="form-label">Company Size</label>
                    <select class="form-control" id="company-size" name="company-size">
                      <option value="0-1">0-1 employees</option>
                      <option value="2-10">2-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-100 employees</option>
                      <option value="51-200">101-200 employees</option>
                      <option value="201-500">201-500 employees</option>
                      <option value="501-1000">501-1000 employees</option>
                      <option value="1001-5000">1001-5000 employees</option>
                      <option value="5001-10000">5001-10000 employees</option>
                      <option value="10000+">10000+ employees</option>
                    </select>
                  </div>


                  <div class="col-lg-6 mb-3">
                    <label for="service-interest" class="form-label">Service Interest</label>
                    <textarea class="form-control" id="service-interest" name="service-interest" rows="1"
                      ></textarea>
                  </div>

                  <!-- extra fields from section 1 -->



                    <div class="col-lg-6 mb-3">
                      <label for="industry" class="form-label">Industry</label>
                      <input type="text" class="form-control" id="industry" name="industry">
                    </div>


                    <!-- Add more fields here -->

                    <div class="col-lg-6 mb-3">
                      <label for="website" class="form-label">Website</label>
                      <input type="url" class="form-control" id="website" name="website" >
                    </div>
                    <div class="col-lg-6 mb-3">
                      <label for="lead-source" class="form-label">Lead Source</label>
                      <select class="form-control" id="lead-source" name="lead-source">
                        <option value="referral" default>Referral</option>
                        <option value="coldCall">Cold Call</option>
                        <option value="partner">Partner</option>
                        <option value="event">Event</option>
                        <option value="emailCampaign">Email Campaign</option>
                        <option value="advertisement">Advertisement</option>
                        <option value="socialMedia">Social Media</option>
                        <option value="website">Website</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div class="col-lg-6 mb-3">
                      <label for="contact-status" class="form-label">Contact Status</label>
                      <select class="form-control" id="contact-status" name="contact-status" >
                        <option value="New">New</option>
                <option value="Contacted">Contacted</option>
                <option value="Qualified">Qualified</option>
                <option value="Converted">Converted</option>
                <option value="InProgress">InProgress</option>
                <option value="Closed">Closed</option>
                <option value="Qualified">Qualified</option>
                      </select>
                    </div>

                    <div class="col-lg-6 mb-3">
                      <label for="company-revenue" class="form-label">Company Revenue</label>
                      <input type="text" class="form-control" id="company-revenue" name="company-revenue" >
                    </div>

                    <div class="col-lg-6 mb-3">
                      <label for="account-manager" class="form-label">Account Manager</label>
                      <input type="text" class="form-control" id="account-manager" name="account-manager" >
                    </div>

                    <div class="col-lg-6 mb-3">
                      <label for="company-address" class="form-label">Company Address</label>
                      <input type="text" class="form-control" id="company-address" name="company-address" >
                    </div>

                    <div class="col-lg-6 mb-3">
                      <label for="linkedin-profile" class="form-label">LinkedIn Profile</label>
                      <input type="url" class="form-control" id="linkedin-profile" name="linkedin-profile">
                    </div>
                    <div class="col-lg-6 mb-3">
                      <label for="tags" class="form-label">Tags</label>
                      <input type="text" value="crm" class="form-control" id="tags" name="tags">
                    </div>
                    <div class="col-lg-6 mb-3">
                      <label for="preferred-contact-method" class="form-label">Preferred Contact Method</label>
                      <select class="form-control" id="preferred-contact-method" name="preferred-contact-method"
                        >
                        <option value="Email">Email</option>
                        <option value="Phone">Phone</option>
                        <option value="SMS">SMS</option>
                        <option value="LinkedIn">LinkedIn</option>
                        <option value="WhatsApp">WhatsApp</option>
                      </select>
                    </div>


                    <div class="col-lg-6 mb-3">

                      <label for="marketing-opt-out" class="form-label">Marketing Opt-Out</label>

                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="opt-out-email" name="opt-out-email">
                        <label class="form-check-label" for="opt-out-email">Email</label>
                      </div>

                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="opt-out-sms" name="opt-out-sms">
                        <label class="form-check-label" for="opt-out-sms">SMS</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="opt-out-phone" name="opt-out-phone">
                        <label class="form-check-label" for="opt-out-phone">Phone</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="opt-out-linkedin" name="opt-out-linkedin">
                        <label class="form-check-label" for="opt-out-linkedin">LinkedIn</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="opt-out-whatsapp" name="opt-out-whatsapp">
                        <label class="form-check-label" for="opt-out-whatsapp">WhatsApp</label>
                      </div>
                    </div>




                    <div class="col-lg-6 mb-3">
                      <label for="notes" class="form-label mb-0">Notes</label>
                      <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                  </div>
                </div>
            </div>

            <!-- Variables Section - Moved outside of moreFields -->
            <div class=" mt-4 mb-4">
              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <div class="d-flex align-items-center mb-3">
                      <h5 class="card-title mb-0 me-3">Variables</h5>
                      <button
                        type="button"
                        id="addVariableBtn"
                        class="btn btn-sm me-2"
                        style="background-color: #5156be; color: #ffffff"
                      >
                        <i class="fas fa-plus me-1"></i> Add Variables
                      </button>
                    </div>
                    <div
                      id="variablesContainer"
                      class="border rounded p-4"
                      style="
                        min-height: 100px;
                        max-height: 200px;
                        overflow-y: auto;
                        width: 100%;
                        margin-left: 15px;
                      "
                    >
                      <table class="table table-bordered mb-0" id="variablesTable" style="display: none;">
                        <thead class="table-light">
                          <tr>
                            <th>Name</th>
                            <th>Value</th>
                            <th style="width: 80px;">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <!-- Variables will be added here dynamically -->
                        </tbody>
                      </table>
                      <div
                        class="text-muted text-center py-3"
                        id="noVariablesMessage"
                      >
                        No variables added yet
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <button type="button" id="moreButton" class="ms-1 btn btn-link mb-3">+ show more</button>

            <div class="p-3">
              <button type="submit" id="" class="btn btn-primary">Add New Contact</button>
              <button type="reset" class="btn btn-outline-secondary">Reset</button>
            </div>
            </form >
          </div>


           <!-- Upload contact Modal starts -->


    <!-- Upload Image or Capture Using Webcam Modal -->
    <!-- <div class="modal fade" id="businessCardModal" tabindex="-1" aria-labelledby="businessCardModalLabel" aria-hidden="true"> -->
        <!-- <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="businessCardModalLabel">Scan Business Card</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div id="step1">
                        <div class="text-center mb-3">
                            <button class="btn btn-primary" id="uploadButton">
                                <i class="fas fa-upload"></i> Upload File
                            </button>
                            <button class="btn btn-primary" id="scanButton">
                                <i class="fas fa-camera"></i> Scan from Camera
                            </button>
                        </div>
                        <input type="file" id="fileInput" class="d-none">
                        <div id="cameraInput" class="d-none">
                            <video id="video" width="100%" height="400"></video>
                            <button id="capture" class="btn btn-primary mt-2">Capture</button>
                            <canvas id="canvas" class="d-none"></canvas>
                        </div>
                    </div>


                    <div id="step2" class="d-none">
                        <div class="text-center">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                                    aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: 75%"></div>
                            </div>
                            <p class="mt-2">Processing...</p>
                        </div>
                    </div>


                    <div id="step3" class="d-none text-center">
                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                        <p class="mt-2">Success! Business card has been scanned.</p>
                        <button class="btn btn-primary" id="confirmButton">Okay</button>
                    </div>
                </div>
            </div>
        </div> -->
    <!-- </div> -->
<!-- Upload contact Modal ends -->
  </div>

<!-- Add Variable Modal -->
<div class="modal fade" id="variableModal" tabindex="-1" aria-labelledby="variableModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background-color: #5156be; color: #ffffff;">
        <h5 style="color: #ffffff" class="modal-title" id="variableModalLabel">Add Variable</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="variableForm">
          <div class="mb-3">
            <label for="variableName" class="form-label">Variable Name</label>
            <input type="text" class="form-control" id="variableName" required>
          </div>
          <div class="mb-3">
            <label for="variableValue" class="form-label">Value</label>
            <input type="text" class="form-control" id="variableValue" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn" id="saveVariableBtn" style="background-color: #5156be; color: #ffffff;">Save</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Variable Modal -->
<div class="modal fade" id="editVariableModal" tabindex="-1" aria-labelledby="editVariableModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background-color: #5156be; color: #ffffff;">
        <h5 style="color: #ffffff" class="modal-title" id="editVariableModalLabel">Edit Variable</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editVariableForm">
          <div class="mb-3">
            <label for="editVariableName" class="form-label">Variable Name</label>
            <input type="text" class="form-control" id="editVariableName" required>
            <input type="hidden" id="originalVariableName">
          </div>
          <div class="mb-3">
            <label for="editVariableValue" class="form-label">Value</label>
            <input type="text" class="form-control" id="editVariableValue" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn" id="updateVariableBtn" style="background-color: #5156be; color: #ffffff;">Update</button>
      </div>
    </div>
  </div>
</div>

        <script>
          //scrip to expand and hire contact section
          const moreButton = document.getElementById('moreButton');
          const moreFields = document.getElementById('moreFields');

          moreButton.addEventListener('click', function () {
            if (moreFields.style.display === 'none') {
              moreFields.style.display = 'block';
              moreButton.textContent = '- show less';
            } else {
              moreFields.style.display = 'none';
              moreButton.textContent = '+ show more';
            }
          });
        </script>


        <%- contentFor('FooterJs') %>

        <!-- Scan Business Card Modal Script starts -->
        <!-- <script>
          $(document).ready(function () {
              let capturedImage;

              // Show file input when upload button is clicked
              $("#uploadButton").click(function () {
                  $("#fileInput").click();
              });

              // Show camera input when scan button is clicked
              $("#scanButton").click(function () {
                  $("#cameraInput").removeClass("d-none");
                  startCamera();
              });

              // Handle file input change
              $("#fileInput").change(function () {
                  // Show progress bar
                  showStep(2);
                  // Send file to server
                  const file = this.files[0];
                  uploadFile(file);
              });

              // Handle capture button click
              $("#capture").click(function () {
                  captureImage();
                  // Show progress bar
                  showStep(2);
                  // Send captured image to server
                  uploadFile(capturedImage);
              });

              // Handle confirm button click
              $("#confirmButton").click(function () {
                  $("#businessCardModal").modal('hide');
              });

              // Show specific step in modal
              function showStep(step) {
                  $("#step1, #step2, #step3").addClass("d-none");
                  $("#step" + step).removeClass("d-none");
              }

              // Start camera
              function startCamera() {
                  navigator.mediaDevices.getUserMedia({ video: true })
                      .then(function (stream) {
                          var video = $("#video")[0];
                          video.srcObject = stream;
                          video.play();
                      })
                      .catch(function (err) {
                          console.log("An error occurred: " + err);
                      });
              }

              // Capture image from camera
              function captureImage() {
                  var canvas = $("#canvas")[0];
                  var video = $("#video")[0];
                  var context = canvas.getContext('2d');
                  context.drawImage(video, 0, 0, canvas.width, canvas.height);
                  capturedImage = dataURLToFile(canvas.toDataURL('image/png'), 'captured.png');
                  $("#canvas").removeClass("d-none");
              }

              // Convert dataURL to File object
              function dataURLToFile(dataurl, filename) {
                  var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
                      bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
                  while (n--) {
                      u8arr[n] = bstr.charCodeAt(n);
                  }
                  return new File([u8arr], filename, { type: mime });
              }

              // Upload file to server
              function uploadFile(file) {
                  var formData = new FormData();
                  formData.append('file', file);

                  $.ajax({
                      url: 'YOUR_SERVER_ENDPOINT', // Replace with your server endpoint
                      type: 'POST',
                      data: formData,
                      processData: false,
                      contentType: false,
                      success: function (response) {
                          // Simulate server processing time
                          setTimeout(function () {
                              showStep(3);
                              // Handle response (assuming JSON)
                              handleResponse(response);
                          }, 2000);
                      },
                      error: function (xhr, status, error) {
                          showToast('Upload failed: ' + error,"danger");
                          showStep(1);
                      }
                  });
              }

              // Handle server response
              function handleResponse(response) {
                  // Assuming response is JSON with the required fields
                  var data = JSON.parse(response);

                  // Show data in alert
                  //alert(JSON.stringify(data, null, 2));

                  // Set form fields
                  $("#full-name").val(data.fullName);
                  $("#first-name").val(data.firstName);
                  $("#last-name").val(data.lastName);
                  $("#business-email").val(data.businessEmail);
                  $("#phone-number").val(data.phoneNumber);
                  $("#company-name").val(data.companyName);
                  $("#company-size").val(data.companySize);
                  $("#job-title").val(data.jobTitle);
                  $("#website").val(data.website);
                  $("#lead-source").val(data.leadSource);
                  $("#industry").val(data.industry);
              }
          });
        </script> -->
        <!-- Scan Business Card Modal Script ends -->


        <!-- Responsive Table js -->
        <script src="/assets/libs/admin-resources/rwd-table/rwd-table.min.js"></script>
        <!-- Init js -->
        <script src="/assets/js/pages/table-responsive.init.js"></script>


<!-- Create New Contact starts -->
<script>
  $(document).ready(function() {
    // Variables management
    let variables = {};

    // Open variable modal
    $('#addVariableBtn').on('click', function() {
      // Clear the form
      $('#variableForm')[0].reset();
      // Show the modal
      $('#variableModal').modal('show');
    });

    // Save variable
    $('#saveVariableBtn').on('click', function() {
      const name = $('#variableName').val().trim();
      const value = $('#variableValue').val().trim();

      if (!name || !value) {
        showToast('Please enter both variable name and value','danger');
        return;
      }

      // Add to variables object
      variables[name] = value;

      // Update UI
      renderVariables();

      // Close modal
      $('#variableModal').modal('hide');
    });

    // Update variable
    $('#updateVariableBtn').on('click', function() {
      const originalName = $('#originalVariableName').val().trim();
      const newName = $('#editVariableName').val().trim();
      const value = $('#editVariableValue').val().trim();

      if (!newName || !value) {
        showToast('Please enter both variable name and value','danger');
        return;
      }

      // Check if the name has changed and if the new name already exists
      if (newName !== originalName && variables.hasOwnProperty(newName)) {
        showToast('A variable with this name already exists','danger');
        return;
      }

      // If name has changed, delete the old one and add with new name
      if (newName !== originalName) {
        // Get the value
        const oldValue = variables[originalName];
        // Delete the old entry
        delete variables[originalName];
        // Add with new name
        variables[newName] = value;

        showToast('Variable name and value updated successfully','primary');
      } else {
        // Just update the value
        variables[newName] = value;
        showToast('Variable value updated successfully','primary');
      }

      // Update UI
      renderVariables();

      // Close modal
      $('#editVariableModal').modal('hide');
    });

    // Render variables in the UI
    function renderVariables() {
      const $container = $('#variablesContainer');
      const $table = $('#variablesTable');
      const $tableBody = $('#variablesTable tbody');
      const $noVariablesMessage = $('#noVariablesMessage');

      // Clear container and table body
      $container.find('#variablesTable tbody').empty();

      console.log("Rendering variables:", Object.keys(variables).length);

      // Check if there are any variables
      if (Object.keys(variables).length === 0) {
        $table.hide();
        $noVariablesMessage.show();
        return;
      }

      // Hide no variables message and show table
      $noVariablesMessage.hide();
      $table.show();

      // Add each variable to the table
      for (const [name, value] of Object.entries(variables)) {
        const row = `
          <tr>
            <td>${name}</td>
            <td>${value}</td>
            <td class="text-center">
              <button type="button" class="btn btn-sm edit-variable p-1 me-1" data-name="${name}" data-value="${value}" style="background-color: white; border: none;">
                <i class="fas fa-edit" style="color: #5156be;"></i>
              </button>
              <button type="button" class="btn btn-sm delete-variable p-1" data-name="${name}" style="background-color: white; border: none;">
                <i class="fas fa-trash" style="color: red;"></i>
              </button>
            </td>
          </tr>
        `;
        $tableBody.append(row);
      }

      // Add edit event handlers
      $('.edit-variable').on('click', function() {
        const name = $(this).data('name');
        const value = $(this).data('value');

        // Populate the edit form
        $('#editVariableName').val(name);
        $('#originalVariableName').val(name); // Store original name for reference
        $('#editVariableValue').val(value);

        // Show the edit modal
        $('#editVariableModal').modal('show');
      });

      // Add delete event handlers
      $('.delete-variable').on('click', function() {
        const name = $(this).data('name');
        delete variables[name];
        renderVariables();
      });
    }

    // Form submission
    $('#createContactForm').on('submit', function(event) {
      event.preventDefault();
      var contactData = {
        creatorId: $('#creatorId').val(),
        fullName: $('#full-name').val(),
        firstName: $('#first-name').val(),
        lastName: $('#last-name').val(),
        jobTitle: $('#job-title').val(),
        companyName: $('#company-name').val(),
        businessEmail: $('#business-email').val(),
        phoneNumber: $('#phone-number').val(),
        companySize: $('#company-size').val(),
        serviceInterest: $('#service-interest').val(),
        industry: $('#industry').val(),
        website: $('#website').val(),
        leadSource: $('#lead-source').val(),
        contactStatus: $('#contact-status').val(),
        companyRevenue: $('#company-revenue').val(),
        accountManager: $('#account-manager').val(),
        companyAddress: $('#company-address').val(),
        linkedinProfile: $('#linkedin-profile').val(),
        tags: $('#tags').val(),
        communication: {
          email: $('#opt-out-sms').is(':checked'),
          sms: $('#opt-out-sms').is(':checked'),
          phone: $('#opt-out-phone').is(':checked'),
          linkedIn: $('#opt-out-linkedin').is(':checked'),
          whatsApp: $('#opt-out-whatsapp').is(':checked')
        },
        preferredContactMethod: $('#preferred-contact-method').val(),
        notes: $('#notes').val(),
        variables
      };
      console.log(contactData,'datacontact')

    $.ajax({
      url: '/api/contacts',
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify(contactData),
      success: function(response) {

        showToast("Contact Created successfully")
        $('#createContactForm')[0].reset();
        variables = {};
        renderVariables();

      },
      error: function(error) {
     const errorMessage = error.responseJSON.error ||
      error.responseText ?
      JSON.parse(error.responseText).error :
      'Failed to create contact.';

      console.error('Error:', error);
      showToast(errorMessage,"danger")

      }
    });
  });
});

</script>
<!-- Create New Contact ends -->