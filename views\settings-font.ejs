<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Custom Fonts" }) %>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css">

<%- contentFor('body') %> 
<%-include("partials/page-title", {"title":"Custom Fonts", "pagetitle": "Custom Fonts" }) %>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-font me-2"></i>
                    Upload Custom Fonts
                </h4>
                <p class="text-muted mb-0">Upload individual font files or ZIP archives with multiple variants</p>

                <!-- Upload Type Tabs -->
                <ul class="nav nav-tabs nav-tabs-custom mt-3" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#single-upload" role="tab">
                            <i class="bx bx-file me-1"></i>
                            Single Font
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#zip-upload" role="tab">
                            <i class="bx bx-archive me-1"></i>
                            ZIP Archive
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Single Font Upload Tab -->
                    <div class="tab-pane fade show active" id="single-upload" role="tabpanel">
                        <div id="font-dropzone" class="dropzone-custom">
                            <div class="dz-message">
                                <div class="upload-icon mb-3">
                                    <i class="bx bx-upload fs-1 text-primary"></i>
                                </div>
                                <h5>Drop font files here or click to upload</h5>
                                <p class="text-muted">Supports TTF, OTF, WOFF, WOFF2 files (Max 10MB)</p>
                            </div>
                        </div>
                    </div>

                    <!-- ZIP Upload Tab -->
                    <div class="tab-pane fade" id="zip-upload" role="tabpanel">
                        <div class="zip-upload-area">
                            <form id="zip-upload-form" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="fontName" class="form-label">Font Family Name</label>
                                    <input type="text" class="form-control" id="fontName" name="fontName"
                                           placeholder="e.g., Roboto, Open Sans" required>
                                    <div class="form-text">This will be the CSS font-family name</div>
                                </div>

                                <div class="mb-3">
                                    <label for="zipFile" class="form-label">ZIP File</label>
                                    <div class="zip-dropzone border-2 border-dashed border-primary rounded p-4 text-center">
                                        <i class="bx bx-archive fs-1 text-primary mb-3"></i>
                                        <h5>Drop ZIP file here or click to browse</h5>
                                        <p class="text-muted mb-3">ZIP should contain multiple font variants (Regular, Bold, Italic, etc.)</p>
                                        <input type="file" id="zipFile" name="zipFile" accept=".zip" class="d-none">
                                        <button type="button" class="btn btn-primary" onclick="document.getElementById('zipFile').click()">
                                            Choose ZIP File
                                        </button>
                                    </div>
                                    <div id="zip-file-info" class="mt-3 d-none">
                                        <div class="alert alert-info">
                                            <i class="bx bx-info-circle me-2"></i>
                                            <span id="zip-file-name"></span>
                                            <button type="button" class="btn btn-sm btn-outline-danger ms-2" id="remove-zip-file">
                                                <i class="bx bx-x"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-success" id="upload-zip-btn" disabled>
                                    <span id="zip-upload-spinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                                    <i class="bx bx-upload me-1"></i>
                                    Upload ZIP
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div id="upload-progress" class="mt-3 d-none">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Uploading...</span>
                        <span id="upload-percentage" class="text-sm">0%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <div id="upload-status" class="mt-3 d-none">
                    <div class="alert alert-success d-flex align-items-center" role="alert">
                        <i class="bx bx-check-circle me-2"></i>
                        <span>Font uploaded successfully!</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-library me-2"></i>
                    Your Custom Fonts
                </h4>
            </div>
            <div class="card-body">
                <div id="fonts-container">
                    <div class="text-center py-5">
                        <i class="bx bx-font fs-1 text-muted mb-3"></i>
                        <p class="text-muted">No custom fonts uploaded yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-show me-2"></i>
                    Font Preview
                </h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Preview Text</label>
                    <input type="text" id="preview-text" class="form-control" value="The quick brown fox jumps over the lazy dog">
                </div>
                <div class="mb-3">
                    <label class="form-label">Font Size</label>
                    <input type="range" id="font-size" class="form-range" min="12" max="72" value="24">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">12px</small>
                        <small class="text-muted">72px</small>
                    </div>
                </div>
                <div class="preview-area p-3 border rounded">
                    <div id="font-preview-text" style="font-size: 24px; line-height: 1.4;">
                        The quick brown fox jumps over the lazy dog
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-stats me-2"></i>
                    Usage Statistics
                </h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 id="total-fonts" class="text-primary mb-1">0</h3>
                            <p class="text-muted small mb-0">Total Fonts</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 id="total-size" class="text-success mb-1">0 MB</h3>
                        <p class="text-muted small mb-0">Total Size</p>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>

<template id="font-item-template">
    <div class="font-item border rounded p-3 mb-3 cursor-pointer" data-font-id="">
        <div class="d-flex align-items-start justify-content-between">
            <div class="flex-grow-1">
                <div class="d-flex align-items-center mb-2">
                    <div class="font-icon me-3">
                        <i class="bx bx-font fs-3 text-primary"></i>
                    </div>
                    <div>
                        <h6 class="font-name mb-1">Font Name</h6>
                        <div class="d-flex align-items-center">
                            <span class="upload-type badge bg-info me-2">Single</span>
                            <span class="variants-count text-muted small"></span>
                            <span class="text-muted small ms-2">• Uploaded on <span class="upload-date"></span></span>
                        </div>
                    </div>
                </div>

                <!-- Direct Font Preview -->
                <div class="font-preview-direct mt-3 p-3 bg-light rounded">
                    <div class="preview-text" style="font-size: 18px; line-height: 1.4; font-family: Arial;">
                        The quick brown fox jumps over the lazy dog
                    </div>
                    <div class="preview-sizes mt-2">
                        <div class="preview-text-small" style="font-size: 14px; font-family: Arial; color: #666;">
                            ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 1234567890
                        </div>
                    </div>
                </div>
            </div>

            <div class="font-actions ms-3">
                <button class="btn btn-sm btn-outline-primary details-btn me-2" title="View Details">
                    <i class="bx bx-detail"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger delete-btn" title="Delete Font">
                    <i class="bx bx-trash"></i>
                </button>
            </div>
        </div>
    </div>
</template>

<!-- Font Details Modal -->
<div class="modal fade" id="fontDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bx bx-font me-2"></i>
                    <span id="modal-font-name">Font Details</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Font Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Font Family:</strong></td>
                                <td id="modal-font-family"></td>
                            </tr>
                            <tr>
                                <td><strong>Upload Type:</strong></td>
                                <td id="modal-upload-type"></td>
                            </tr>
                            <tr>
                                <td><strong>Variants:</strong></td>
                                <td id="modal-variants-count"></td>
                            </tr>
                            <tr>
                                <td><strong>Uploaded:</strong></td>
                                <td id="modal-upload-date"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Preview Controls</h6>
                        <div class="mb-3">
                            <label class="form-label">Preview Text</label>
                            <input type="text" id="modal-preview-text" class="form-control"
                                   value="The quick brown fox jumps over the lazy dog">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Font Size: <span id="modal-font-size-value">24px</span></label>
                            <input type="range" id="modal-font-size" class="form-range" min="12" max="72" value="24">
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Font Variants</h6>
                    <div id="font-variants-container">
                        <!-- Font variants will be populated here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<%- contentFor('FooterJs') %>
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"
  integrity="sha512-U2WE1ktpMTuRBPoCFDzomoIorbOyUv0sP8B+INA3EzNAhehbzED1rOJg6bCqPf/Tuposxb5ja/MAUnC8THSbLQ=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<script>
$(document).ready(function() {
    let fonts = [];
    let currentPreviewFont = null;

    // Initialize ZIP upload functionality
    initializeZipUpload();

    const dropzone = new Dropzone("#font-dropzone", {
        url: "/api/files/upload/",
        maxFilesize: 10,
        acceptedFiles: ".ttf,.otf,.woff,.woff2",
        addRemoveLinks: true,
        dictDefaultMessage: "",
        dictFileTooBig: "File is too big ({{filesize}}MB). Max filesize: {{maxFilesize}}MB.",
        dictInvalidFileType: "You can't upload files of this type.",
        dictResponseError: "Server responded with {{statusCode}} code.",
        dictCancelUpload: "Cancel",
        dictUploadCanceled: "Upload canceled.",
        dictCancelUploadConfirmation: "Are you sure you want to cancel this upload?",
        dictRemoveFile: "Remove",
        dictMaxFilesExceeded: "You can not upload any more files.",
        
        init: function() {
            this.on("addedfile", function(file) {
                showUploadProgress();
            });
            
            this.on("uploadprogress", function(file, progress, bytesSent) {
                updateUploadProgress(progress);
            });
            
            this.on("success", function(file, response) {
                hideUploadProgress();
                showUploadSuccess();
                
                const fontName = file.name.replace(/\.[^/.]+$/, "");
                console.log(response,fontName)
                saveFontToDatabase(fontName, response.file.url);
                this.removeFile(file);
            });
            
            this.on("error", function(file, errorMessage) {
                hideUploadProgress();
                showUploadError(errorMessage);
                this.removeFile(file);
            });
        }
    });

    function showUploadProgress() {
        $("#upload-progress").removeClass("d-none");
        $("#upload-status").addClass("d-none");
    }

    function updateUploadProgress(progress) {
        $("#upload-percentage").text(Math.round(progress) + "%");
        $("#upload-progress .progress-bar").css("width", progress + "%");
    }

    function hideUploadProgress() {
        setTimeout(function() {
            $("#upload-progress").addClass("d-none");
        }, 1000);
    }

    function showUploadSuccess() {
        $("#upload-status .alert").removeClass("alert-danger").addClass("alert-success");
        $("#upload-status .alert i").removeClass("bx-x-circle").addClass("bx-check-circle");
        $("#upload-status .alert span").text("Font uploaded successfully!");
        $("#upload-status").removeClass("d-none");
        
        setTimeout(function() {
            $("#upload-status").addClass("d-none");
        }, 3000);
        
        loadFonts();
    }

    function showUploadError(error) {
        $("#upload-status .alert").removeClass("alert-success").addClass("alert-danger");
        $("#upload-status .alert i").removeClass("bx-check-circle").addClass("bx-x-circle");
        $("#upload-status .alert span").text("Upload failed: " + error);
        $("#upload-status").removeClass("d-none");
        
        setTimeout(function() {
            $("#upload-status").addClass("d-none");
        }, 5000);
    }

    function saveFontToDatabase(name, fileUrl) {
        $.ajax({
            url: "/api/custom-fonts",
            type: "POST",
            data: {
                name: name,
                fileUrl: fileUrl
            },
            success: function(response) {
                console.log("Font saved to database:", response);
                loadFonts();
            },
            error: function(xhr, status, error) {
                console.error("Failed to save font:", error);
                showUploadError("Failed to save font to database");
            }
        });
    }

    function loadFonts() {
        $.ajax({
            url: "/api/custom-fonts",
            type: "GET",
            success: function(response) {
              console.log(response,'response')
                fonts = response;
                renderFonts();
                // updateStats();
            },
            error: function(xhr, status, error) {
                console.error("Failed to load fonts:", error);
            }
        });
    }

    function renderFonts() {
        const container = $("#fonts-container");
        
        if (fonts.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="bx bx-font fs-1 text-muted mb-3"></i>
                    <p class="text-muted">No custom fonts uploaded yet</p>
                </div>
            `);
            return;
        }

        container.empty();
        
        fonts.forEach(font => {
            const template = document.getElementById("font-item-template");
            const clone = template.content.cloneNode(true);
            
            $(clone).find(".font-name").text(font.name);
            $(clone).find(".upload-date").text(new Date(font.createdAt).toLocaleDateString());
            
            $(clone).find(".preview-btn").click(function() {
                toggleFontPreview(font, $(this).closest(".font-item"));
            });
            
            $(clone).find(".delete-btn").click(function() {
                deleteFont(font._id, $(this).closest(".font-item"));
            });
            
            container.append(clone);
        });
    }

    function toggleFontPreview(font, item) {
        const previewArea = item.find(".font-preview");
        const previewText = item.find(".preview-text");
        
        if (previewArea.hasClass("d-none")) {
            loadFontForPreview(font.fileUrl, font.name);
            previewArea.removeClass("d-none");
            item.find(".preview-btn").html('<i class="bx bx-hide"></i> Hide');
        } else {
            previewArea.addClass("d-none");
            item.find(".preview-btn").html('<i class="bx bx-show"></i> Preview');
        }
    }

    function loadFontForPreview(fileUrl, fontName) {
        const fontFace = new FontFace(fontName, `url(${fileUrl})`);
        
        fontFace.load().then(function(loadedFace) {
            document.fonts.add(loadedFace);
            $(".font-preview .preview-text").css("font-family", fontName);
            $("#font-preview-text").css("font-family", fontName);
            currentPreviewFont = fontName;
        }).catch(function(error) {
            console.error("Failed to load font:", error);
        });
    }

    function deleteFont(fontId, item) {
        if (confirm("Are you sure you want to delete this font?")) {
            $.ajax({
                url: `/api/custom-fonts/${fontId}`,
                type: "DELETE",
                success: function(response) {
                    item.fadeOut(300, function() {
                        $(this).remove();
                        loadFonts();
                    });
                },
                error: function(xhr, status, error) {
                    console.error("Failed to delete font:", error);
                    alert("Failed to delete font");
                }
            });
        }
    }

    // function updateStats() {
    //     $("#total-fonts").text(fonts.length);
    //     $("#total-size").text("0 MB");
    // }

    $("#preview-text").on("input", function() {
        const text = $(this).val();
        $("#font-preview-text").text(text);
        $(".font-preview .preview-text").text(text);
    });

    $("#font-size").on("input", function() {
        const size = $(this).val();
        $("#font-preview-text").css("font-size", size + "px");
        $(".font-preview .preview-text").css("font-size", size + "px");
    });

    loadFonts();

    // Initialize ZIP upload functionality
    function initializeZipUpload() {
        const zipDropzone = document.querySelector('.zip-dropzone');
        const zipFileInput = document.getElementById('zipFile');
        const zipForm = document.getElementById('zip-upload-form');
        const zipFileInfo = document.getElementById('zip-file-info');
        const zipFileName = document.getElementById('zip-file-name');
        const removeZipBtn = document.getElementById('remove-zip-file');
        const uploadZipBtn = document.getElementById('upload-zip-btn');

        // Drag and drop functionality
        zipDropzone.addEventListener('dragover', function(e) {
            e.preventDefault();
            zipDropzone.classList.add('dragover');
        });

        zipDropzone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            zipDropzone.classList.remove('dragover');
        });

        zipDropzone.addEventListener('drop', function(e) {
            e.preventDefault();
            zipDropzone.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].name.toLowerCase().endsWith('.zip')) {
                zipFileInput.files = files;
                handleZipFileSelection(files[0]);
            } else {
                showToast('Please select a ZIP file', 'warning');
            }
        });

        // File input change
        zipFileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleZipFileSelection(e.target.files[0]);
            }
        });

        // Remove file button
        removeZipBtn.addEventListener('click', function() {
            zipFileInput.value = '';
            zipFileInfo.classList.add('d-none');
            uploadZipBtn.disabled = true;
        });

        // Form submission
        zipForm.addEventListener('submit', function(e) {
            e.preventDefault();
            uploadZipFile();
        });

        function handleZipFileSelection(file) {
            if (!file.name.toLowerCase().endsWith('.zip')) {
                showToast('Please select a ZIP file', 'warning');
                return;
            }

            zipFileName.textContent = file.name;
            zipFileInfo.classList.remove('d-none');
            uploadZipBtn.disabled = false;
        }

        function uploadZipFile() {
            const formData = new FormData(zipForm);
            const spinner = document.getElementById('zip-upload-spinner');

            uploadZipBtn.disabled = true;
            spinner.classList.remove('d-none');

            $.ajax({
                url: '/api/custom-fonts/upload-zip',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    showToast(`Font uploaded successfully! ${response.variantsCount} variants found.`, 'success');
                    zipForm.reset();
                    zipFileInfo.classList.add('d-none');
                    loadFonts(); // Reload font list
                },
                error: function(xhr) {
                    const error = xhr.responseJSON?.message || 'Upload failed';
                    showToast(error, 'error');
                },
                complete: function() {
                    uploadZipBtn.disabled = false;
                    spinner.classList.add('d-none');
                }
            });
        }
    }

    // Enhanced font loading with preview support
    function loadFonts() {
        $.get("/api/custom-fonts", function(data) {
            fonts = data.fonts || [];
            displayFonts(fonts);
        }).fail(function() {
            showToast("Failed to load fonts", "error");
        });
    }

    function displayFonts(fonts) {
        const container = $("#font-list");

        if (fonts.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="bx bx-font fs-1 text-muted mb-3"></i>
                    <p class="text-muted">No custom fonts uploaded yet</p>
                </div>
            `);
            return;
        }

        container.empty();

        fonts.forEach(font => {
            const template = document.getElementById("font-item-template");
            const clone = template.content.cloneNode(true);

            // Set font data
            $(clone).find(".font-item").attr("data-font-id", font._id);
            $(clone).find(".font-name").text(font.name);
            $(clone).find(".upload-date").text(new Date(font.createdAt).toLocaleDateString());

            // Set upload type and variants info
            const uploadTypeBadge = $(clone).find(".upload-type");
            const variantsCount = $(clone).find(".variants-count");

            if (font.uploadType === 'zip') {
                uploadTypeBadge.text('ZIP').removeClass('bg-info').addClass('bg-success');
                variantsCount.text(`${font.variants?.length || 0} variants`);
            } else {
                uploadTypeBadge.text('Single').removeClass('bg-success').addClass('bg-info');
                variantsCount.text('1 file');
            }

            // Load and apply font for preview
            loadFontForPreview(font, clone);

            // Add event listeners
            $(clone).find(".details-btn").on("click", function(e) {
                e.stopPropagation();
                showFontDetails(font);
            });

            $(clone).find(".delete-btn").on("click", function(e) {
                e.stopPropagation();
                deleteFont(font._id);
            });

            // Click on font item to show details
            $(clone).find(".font-item").on("click", function() {
                showFontDetails(font);
            });

            container.append(clone);
        });
    }

    function loadFontForPreview(font, clone) {
        let fontUrl = '';
        let fontFamily = font.fontFamily || font.name;

        if (font.uploadType === 'zip' && font.variants && font.variants.length > 0) {
            // Use the first variant (usually Regular)
            const regularVariant = font.variants.find(v => v.variant === 'Regular') || font.variants[0];
            fontUrl = regularVariant.fileUrl;
        } else if (font.fileUrl) {
            fontUrl = font.fileUrl;
        }

        if (fontUrl) {
            // Create unique font family name
            const uniqueFontFamily = `custom-font-${font._id}`;

            // Load font
            const fontFace = new FontFace(uniqueFontFamily, `url(${fontUrl})`);
            fontFace.load().then(function(loadedFont) {
                document.fonts.add(loadedFont);

                // Apply font to preview elements
                $(clone).find(".preview-text").css("font-family", uniqueFontFamily);
                $(clone).find(".preview-text-small").css("font-family", uniqueFontFamily);
            }).catch(function(error) {
                console.error('Font loading failed:', error);
                // Keep default font if loading fails
            });
        }
    }

    function showFontDetails(font) {
        // Populate modal with font details
        $("#modal-font-name").text(font.name);
        $("#modal-font-family").text(font.fontFamily || font.name);
        $("#modal-upload-type").text(font.uploadType === 'zip' ? 'ZIP Archive' : 'Single File');
        $("#modal-variants-count").text(font.variants?.length || 1);
        $("#modal-upload-date").text(new Date(font.createdAt).toLocaleDateString());

        // Show font variants
        const variantsContainer = $("#font-variants-container");
        variantsContainer.empty();

        if (font.uploadType === 'zip' && font.variants) {
            font.variants.forEach(variant => {
                const variantHtml = `
                    <div class="font-variant-preview" data-variant="${variant.variant}">
                        <div class="variant-name">${variant.variant}</div>
                        <div class="variant-preview-text" style="font-family: custom-font-${font._id}-${variant.variant}; font-size: 24px;">
                            The quick brown fox jumps over the lazy dog
                        </div>
                        <div class="variant-info">
                            Weight: ${variant.fontWeight || 'normal'} | Style: ${variant.fontStyle || 'normal'} |
                            Size: ${(variant.fileSize / 1024).toFixed(1)}KB
                        </div>
                    </div>
                `;
                variantsContainer.append(variantHtml);

                // Load variant font
                const uniqueFontFamily = `custom-font-${font._id}-${variant.variant}`;
                const fontFace = new FontFace(uniqueFontFamily, `url(${variant.fileUrl})`);
                fontFace.load().then(function(loadedFont) {
                    document.fonts.add(loadedFont);
                }).catch(function(error) {
                    console.error('Variant font loading failed:', error);
                });
            });
        } else {
            // Single font
            const variantHtml = `
                <div class="font-variant-preview">
                    <div class="variant-name">Regular</div>
                    <div class="variant-preview-text" style="font-family: custom-font-${font._id}; font-size: 24px;">
                        The quick brown fox jumps over the lazy dog
                    </div>
                    <div class="variant-info">Single font file</div>
                </div>
            `;
            variantsContainer.append(variantHtml);
        }

        // Show modal
        $("#fontDetailsModal").modal("show");
    }

    // Modal preview controls
    $("#modal-preview-text").on("input", function() {
        const text = $(this).val();
        $("#font-variants-container .variant-preview-text").text(text);
    });

    $("#modal-font-size").on("input", function() {
        const size = $(this).val();
        $("#modal-font-size-value").text(size + "px");
        $("#font-variants-container .variant-preview-text").css("font-size", size + "px");
    });
});
</script>

<style>
.dropzone-custom {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dropzone-custom:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.dropzone-custom.dz-drag-hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-icon {
    color: #007bff;
}

.font-item {
    transition: all 0.3s ease;
}

.font-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.font-preview {
    background: #f8f9fa;
    border-radius: 4px;
}

.preview-area {
    background: #f8f9fa;
    min-height: 100px;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card {
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.08);
    border-radius: 12px;
}

.card-header {
    background: transparent;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.form-control, .form-range {
    border-radius: 8px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* ZIP Upload Styles */
.zip-dropzone {
    transition: all 0.3s ease;
    cursor: pointer;
}

.zip-dropzone:hover {
    background-color: #f8f9fa;
    border-color: #5156be !important;
}

.zip-dropzone.dragover {
    border-color: #28a745 !important;
    background-color: #d4edda;
}

/* Font Item Styles */
.font-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.font-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.font-preview-direct {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.preview-text {
    word-break: break-all;
    overflow-wrap: break-word;
}

.upload-type.badge {
    font-size: 0.7rem;
}

/* Font Variant Preview Styles */
.font-variant-preview {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    transition: all 0.3s ease;
}

.font-variant-preview:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.variant-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.variant-preview-text {
    line-height: 1.4;
    margin-bottom: 8px;
}

.variant-info {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Tab Styles */
.nav-tabs-custom .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
}

.nav-tabs-custom .nav-link.active {
    border-bottom-color: #5156be;
    color: #5156be;
    background: none;
}

.nav-tabs-custom .nav-link:hover {
    border-bottom-color: #5156be;
    color: #5156be;
}

/* Modal Styles */
.modal-lg {
    max-width: 900px;
}

#font-variants-container .font-variant-preview {
    background: #f8f9fa;
}

/* Responsive */
@media (max-width: 768px) {
    .font-item .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .font-actions {
        margin-top: 10px;
        margin-left: 0 !important;
    }

    .preview-text {
        font-size: 16px !important;
    }
}
</style>