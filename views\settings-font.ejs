<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Custom Fonts" }) %>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css">

<%- contentFor('body') %> 
<%-include("partials/page-title", {"title":"Custom Fonts", "pagetitle": "Custom Fonts" }) %>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-font me-2"></i>
                    Upload Custom Fonts
                </h4>
                <p class="text-muted mb-0">Upload your custom font files (TTF, OTF, WOFF, WOFF2)</p>
            </div>
            <div class="card-body">
                <div id="font-dropzone" class="dropzone-custom">
                    <div class="dz-message">
                        <div class="upload-icon mb-3">
                            <i class="bx bx-upload fs-1 text-primary"></i>
                        </div>
                        <h5>Drop font files here or click to upload</h5>
                        <p class="text-muted">Supports TTF, OTF, WOFF, WOFF2 files (Max 10MB)</p>
                    </div>
                </div>

                <div id="upload-progress" class="mt-3 d-none">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Uploading...</span>
                        <span id="upload-percentage" class="text-sm">0%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <div id="upload-status" class="mt-3 d-none">
                    <div class="alert alert-success d-flex align-items-center" role="alert">
                        <i class="bx bx-check-circle me-2"></i>
                        <span>Font uploaded successfully!</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-library me-2"></i>
                    Your Custom Fonts
                </h4>
            </div>
            <div class="card-body">
                <div id="fonts-container">
                    <div class="text-center py-5">
                        <i class="bx bx-font fs-1 text-muted mb-3"></i>
                        <p class="text-muted">No custom fonts uploaded yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-show me-2"></i>
                    Font Preview
                </h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Preview Text</label>
                    <input type="text" id="preview-text" class="form-control" value="The quick brown fox jumps over the lazy dog">
                </div>
                <div class="mb-3">
                    <label class="form-label">Font Size</label>
                    <input type="range" id="font-size" class="form-range" min="12" max="72" value="24">
                    <div class="d-flex justify-content-between">
                        <small class="text-muted">12px</small>
                        <small class="text-muted">72px</small>
                    </div>
                </div>
                <div class="preview-area p-3 border rounded">
                    <div id="font-preview-text" style="font-size: 24px; line-height: 1.4;">
                        The quick brown fox jumps over the lazy dog
                    </div>
                </div>
            </div>
        </div>

        <!-- <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bx bx-stats me-2"></i>
                    Usage Statistics
                </h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 id="total-fonts" class="text-primary mb-1">0</h3>
                            <p class="text-muted small mb-0">Total Fonts</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 id="total-size" class="text-success mb-1">0 MB</h3>
                        <p class="text-muted small mb-0">Total Size</p>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>

<template id="font-item-template">
    <div class="font-item border rounded p-3 mb-3">
        <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <div class="font-icon me-3">
                    <i class="bx bx-font fs-3 text-primary"></i>
                </div>
                <div>
                    <h6 class="font-name mb-1">Font Name</h6>
                    <p class="text-muted small mb-0">Uploaded on <span class="upload-date"></span></p>
                </div>
            </div>
            <div class="font-actions">
                <button class="btn btn-sm btn-outline-primary preview-btn me-2">
                    <i class="bx bx-show"></i> Preview
                </button>
                <button class="btn btn-sm btn-outline-danger delete-btn">
                    <i class="bx bx-trash"></i>
                </button>
            </div>
        </div>
        <div class="font-preview mt-3 d-none">
            <div class="preview-text" style="font-size: 18px; line-height: 1.4;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
    </div>
</template>

<%- contentFor('FooterJs') %>
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"
  integrity="sha512-U2WE1ktpMTuRBPoCFDzomoIorbOyUv0sP8B+INA3EzNAhehbzED1rOJg6bCqPf/Tuposxb5ja/MAUnC8THSbLQ=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<script>
$(document).ready(function() {
    let fonts = [];
    let currentPreviewFont = null;

    const dropzone = new Dropzone("#font-dropzone", {
        url: "/api/files/upload/",
        maxFilesize: 10,
        acceptedFiles: ".ttf,.otf,.woff,.woff2",
        addRemoveLinks: true,
        dictDefaultMessage: "",
        dictFileTooBig: "File is too big ({{filesize}}MB). Max filesize: {{maxFilesize}}MB.",
        dictInvalidFileType: "You can't upload files of this type.",
        dictResponseError: "Server responded with {{statusCode}} code.",
        dictCancelUpload: "Cancel",
        dictUploadCanceled: "Upload canceled.",
        dictCancelUploadConfirmation: "Are you sure you want to cancel this upload?",
        dictRemoveFile: "Remove",
        dictMaxFilesExceeded: "You can not upload any more files.",
        
        init: function() {
            this.on("addedfile", function(file) {
                showUploadProgress();
            });
            
            this.on("uploadprogress", function(file, progress, bytesSent) {
                updateUploadProgress(progress);
            });
            
            this.on("success", function(file, response) {
                hideUploadProgress();
                showUploadSuccess();
                
                const fontName = file.name.replace(/\.[^/.]+$/, "");
                console.log(response,fontName)
                saveFontToDatabase(fontName, response.file.url);
                this.removeFile(file);
            });
            
            this.on("error", function(file, errorMessage) {
                hideUploadProgress();
                showUploadError(errorMessage);
                this.removeFile(file);
            });
        }
    });

    function showUploadProgress() {
        $("#upload-progress").removeClass("d-none");
        $("#upload-status").addClass("d-none");
    }

    function updateUploadProgress(progress) {
        $("#upload-percentage").text(Math.round(progress) + "%");
        $("#upload-progress .progress-bar").css("width", progress + "%");
    }

    function hideUploadProgress() {
        setTimeout(function() {
            $("#upload-progress").addClass("d-none");
        }, 1000);
    }

    function showUploadSuccess() {
        $("#upload-status .alert").removeClass("alert-danger").addClass("alert-success");
        $("#upload-status .alert i").removeClass("bx-x-circle").addClass("bx-check-circle");
        $("#upload-status .alert span").text("Font uploaded successfully!");
        $("#upload-status").removeClass("d-none");
        
        setTimeout(function() {
            $("#upload-status").addClass("d-none");
        }, 3000);
        
        loadFonts();
    }

    function showUploadError(error) {
        $("#upload-status .alert").removeClass("alert-success").addClass("alert-danger");
        $("#upload-status .alert i").removeClass("bx-check-circle").addClass("bx-x-circle");
        $("#upload-status .alert span").text("Upload failed: " + error);
        $("#upload-status").removeClass("d-none");
        
        setTimeout(function() {
            $("#upload-status").addClass("d-none");
        }, 5000);
    }

    function saveFontToDatabase(name, fileUrl) {
        $.ajax({
            url: "/api/custom-fonts",
            type: "POST",
            data: {
                name: name,
                fileUrl: fileUrl
            },
            success: function(response) {
                console.log("Font saved to database:", response);
                loadFonts();
            },
            error: function(xhr, status, error) {
                console.error("Failed to save font:", error);
                showUploadError("Failed to save font to database");
            }
        });
    }

    function loadFonts() {
        $.ajax({
            url: "/api/custom-fonts",
            type: "GET",
            success: function(response) {
              console.log(response,'response')
                fonts = response;
                renderFonts();
                // updateStats();
            },
            error: function(xhr, status, error) {
                console.error("Failed to load fonts:", error);
            }
        });
    }

    function renderFonts() {
        const container = $("#fonts-container");
        
        if (fonts.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="bx bx-font fs-1 text-muted mb-3"></i>
                    <p class="text-muted">No custom fonts uploaded yet</p>
                </div>
            `);
            return;
        }

        container.empty();
        
        fonts.forEach(font => {
            const template = document.getElementById("font-item-template");
            const clone = template.content.cloneNode(true);
            
            $(clone).find(".font-name").text(font.name);
            $(clone).find(".upload-date").text(new Date(font.createdAt).toLocaleDateString());
            
            $(clone).find(".preview-btn").click(function() {
                toggleFontPreview(font, $(this).closest(".font-item"));
            });
            
            $(clone).find(".delete-btn").click(function() {
                deleteFont(font._id, $(this).closest(".font-item"));
            });
            
            container.append(clone);
        });
    }

    function toggleFontPreview(font, item) {
        const previewArea = item.find(".font-preview");
        const previewText = item.find(".preview-text");
        
        if (previewArea.hasClass("d-none")) {
            loadFontForPreview(font.fileUrl, font.name);
            previewArea.removeClass("d-none");
            item.find(".preview-btn").html('<i class="bx bx-hide"></i> Hide');
        } else {
            previewArea.addClass("d-none");
            item.find(".preview-btn").html('<i class="bx bx-show"></i> Preview');
        }
    }

    function loadFontForPreview(fileUrl, fontName) {
        const fontFace = new FontFace(fontName, `url(${fileUrl})`);
        
        fontFace.load().then(function(loadedFace) {
            document.fonts.add(loadedFace);
            $(".font-preview .preview-text").css("font-family", fontName);
            $("#font-preview-text").css("font-family", fontName);
            currentPreviewFont = fontName;
        }).catch(function(error) {
            console.error("Failed to load font:", error);
        });
    }

    function deleteFont(fontId, item) {
        if (confirm("Are you sure you want to delete this font?")) {
            $.ajax({
                url: `/api/custom-fonts/${fontId}`,
                type: "DELETE",
                success: function(response) {
                    item.fadeOut(300, function() {
                        $(this).remove();
                        loadFonts();
                    });
                },
                error: function(xhr, status, error) {
                    console.error("Failed to delete font:", error);
                    alert("Failed to delete font");
                }
            });
        }
    }

    // function updateStats() {
    //     $("#total-fonts").text(fonts.length);
    //     $("#total-size").text("0 MB");
    // }

    $("#preview-text").on("input", function() {
        const text = $(this).val();
        $("#font-preview-text").text(text);
        $(".font-preview .preview-text").text(text);
    });

    $("#font-size").on("input", function() {
        const size = $(this).val();
        $("#font-preview-text").css("font-size", size + "px");
        $(".font-preview .preview-text").css("font-size", size + "px");
    });

    loadFonts();
});
</script>

<style>
.dropzone-custom {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dropzone-custom:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.dropzone-custom.dz-drag-hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-icon {
    color: #007bff;
}

.font-item {
    transition: all 0.3s ease;
}

.font-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.font-preview {
    background: #f8f9fa;
    border-radius: 4px;
}

.preview-area {
    background: #f8f9fa;
    min-height: 100px;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card {
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.08);
    border-radius: 12px;
}

.card-header {
    background: transparent;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.form-control, .form-range {
    border-radius: 8px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}
</style>