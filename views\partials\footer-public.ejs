<div class="footer container-fluid" style="left: 0">
  <div class="row">
    <div class="col-sm-6">
      <span id="businessName"></span> | © copyright reserved |
      <a
        class="text-secondary"
        href="https://careers.letsshineksa.com/data-privacy"
        target="_blank"
        >Privary Policy</a
      >
      |
      <script>
        document.write(new Date().getFullYear());
      </script>
    </div>
    <div class="col-sm-6">
      <div class="text-sm-end d-none d-sm-block">
        <p>
          Powered by
          <a
            target="_blank"
            class="brand-name"
            href="https://www.mixcommerce.co/mixcertificate/"
            >MixCertificate
          </a>
        </p>
      </div>
    </div>
  </div>
</div>
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script>
  $(document).ready(function () {
    $.ajax({
      url: "/api/users/profile",
      method: "GET",
      success: function (response) {
        businessId = response.businessId;
        fetchBusinessData(businessId); // Call to fetch data based on businessId
      },
      error: function (xhr, status, error) {
        console.error("Error fetching businessId:", error);
      },
    });
    function fetchBusinessData(businessId) {
      $.ajax({
        method: "GET",
        url: `/api/business/${businessId}`,
        success: function (result) {
          $("#businessName").text(result.businessName);
        },
        error: function (error) {
          console.error(error);
        },
      });
    }
  });
</script>

<script type="text/javascript">
  (function (c, l, a, r, i, t, y) {
    c[a] =
      c[a] ||
      function () {
        (c[a].q = c[a].q || []).push(arguments);
      };
    t = l.createElement(r);
    t.async = 1;
    t.src = "https://www.clarity.ms/tag/" + i;
    y = l.getElementsByTagName(r)[0];
    y.parentNode.insertBefore(t, y);
  })(window, document, "clarity", "script", "s4x38v3eal");
</script>
