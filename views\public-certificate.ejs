<script
  src="https://code.jquery.com/jquery-3.7.1.min.js"
  integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="
  crossorigin="anonymous"
></script>

<style>
  #add-to-linkedin {
    background-color: #0077b5;
    /* LinkedIn brand color */
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    border-radius: 4px;
    font-size: 16px;
  }

  #add-to-linkedin:hover {
    background-color: #005582;
    /* Darker shade for hover effect */
    color: #ffffff;
  }

  #add-to-linkedin .fa-linkedin {
    margin-right: 8px;
    /* Space between icon and text */
  }
</style>

<!-- social share button starts -->
<style>
  .social-share-btn {
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    border-radius: 4px;
    font-size: 16px;
    margin-right: 10px;
  }

  .social-share-btn:hover {
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    border-radius: 4px;
    font-size: 16px;
    margin-right: 10px;
  }

  .social-share-btn .fab {
    margin-right: 8px;
    /* Space between icon and text */
  }

  #share-linkedin {
    background-color: #0077b5;
    /* LinkedIn brand color */
  }

  #share-linkedin:hover {
    background-color: #005582;
    /* Darker shade for hover effect */
  }

  #share-twitter {
    background-color: #1da1f2;
    /* Twitter brand color */
  }

  #share-twitter:hover {
    background-color: #0d8ddb;
    /* Darker shade for hover effect */
  }

  #share-facebook {
    background-color: #3b5998;
    /* Facebook brand color */
  }

  #share-facebook:hover {
    background-color: #2d4373;
    /* Darker shade for hover effect */
  }
</style>
<!-- social share button ends -->

<body>
  <div class="container mt-4">
    <button id="toggle-form" class="btn btn-primary mb-3">Submit Query</button>

    <div id="submission-form-container" style="display: none">
      <form id="submission-form" class="p-3 border rounded bg-light">
        <div class="mb-2">
          <input
            type="text"
            id="name"
            name="name"
            class="form-control form-control-sm"
            placeholder="Name"
            required
          />
        </div>
        <div class="mb-2">
          <input
            type="email"
            id="email"
            name="email"
            class="form-control form-control-sm"
            placeholder="Email"
            required
          />
        </div>
        <div class="mb-2">
          <input
            type="tel"
            id="phone"
            name="phone"
            class="form-control form-control-sm"
            placeholder="Phone"
            required
          />
        </div>
        <div class="mb-2">
          <textarea
            id="note"
            name="note"
            class="form-control form-control-sm"
            rows="2"
            placeholder="Your query or note"
            required
          ></textarea>
        </div>
        <button id="submitBtn" type="submit" class="btn btn-sm btn-primary">
          Submit
        </button>
      </form>
    </div>
  </div>

  <div class="container my-5">
    <div class="row">
      <div class="col-md-8">
        <div class="mb-4">
          <img
            id="certificate-image"
            src=""
            alt="Certificate"
            class="img-fluid"
          />
        </div>
      </div>

      <div class="col-md-4">
        <a id="profile-link" href="" class="text-secondary">
          <h1 id="person-name">Person's Name</h1>
        </a>

        <!-- Certificate Table starts-->

        <!-- Certificate Details Table -->
        <table id="certificate-details" class="table">
          <thead>
            <tr>
              <th>Field</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Recipient Name</td>
              <td id="recipient-name"></td>
            </tr>
            <tr>
              <td>Position</td>
              <td id="position"></td>
            </tr>
            <tr>
              <td>Project Name</td>
              <td id="project-name"></td>
            </tr>
            <tr>
              <td>Start Date</td>
              <td id="start-date"></td>
            </tr>
            <tr>
              <td>End Date</td>
              <td id="end-date"></td>
            </tr>
            <tr>
              <td>Certificate ID</td>
              <td id="certificate-id"></td>
              <td></td>
            </tr>
          </tbody>
        </table>

        <!-- Certificate Table ends-->
        <div class="row">
          <div class="col-md-10">
            <!-- Add to LinkedIn Button -->
            <div id="linkedin-button-container">
              <a href="#" target="_blank" id="add-to-linkedin" class="btn">
                <i class="fab fa-linkedin"></i> Add Certificate to LinkedIn
              </a>
            </div>
          </div>
        </div>

        <!-- Add to Linkedin buttons ends -->

        <!-- Add to LinkedIn Button -->

        <div class="col-md-12 d-flex mt-2">
          <!-- Share on LinkedIn Button -->
          <a
            href="#"
            target="_blank"
            id="share-linkedin"
            class="btn social-share-btn"
          >
            <i class="fab fa-linkedin"></i> Linkedin
          </a>
          <!-- Share on Twitter Button -->
          <a
            href="#"
            target="_blank"
            id="share-twitter"
            class="btn social-share-btn"
          >
            <i class="fab fa-twitter"></i> Twitter
          </a>
          <!-- Share on Facebook Button -->
          <a
            href="#"
            target="_blank"
            id="share-facebook"
            class="btn social-share-btn"
          >
            <i class="fab fa-facebook"></i> Facebook
          </a>
        </div>

        <h3 class="mt-5">
          Other Certificates <span id="otherCertificatesCount">(0)</span>
        </h3>

        <ul id="other-certificates-list" class="list-group">
          <!-- Other Certificates will be dynamically added here -->
        </ul>

        <h3 class="mt-4">Badges <span id="badgesCount">(0)</span></h3>
        <ul id="badges-list" class="list-group">
          <!-- Other Certificates will be dynamically added here -->
        </ul>
      </div>
    </div>
  </div>

  <script>
    // Extract the certificateId from the URL
    const path = window.location.pathname;
    const pathSegments = path.split("/").filter(Boolean);
    const certificateId = pathSegments[pathSegments.length - 1];
    const nameSpaceCode = pathSegments[0];

    $(document).ready(function () {
      // Fetch the certificate data
      $.ajax({
        url: `/${nameSpaceCode}/certificate/api/certificate/${certificateId}`,
        method: "GET",
        success: function (data) {
          const certificate = data.certificate;

          // Update certificate details on the page
          $("#person-name").text(certificate.recipientName || "N/A");

          //update table
          $("#recipient-name").text(certificate.recipientName || "N/A");
          $("#position").text(certificate.position || "N/A");
          $("#project-name").text(certificate.certificateName || "N/A");
          $("#start-date").text(certificate.startDate || "N/A");
          $("#end-date").text(certificate.endDate || "N/A");
          $("#certificate-id").text(certificate.certificateId || "N/A");

          //update certificate imgae
          $("#certificate-image").attr("src", certificate.imageURL);

          //update length of certificate and badge
          if (
            certificate.contactId &&
            certificate.contactId.certificates.length
          ) {
            // Process Other Certificates
            certificate.contactId.certificates.forEach(function (
              singleCertificate
            ) {
              if (singleCertificate._id !== certificateId) {
                $("#other-certificates-list").append(`
                              <li class="list-group-item">
                                  <a href="${singleCertificate._id}">
                                      Certificate ID: ${singleCertificate._id}
                                  </a>
                              </li>
                          `);
              }
            });
            $("#otherCertificatesCount").text(
              "(" + certificate.contactId.certificates.length + ")"
            );
          }
          if (certificate.contactId && certificate.contactId.badges.length) {
            // Process Other Badges
            certificate.contactId.badges.forEach(function (singleBadge) {
              $("#badges-list").append(`
                          <tr>
                              <td>
                                  <img src="${singleBadge.badgeURL}" alt="${singleBadge.title}" style="width: 50px; height: 50px;">
                              </td>
                              <td>
                                  <a href="https://mixcertificate-dev.mixcommerce.co/badge/${singleBadge._id}">
                                      <p>${singleBadge.title}</p>
                                  </a>
                              </td>
                          </tr>
                      `);
            });
            $("#badgesCount").text(
              "(" + certificate.contactId.badges.length + ")"
            );
          }

          // Update Open Graph (OG) tags
          updateOgTags(certificate);
        },
        error: function (err) {
          console.log("Error fetching certificate: ", err);
          // Handle certificate not found
          // window.location.href = "/certificate-not-found";
        },
      });

      // Social Share Script
      const currentPageUrl = encodeURIComponent(window.location.href);
      const linkedinShareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${currentPageUrl}`;
      const twitterShareUrl = `https://twitter.com/intent/tweet?url=${currentPageUrl}`;
      const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${currentPageUrl}`;

      $("#share-linkedin").attr("href", linkedinShareUrl);
      $("#share-twitter").attr("href", twitterShareUrl);
      $("#share-facebook").attr("href", facebookShareUrl);

      // Add to LinkedIn Button
      $.ajax({
        url: `/${nameSpaceCode}/certificate/api/certificate/${certificateId}`,
        method: "GET",
        success: function (data) {
          const certificate = data.certificate;
          const linkedInUrl =
            `https://www.linkedin.com/profile/add?startTask=CERTIFICATION_NAME` +
            `&name=${encodeURIComponent(certificate.certificateName)}` +
            `&organizationName=${encodeURIComponent(
              data.companyName || "mixcertificate"
            )}` +
            `&certUrl=${encodeURIComponent(
              `${window.location.origin}/certificate/${certificate._id}`
            )}` +
            `&certId=${encodeURIComponent(
              certificate.certificateId || certificate._id
            )}`;

          $("#add-to-linkedin").attr("href", linkedInUrl);
        },
        error: function (err) {
          console.log("Error fetching contact information: ", err);
        },
      });

      // Function to update OG tags
      function updateOgTags(cert) {
        const pageTitle = `${cert.certificateName} - ${cert.recipientName}`;
        const description = `This certificate certifies ${cert.recipientName} for ${cert.projectName}.`;
        const url = `${window.location.origin}/certificate/${cert._id}`;

        $("title").text(pageTitle);
        $('meta[property="og:title"]').attr("content", pageTitle);
        $('meta[property="og:description"]').attr("content", description);
        $('meta[property="og:url"]').attr("content", url);
        $('meta[property="og:image"]').attr("content", cert.imageURL);
        $('meta[property="og:site_name"]').attr(
          "content",
          "MixCertificate by MixCommerce"
        );
      }
    });
  </script>

  <script>
    $("#toggle-form").on("click", function () {
      $("#submission-form-container").slideToggle();
    });

    $("#submission-form").on("submit", function (e) {
      e.preventDefault();
      const submitBtn = $("#submitBtn");
      const originalText = submitBtn.text();
      submitBtn.prop("disabled", true);
      submitBtn.html(
        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...'
      );

      const formData = {
        name: $("#name").val(),
        email: $("#email").val(),
        phone: $("#phone").val(),
        note: $("#note").val(),
      };

      $.ajax({
        url: `/api/forms/public/submit`,
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify(formData),
        success: function (response) {
          alert("Thank you! Your query was submitted.");
          $("#submission-form")[0].reset();
          $("#submission-form-container").slideUp();
          submitBtn.prop("disabled", false);
          submitBtn.html(originalText);
        },
        error: function (xhr) {
          alert("Error: " + xhr.responseJSON?.message || "Submission failed.");
          submitBtn.prop("disabled", false);
          submitBtn.html(originalText);
        },
      });
    });
  </script>
</body>
