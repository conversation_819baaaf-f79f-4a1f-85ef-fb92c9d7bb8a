const cron = require("node-cron");
const ApiKey = require("../models/ApiKey");
const updateExpiredKeys = () => {
  cron.schedule("0 * * * *", async () => {
    // Runs every hour

    const now = new Date();
    try {
      await ApiKey.updateMany(
        { expiresAt: { $lte: now }, status: "active" },
        { $set: { status: "expired" } }
      );
      console.log("Expired API keys updated successfully.");
    } catch (err) {
      console.error("Failed to update expired API keys:", err.message);
    }
  });
};

module.exports = updateExpiredKeys;
