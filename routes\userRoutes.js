const express = require("express");
const bodyParser = require("body-parser");
const userController = require("../controllers/userController");
const { checkPermissions } = require("../middleware/middleware"); // Importing the checkPermissions middleware

const router = express.Router();

// Middleware to parse JSON bodies
router.use(bodyParser.json());

// Get total count of User documents (Admin and Manager only)
router.get(
  "/analytics/count",
  checkPermissions("User", ["read"]),
  userController.getUserCount
);

// Get estimated count of User documents (Admin and Manager only)
router.get(
  "/analytics/estimated-count",
  checkPermissions("User", ["read"]),
  userController.getEstimatedUserCount
);

// Create a new user (Admin and Manager only)
router.post(
  "/",
  checkPermissions("User", ["create"]),
  userController.createUser
);

// Get all users (Admin, Manager, Editor, and Viewer)
router.get("/", checkPermissions("User", ["read"]), userController.getUsers);

// Get current user profile (All roles)
router.get(
  "/profile",
  checkPermissions("User", ["read"]),
  userController.getCurrentUserInfo
);

// Update the current user's profile (All roles)
router.put(
  "/profile",
  checkPermissions("User", ["update"]),
  userController.updateCurrentUserProfile
);

// Get a single user by ID (Admin, Manager, Editor, and Viewer)
router.get(
  "/:id",
  checkPermissions("User", ["read"]),
  userController.getUserById
);

// Update a user by ID (Admin and Manager only)
router.put(
  "/:id",
  checkPermissions("User", ["update"]),
  userController.updateUserById
);

// Delete a user by ID (Admin and Manager only)
router.delete(
  "/:id",
  checkPermissions("User", ["delete"]),
  userController.deleteUserById
);

router.post(
  "/verify-password",
  userController.verifyPasswordAndSendOtpForAccountDeletion
);

router.post("/verify-otp", userController.veryOtpForAccountDeletion);

router.post("/resend-otp", userController.resendOtpForAccountDeletion);

router.post("/delete-account", userController.markAccountForDeletion);

router.post(
  "/invite",
  checkPermissions("User", ["create"]),
  userController.inviteUser
);

router.post(
  "/reinvite",
  checkPermissions("User", ["update"]),
  userController.reinviteUser
);

module.exports = router;
