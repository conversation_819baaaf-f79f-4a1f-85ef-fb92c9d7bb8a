const express = require('express');
const router = express.Router();
const eventController = require('../controllers/eventController');
const { checkPermissions } = require('../middleware/middleware');

router.post('/', checkPermissions('Event', ['create']), eventController.createEvent);
router.get('/', checkPermissions('Event', ['read']), eventController.getAllEvents);
router.get('/certificate/:id', checkPermissions('Event', ['read']), eventController.getCertificateEventById);

router.get('/:id', checkPermissions('Event', ['read']), eventController.getEventById);
router.put('/lock/:id', checkPermissions('Event', ['update']), eventController.toggleCertificateGenerationLock);
router.put('/:id', checkPermissions('Event', ['update']), eventController.updateEvent);
router.delete('/:id', checkPermissions('Event', ['delete']), eventController.deleteEvent);
router.post('/new-contact-to-event/:id', checkPermissions('Event', ['create']), eventController.createCertificateForNewContact);
router.post('/bulk-email-certificates', checkPermissions('Event', ['create']), eventController.sendBulkEmailsCertificateRecipients);
router.get('/certificates/bulk-download', checkPermissions('Event', ['read']), eventController.bulkDownloadCertificates);
router.delete('/delete/candidate', checkPermissions('Event', ['delete']), eventController.deleteCandidateFromEvent);

module.exports = router;
