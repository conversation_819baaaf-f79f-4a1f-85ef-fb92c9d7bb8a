const Webhook = require("../models/Webhook");

const SUPPORTED_TRIGGERS = [
  "certificate.bulk_generated",
  "candidate.added",
  "candidate.deleted",
  "certificate.issued",
];

// Create webhook
exports.createWebhook = async (req, res) => {
  console.log("Creating webhook", req.body);
  try {
    const { eventId, url, triggers, secret, isEnabled } = req.body;

    if (!eventId || !url || !Array.isArray(triggers) || triggers.length === 0) {
      return res.status(400).json({ error: "Missing or invalid fields" });
    }

    if (!triggers.every((t) => SUPPORTED_TRIGGERS.includes(t))) {
      return res
        .status(400)
        .json({ error: "One or more unsupported triggers" });
    }

    const webhook = new Webhook({
      eventId,
      url,
      triggers,
      secret,
      isEnabled:
        isEnabled !== undefined && isEnabled !== null ? isEnabled : true,
    });

    await webhook.save();
    res.status(201).json({ success: true, webhook });
  } catch (err) {
    console.error("Error creating webhook", err);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Get all webhooks for an event
exports.getWebhooksByEvent = async (req, res) => {
  try {
    const { eventId } = req.params;
    const webhooks = await Webhook.find({ eventId });
    res.json({ success: true, webhooks });
  } catch (err) {
    res.status(500).json({ error: "Failed to fetch webhooks" });
  }
};

// Update webhook
exports.updateWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    const { url, triggers, secret, isEnabled } = req.body;

    const webhook = await Webhook.findById(id);
    if (!webhook) return res.status(404).json({ error: "Webhook not found" });

    if (triggers && !triggers.every((t) => SUPPORTED_TRIGGERS.includes(t))) {
      return res.status(400).json({ error: "Invalid triggers" });
    }

    webhook.url = url ?? webhook.url;
    webhook.triggers = triggers ?? webhook.triggers;
    webhook.secret = secret ?? webhook.secret;
    webhook.isEnabled = isEnabled ?? webhook.isEnabled;

    await webhook.save();
    res.json({ success: true, webhook });
  } catch (err) {
    res.status(500).json({ error: "Failed to update webhook" });
  }
};

// Delete webhook
exports.deleteWebhook = async (req, res) => {
  try {
    const { id } = req.params;
    await Webhook.findByIdAndDelete(id);
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: "Failed to delete webhook" });
  }
};


exports.getAllWebhooks = async (req, res) => {
  try {
    const webhooks = await Webhook.find({ isEnabled: true });

    // const eventWebhooks = webhooks.reduce((acc, webhook) => {
    //   webhook.triggers.forEach((trigger) => {
    //     if (!acc[trigger]) {
    //       acc[trigger] = [];
    //     }
    //     acc[trigger].push(webhook);
    //   });
    //   return acc;
    // }, {});

    res.json({ success: true, webhooks: webhooks });
  } catch (err) {
    res.status(500).json({ error: "Failed to fetch webhooks" });
  }
};