<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title": "New Ticket"}) %>

<!-- Select2 CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />

<style>
  .ticket-form-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    padding: 30px;
    margin-bottom: 30px;
  }

  .form-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    padding: 25px 30px;
    margin: -30px -30px 30px -30px;
    border-radius: 12px 12px 0 0;
  }

  .form-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
  }

  .form-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    color: #ffffff;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
  }

  .required {
    color: #dc3545;
  }

  .form-control-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
  }

  .form-control-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .form-control-custom.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  .invalid-feedback {
    display: block;
    font-size: 14px;
    color: #dc3545;
    margin-top: 5px;
  }

  /* Priority Radio Buttons */
  .priority-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 10px;
  }

  .priority-option {
    position: relative;
  }

  .priority-radio {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }

  .priority-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-align: center;
  }

  .priority-radio:checked + .priority-label {
    border-color: #5156be;
    background: #f0f0ff;
    color: #5156be;
  }

  .priority-label.low {
    color: #28a745;
  }

  .priority-radio:checked + .priority-label.low {
    border-color: #28a745;
    background: #d4edda;
    color: #28a745;
  }

  .priority-label.medium {
    color: #ffc107;
  }

  .priority-radio:checked + .priority-label.medium {
    border-color: #ffc107;
    background: #fff3cd;
    color: #856404;
  }

  .priority-label.high {
    color: #fd7e14;
  }

  .priority-radio:checked + .priority-label.high {
    border-color: #fd7e14;
    background: #ffeaa7;
    color: #fd7e14;
  }

  .priority-label.urgent {
    color: #dc3545;
  }

  .priority-radio:checked + .priority-label.urgent {
    border-color: #dc3545;
    background: #f8d7da;
    color: #dc3545;
  }

  /* Select2 Customization */
  .select2-container--default .select2-selection--single {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    height: 50px;
    padding: 12px 15px;
    font-size: 16px;
  }

  .select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 26px;
    padding-left: 0;
  }

  .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 46px;
    right: 10px;
  }

  /* Tags Input */
  .tags-input {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    min-height: 50px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: center;
    cursor: text;
  }

  .tags-input:focus-within {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
  }

  .tag {
    background: #5156be;
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .tag-remove {
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
  }

  .tag-input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 100px;
    padding: 8px;
    font-size: 16px;
  }

  /* File Upload */
  .file-upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .file-upload-area:hover {
    border-color: #5156be;
    background: #f0f0ff;
  }

  .file-upload-area.dragover {
    border-color: #5156be;
    background: #f0f0ff;
  }

  .file-list {
    margin-top: 15px;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 5px;
  }

  .file-remove {
    background: #dc3545;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
  }

  /* Buttons */
  .btn-primary-custom {
    background: #5156be;
    color: #ffffff;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .btn-primary-custom:hover {
    background: #4c51b8;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(81, 86, 190, 0.3);
  }

  .btn-secondary-custom {
    background: #6c757d;
    color: #ffffff;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-right: 10px;
  }

  .btn-secondary-custom:hover {
    background: #5a6268;
    transform: translateY(-1px);
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #5156be;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .ticket-form-container {
      padding: 20px;
    }

    .form-header {
      padding: 20px;
      margin: -20px -20px 20px -20px;
    }

    .priority-options {
      grid-template-columns: repeat(2, 1fr);
    }

    .form-actions {
      flex-direction: column;
      gap: 10px;
    }

    .btn-primary-custom,
    .btn-secondary-custom {
      width: 100%;
      margin-right: 0;
    }
  }

  @media (max-width: 576px) {
    .priority-options {
      grid-template-columns: 1fr;
    }
  }
</style>


<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "New Ticket" , "pagetitle": "Ticket" }) %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="ticket-form-container">
        <div class="form-header">
          <h2><i class="bx bx-support me-2"></i>Create New Support Ticket</h2>
          <p>Fill out the form below to create a new support ticket</p>
        </div>

        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <form id="ticketForm">
          <!-- Title -->
          <div class="form-group">
            <label for="title" class="form-label">
              Title <span class="required">*</span>
            </label>
            <input
              type="text"
              id="title"
              name="title"
              class="form-control-custom"
              placeholder="Enter ticket title"
              required
            />
            <div class="invalid-feedback" id="titleError"></div>
          </div>

          <!-- Company Name -->
          <div class="form-group">
            <label for="companyName" class="form-label">
              Company Name <span class="required">*</span>
            </label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              class="form-control-custom"
              placeholder="Enter company name"
              required
            />
            <div class="invalid-feedback" id="companyNameError"></div>
          </div>

          <!-- Description -->
          <div class="form-group">
            <label for="description" class="form-label">
              Description <span class="required">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              class="form-control-custom"
              rows="6"
              placeholder="Describe the issue in detail..."
              required
            ></textarea>
            <div class="invalid-feedback" id="descriptionError"></div>
          </div>

          <!-- Priority -->
          <div class="form-group">
            <label class="form-label">
              Priority <span class="required">*</span>
            </label>
            <div class="priority-options">
              <div class="priority-option">
                <input type="radio" id="priority_low" name="priority" value="Low" class="priority-radio" required>
                <label for="priority_low" class="priority-label low">
                  <i class="bx bx-down-arrow-alt me-1"></i>Low
                </label>
              </div>
              <div class="priority-option">
                <input type="radio" id="priority_medium" name="priority" value="Medium" class="priority-radio" checked>
                <label for="priority_medium" class="priority-label medium">
                  <i class="bx bx-minus me-1"></i>Medium
                </label>
              </div>
              <div class="priority-option">
                <input type="radio" id="priority_high" name="priority" value="High" class="priority-radio">
                <label for="priority_high" class="priority-label high">
                  <i class="bx bx-up-arrow-alt me-1"></i>High
                </label>
              </div>
              <div class="priority-option">
                <input type="radio" id="priority_urgent" name="priority" value="Urgent" class="priority-radio">
                <label for="priority_urgent" class="priority-label urgent">
                  <i class="bx bx-error me-1"></i>Urgent
                </label>
              </div>
            </div>
            <div class="invalid-feedback" id="priorityError"></div>
          </div>

          <!-- Assign To -->
          <div class="form-group">
            <label for="assignTo" class="form-label">
              Assign To
            </label>
            <select id="assignTo" name="assignTo" class="form-control-custom">
              <option value="">Select a user to assign (optional)</option>
            </select>
            <div class="invalid-feedback" id="assignToError"></div>
          </div>
           <!-- Company  Name  -->
              <div class="form-group">
            <label for="assignTo" class="form-label">
              Company Name
            </label>
            <select id="assignTo" name="assignTo" class="form-control-custom">
              <option value="">Select a user to assign (optional)</option>
            </select>
            <div class="invalid-feedback" id="assignToError"></div>
          </div>
          <!-- Tags -->
          <div class="form-group">
            <label for="tags" class="form-label">
              Tags
            </label>
            <div class="tags-input" id="tagsInput">
              <input type="text" class="tag-input" placeholder="Type and press Enter to add tags..." />
            </div>
            <input type="hidden" id="tagsHidden" name="tags" />
            <small class="text-muted">Press Enter to add tags. Tags help categorize and search tickets.</small>
          </div>

          <!-- Attachments -->
          <div class="form-group">
            <label class="form-label">
              Attachments
            </label>
            <div class="file-upload-area" id="fileUploadArea">
              <i class="bx bx-cloud-upload" style="font-size: 48px; color: #5156be; margin-bottom: 10px;"></i>
              <p style="margin: 0; font-size: 16px; color: #495057;">
                <strong>Click to upload</strong> or drag and drop files here
              </p>
              <p style="margin: 5px 0 0 0; font-size: 14px; color: #6c757d;">
                Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF (Max 10MB each)
              </p>
            </div>
            <input type="file" id="fileInput" multiple style="display: none;" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif" />
            <div class="file-list" id="fileList"></div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions" style="display: flex; justify-content: flex-end; margin-top: 30px;">
            <button type="button" class="btn-secondary-custom" onclick="resetForm()">
              <i class="bx bx-reset me-1"></i>Reset
            </button>
            <button type="submit" class="btn-primary-custom" id="submitBtn">
              <i class="bx bx-check me-1"></i>Create Ticket
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>





<%- contentFor('FooterJs') %>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
  // Global variables
  let selectedFiles = [];
  let selectedTags = [];

  $(document).ready(function() {
    initializeForm();
    loadUsers();
    setupEventListeners();
  });

  // Initialize form components
  function initializeForm() {
    // Initialize Select2 for assignTo dropdown
    $('#assignTo').select2({
      placeholder: 'Select a user to assign (optional)',
      allowClear: true,
      width: '100%'
    });
  }

  // Load users for assignment dropdown
  function loadUsers() {
    $.ajax({
      url: "/api/users",
      method: "GET",
      success: function(response) {
        console.log('Users loaded:', response);
        populateAssignToDropdown(response.data || response);
      },
      error: function(xhr) {
        console.error('Error loading users:', xhr);
        showAlert('Error loading users for assignment', 'danger');
      }
    });
  }

  // Populate assign to dropdown
  function populateAssignToDropdown(users) {
    const $assignTo = $('#assignTo');
    $assignTo.empty();
    $assignTo.append('<option value="">Select a user to assign (optional)</option>');

    if (users && users.length > 0) {
      users.forEach(user => {
        const displayName = `${user.firstName} ${user.lastName} (${user.email})`;
        $assignTo.append(`<option value="${user._id}">${displayName}</option>`);
      });
    }
  }

  // Setup event listeners
  function setupEventListeners() {
    // Form submission
    $('#ticketForm').on('submit', function(e) {
      e.preventDefault();
      submitTicket();
    });

    // File upload area click
    $('#fileUploadArea').on('click', function() {
      $('#fileInput').click();
    });

    // File input change
    $('#fileInput').on('change', function(e) {
      handleFileSelection(e.target.files);
    });

    // Drag and drop functionality
    $('#fileUploadArea').on('dragover', function(e) {
      e.preventDefault();
      $(this).addClass('dragover');
    });

    $('#fileUploadArea').on('dragleave', function(e) {
      e.preventDefault();
      $(this).removeClass('dragover');
    });

    $('#fileUploadArea').on('drop', function(e) {
      e.preventDefault();
      $(this).removeClass('dragover');
      handleFileSelection(e.originalEvent.dataTransfer.files);
    });

    // Tags input functionality
    $('.tag-input').on('keypress', function(e) {
      if (e.which === 13) { // Enter key
        e.preventDefault();
        addTag($(this).val().trim());
        $(this).val('');
      }
    });

    // Priority radio button change
    $('input[name="priority"]').on('change', function() {
      clearFieldError('priority');
    });

    // Input field changes to clear errors
    $('#title, #companyName, #description').on('input', function() {
      clearFieldError($(this).attr('name'));
    });
  }

  // Handle file selection
  function handleFileSelection(files) {
    Array.from(files).forEach(file => {
      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        showAlert(`File "${file.name}" is too large. Maximum size is 10MB.`, 'warning');
        return;
      }

      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif'
      ];

      if (!allowedTypes.includes(file.type)) {
        showAlert(`File "${file.name}" has an unsupported format.`, 'warning');
        return;
      }

      // Check if file already selected
      if (selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
        showAlert(`File "${file.name}" is already selected.`, 'warning');
        return;
      }

      selectedFiles.push(file);
      displaySelectedFiles();
    });
  }

  // Display selected files
  function displaySelectedFiles() {
    const $fileList = $('#fileList');
    $fileList.empty();

    selectedFiles.forEach((file, index) => {
      const fileSize = (file.size / 1024 / 1024).toFixed(2);
      const fileItem = `
        <div class="file-item">
          <div>
            <i class="bx bx-file me-2"></i>
            <span>${file.name}</span>
            <small class="text-muted ms-2">(${fileSize} MB)</small>
          </div>
          <button type="button" class="file-remove" onclick="removeFile(${index})">
            <i class="bx bx-x"></i>
          </button>
        </div>
      `;
      $fileList.append(fileItem);
    });
  }

  // Remove file
  function removeFile(index) {
    selectedFiles.splice(index, 1);
    displaySelectedFiles();
  }

  // Add tag
  function addTag(tagText) {
    if (!tagText || selectedTags.includes(tagText)) {
      return;
    }

    selectedTags.push(tagText);
    displayTags();
    updateTagsHidden();
  }

  // Display tags
  function displayTags() {
    const $tagsInput = $('#tagsInput');
    const $tagInput = $tagsInput.find('.tag-input');

    // Remove existing tags
    $tagsInput.find('.tag').remove();

    // Add tags before input
    selectedTags.forEach((tag, index) => {
      const tagElement = `
        <span class="tag">
          ${tag}
          <button type="button" class="tag-remove" onclick="removeTag(${index})">×</button>
        </span>
      `;
      $tagInput.before(tagElement);
    });
  }

  // Remove tag
  function removeTag(index) {
    selectedTags.splice(index, 1);
    displayTags();
    updateTagsHidden();
  }

  // Update hidden tags input
  function updateTagsHidden() {
    $('#tagsHidden').val(JSON.stringify(selectedTags));
  }

  // Submit ticket
  function submitTicket() {
    // Clear previous errors
    clearAllErrors();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Prepare form data
    const formData = new FormData();

    // Add text fields
    formData.append('title', $('#title').val().trim());
    formData.append('companyName', $('#companyName').val().trim());
    formData.append('description', $('#description').val().trim());
    formData.append('priority', $('input[name="priority"]:checked').val());

    // Add optional fields
    const assignTo = $('#assignTo').val();
    if (assignTo) {
      formData.append('assignTo', assignTo);
    }

    // Add tags
    if (selectedTags.length > 0) {
      formData.append('tags', JSON.stringify(selectedTags));
    }

    // Add files
    selectedFiles.forEach((file, index) => {
      formData.append('attachments', file);
    });

    // Show loading state
    const $submitBtn = $('#submitBtn');
    const originalText = $submitBtn.html();
    $submitBtn.prop('disabled', true).html('<span class="loading-spinner"></span>Creating Ticket...');

    // Submit form
    $.ajax({
      url: '/api/tickets',
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        console.log('Ticket created successfully:', response);
        showAlert('Ticket created successfully!', 'success');

        // Reset form after successful submission
        setTimeout(() => {
          resetForm();
        }, 2000);
      },
      error: function(xhr) {
        console.error('Error creating ticket:', xhr);
        let message = 'Error creating ticket. Please try again.';

        if (xhr.responseJSON) {
          if (xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
          } else if (xhr.responseJSON.error) {
            message = xhr.responseJSON.error;
          }
        }

        showAlert(message, 'danger');
      },
      complete: function() {
        // Restore button state
        $submitBtn.prop('disabled', false).html(originalText);
      }
    });
  }

  // Validate form
  function validateForm() {
    let isValid = true;

    // Validate title
    const title = $('#title').val().trim();
    if (!title) {
      showFieldError('title', 'Title is required');
      isValid = false;
    } else if (title.length < 3) {
      showFieldError('title', 'Title must be at least 3 characters long');
      isValid = false;
    }

    // Validate company name
    const companyName = $('#companyName').val().trim();
    if (!companyName) {
      showFieldError('companyName', 'Company name is required');
      isValid = false;
    } else if (companyName.length < 2) {
      showFieldError('companyName', 'Company name must be at least 2 characters long');
      isValid = false;
    }

    // Validate description
    const description = $('#description').val().trim();
    if (!description) {
      showFieldError('description', 'Description is required');
      isValid = false;
    } else if (description.length < 10) {
      showFieldError('description', 'Description must be at least 10 characters long');
      isValid = false;
    }

    // Validate priority
    const priority = $('input[name="priority"]:checked').val();
    if (!priority) {
      showFieldError('priority', 'Please select a priority level');
      isValid = false;
    }

    return isValid;
  }

  // Show field error
  function showFieldError(fieldName, message) {
    const $field = $(`#${fieldName}`);
    const $error = $(`#${fieldName}Error`);

    $field.addClass('is-invalid');
    $error.text(message);
  }

  // Clear field error
  function clearFieldError(fieldName) {
    const $field = $(`#${fieldName}`);
    const $error = $(`#${fieldName}Error`);

    $field.removeClass('is-invalid');
    $error.text('');
  }

  // Clear all errors
  function clearAllErrors() {
    $('.form-control-custom').removeClass('is-invalid');
    $('.invalid-feedback').text('');
  }

 

  // Reset form
  function resetForm() {
    // Reset form fields
    $('#ticketForm')[0].reset();

    // Reset Select2
    $('#assignTo').val(null).trigger('change');

    // Reset priority to medium (default)
    $('#priority_medium').prop('checked', true);

    // Clear files
    selectedFiles = [];
    displaySelectedFiles();

    // Clear tags
    selectedTags = [];
    displayTags();
    updateTagsHidden();

    // Clear errors
    clearAllErrors();

    // Clear alerts
    $('#alertContainer').empty();

    // Focus on title field
    $('#title').focus();
  }

  // Global functions for onclick handlers
  window.removeFile = removeFile;
  window.removeTag = removeTag;
  window.resetForm = resetForm;
</script>