<%- contentFor('HeaderCss') %> 
<%- include("partials/title-meta", {"title":"Ticket Settings" }) %>
<%- contentFor('body') %>
 <%- include("partials/page-title", { title: "Ticket Settings", pagetitle:"Ticket" }) %>
<style>
  #tickets {
    min-height: 100vh;
    padding: 2rem;
   
  }

 
  .single-ticket-container {
    padding-right: 10px;
  }

  .ticket-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    border-radius: 1rem;
    background: #fff;
    padding: 1.5rem;
  }

  .ticket-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .ticket-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1rem;
  }

  .ticket-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
  }

  .status-open {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  .priority-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    border-radius: 0.8rem;
    font-weight: 600;
  }

  .priority-high {
    background-color: #ffebee;
    color: #d32f2f;
  }

  /* ONLY SCROLL COMMENTS */
  .comments-container {
    max-height: 200px;
    overflow-y: auto;
    padding-right: 10px;
    margin-top: 1rem;
    border-top: 1px solid #dee2e6;
  }

  .comments-container::-webkit-scrollbar {
    width: 4px;
  }

  .comments-container::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .comments-container::-webkit-scrollbar-thumb {
    background: #5156be;
    border-radius: 2px;
  }

  .comment-item {
    background: white;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #5156be;
  }

  .comment-user {
    font-weight: 600;
    color: #5156be;
    font-size: 0.85rem;
  }

  .comment-date {
    font-size: 0.75rem;
    color: #6c757d;
  }

  .comment-message {
    color: #495057;
    font-size: 0.9rem;
    margin: 0.5rem 0 0 0;
    line-height: 1.4;
  }

  @media (max-width: 768px) {
    .ticket-card {
      padding: 1rem;
    }

    .comments-container {
      max-height: 150px;
    }
  }
</style>

<section id="tickets">
  <div class="container h-100">
    <div class="row justify-content-center align-items-center h-100">
      <div class="col-12 col-md-11 col-lg-10">
        <div class="text-center mb-4">
          <h2 class="mb-2">
            <i class="fas fa-ticket-alt me-2" style="color: #5156be"></i>
            Support Tickets
          </h2>
          <span class="badge bg-primary" id="ticketCount">1 Ticket</span>
        </div>

        <!-- Ticket Container (No overflow here) -->
        <div id="ticketsContainer" class="single-ticket-container">
          <div class="ticket-card">
            <div class="ticket-header">
              <h5 class="mb-0">Login Not Working</h5>
            </div>
            <div class="mt-3">
              <p><strong>Status:</strong> <span class="ticket-status status-open">Open</span></p>
              <p><strong>Priority:</strong> <span class="priority-badge priority-high">High</span></p>
              <p><strong>Description:</strong> I’m unable to log in using my registered email. It says “Invalid Credentials” even though I'm sure the details are correct.</p>
            </div>

            <!-- Comments scroll only -->
            <div class="comments-container">
            
              
            </div>
          </div>
        </div>

        
      </div>
    </div>
  </div>
</section>



 <%- contentFor('FooterJs') %>

<script>
  $(document).ready(function () {
    const ticketId=window.location.pathname.split("/").pop();
    console.log(ticketId, "ticketId");
    loadSingleTicket();

    // Setup comment input event listeners
    $(document).on("keypress", ".comment-input", function (e) {
      if (e.which === 13) {
        // Enter key
        e.preventDefault();
        const ticketId = $(this).data("ticket-id");
        addComment(ticketId, $(this));
      }
    });

    // Setup comment button click
    $(document).on("click", ".comment-btn", function () {
      const ticketId = $(this).data("ticket-id");
      const $input = $(this).siblings(".comment-input");
      addComment(ticketId, $input);
    });

    // Setup navigation buttons
    $(document).on("click", "#prevTicket", function () {
      navigateTicket(-1);
    });

    $(document).on("click", "#nextTicket", function () {
      navigateTicket(1);
    });
  });

  let allTickets = [];
  let currentTicketIndex = 0;

  // Load all public tickets
  function loadSingleTicket() {
    $("#ticketsLoading").show();
    $("#ticketsContainer").hide();
    $("#noTickets").hide();
    $("#ticketNavigation").hide();

    $.ajax({
      method: "GET",
      url: `/api/tickets/${ticketId}`,
      success: function (response) {
        console.log(response, "response");
        $("#ticketsLoading").hide();

        if (response && response.tickets && response.tickets.length > 0) {
          allTickets = response.tickets;
          currentTicketIndex = 0;
          renderCurrentTicket();
          $("#ticketsContainer").show();

          // Show navigation if more than one ticket
          if (allTickets.length > 1) {
            $("#ticketNavigation").show(); 
            updateNavigation();
          }
        } else {
          $("#noTickets").show();
        }
      },
      error: function (error) {
        console.error("Error loading tickets:", error);
        $("#ticketsLoading").hide();
        $("#noTickets").show();
        showToast("Error loading tickets", "error");
      },
    });
  }

  // Render current ticket in the UI
  function renderCurrentTicket() {
    if (allTickets.length === 0) return;

    const ticket = allTickets[currentTicketIndex];
    const $container = $("#ticketsContainer");
    $container.empty();

    const ticketHtml = createTicketCard(ticket);
    $container.append(ticketHtml);

    // Update ticket count
    $("#ticketCount").text(
      `${allTickets.length} Ticket${allTickets.length !== 1 ? "s" : ""}`
    );
  }

  // Navigate between tickets
  function navigateTicket(direction) {
    const newIndex = currentTicketIndex + direction;

    if (newIndex >= 0 && newIndex < allTickets.length) {
      currentTicketIndex = newIndex;
      renderCurrentTicket();
      updateNavigation();

      // Scroll to top of ticket container
      $("#ticketsContainer").scrollTop(0);
    }
  }

  // Update navigation buttons and position
  function updateNavigation() {
    $("#prevTicket").prop("disabled", currentTicketIndex === 0);
    $("#nextTicket").prop(
      "disabled",
      currentTicketIndex === allTickets.length - 1
    );
    $("#ticketPosition").text(
      `${currentTicketIndex + 1} of ${allTickets.length}`
    );
  }

  // Create individual ticket card HTML
  function createTicketCard(ticket) {
    const statusClass = getStatusClass(ticket.status);
    const priorityClass = getPriorityClass(ticket.priority);
    const typeClass = getTypeClass(ticket.type);
    const createdDate = new Date(ticket.createdDate).toLocaleDateString();
    const assigneeName = ticket.assignee
      ? `${ticket.assignee.firstName} ${ticket.assignee.lastName}`
      : "Unassigned";

    return `
      <div class="ticket-card card">
        <div class="ticket-header card-header p-3">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <h5 class="mb-2 text-white">${ticket.title}</h5>
              <div class="d-flex flex-wrap gap-2 mb-2">
                <span class="ticket-status ${statusClass}">${
      ticket.status || "Open"
    }</span>
                ${
                  ticket.priority
                    ? `<span class="priority-badge ${priorityClass}">${ticket.priority}</span>`
                    : ""
                }
                ${
                  ticket.type
                    ? `<span class="type-badge ${typeClass}">${ticket.type}</span>`
                    : ""
                }
              </div>
            </div>
            <small class="text-white-50">#${ticket._id.slice(-6)}</small>
          </div>
        </div>

        <div class="card-body p-3">
          ${
            ticket.description
              ? `<p class="text-muted mb-3">${ticket.description}</p>`
              : ""
          }

          <div class="row text-sm mb-3">
            <div class="col-sm-6">
              <small class="text-muted">
                <i class="fas fa-calendar-alt me-1"></i>
                Created: ${createdDate}
              </small>
            </div>
            <div class="col-sm-6">
              <small class="text-muted">
                <i class="fas fa-user me-1"></i>
                Assigned: ${assigneeName}
              </small>
            </div>
          </div>

          ${
            ticket.tags && ticket.tags.length > 0
              ? `
            <div class="mb-3">
              ${ticket.tags
                .map(
                  (tag) =>
                    `<span class="badge bg-light text-dark me-1">${tag}</span>`
                )
                .join("")}
            </div>
          `
              : ""
          }
        </div>

        <div class="comment-section p-3">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
              <i class="fas fa-comments me-2"></i>
              Comments (${ticket.comments ? ticket.comments.length : 0})
            </h6>
          </div>

          ${
            ticket.comments && ticket.comments.length > 0
              ? `
            <div class="comments-container mb-3">
              ${ticket.comments
                .map((comment) => createCommentHtml(comment))
                .join("")}
            </div>
          `
              : `
            <div class="text-center text-muted py-3 mb-3">
              <i class="fas fa-comment-slash fa-2x mb-2"></i>
              <p class="mb-0">No comments yet. Be the first to comment!</p>
            </div>
          `
          }

          <div class="d-flex gap-2">
            <input
              type="text"
              class="form-control comment-input"
              placeholder="Add a comment... (Press Enter to send)"
              data-ticket-id="${ticket._id}"
            />
            <button
              class="btn comment-btn"
              data-ticket-id="${ticket._id}"
              title="Send Comment"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Create comment HTML
  function createCommentHtml(comment) {
    const commentDate = new Date(comment.createdAt).toLocaleString();
    const userName = comment.user
      ? `${comment.user.firstName} ${comment.user.lastName}`
      : "Anonymous";

    return `
      <div class="comment-item">
        <div class="d-flex justify-content-between align-items-center mb-1">
          <span class="comment-user">${userName}</span>
          <span class="comment-date">${commentDate}</span>
        </div>
        <p class="comment-message">${comment.message}</p>
      </div>
    `;
  }

  // Add comment to ticket
  function addComment(ticketId, $input) {
    const message = $input.val().trim();

    if (!message) {
      showToast("Please enter a comment", "warning");
      return;
    }

    // Disable input and button
    $input.prop("disabled", true);
    const $btn = $input.siblings(".comment-btn");
    const originalBtnHtml = $btn.html();
    $btn.prop("disabled", true).html('<i class="fas fa-spinner fa-spin"></i>');

    $.ajax({
      method: "POST",
      url: `/api/tickets/public/${ticketId}/comment`,
      contentType: "application/json",
      data: JSON.stringify({ message: message }),
      success: function (response) {
        // Clear input
        $input.val("");

        // Store current position before reloading
        const currentIndex = currentTicketIndex;

        // Reload tickets to show new comment
        $.ajax({
          method: "GET",
          url: "/api/tickets/public/all",
          success: function (response) {
            if (response && response.tickets && response.tickets.length > 0) {
              allTickets = response.tickets;
              currentTicketIndex = Math.min(
                currentIndex,
                allTickets.length - 1
              );
              renderCurrentTicket();
              updateNavigation();
            }
          },
        });

        showToast("Comment added successfully", "success");
      },
      error: function (xhr) {
        console.error("Error adding comment:", xhr);
        const errorMessage =
          xhr.responseJSON?.message || "Error adding comment";
        showToast(errorMessage, "error");
      },
      complete: function () {
        // Re-enable input and button
        $input.prop("disabled", false);
        $btn.prop("disabled", false).html(originalBtnHtml);
        $input.focus();
      },
    });
  }

  // Statistics removed - not needed for single ticket view

  // Get status CSS class
  function getStatusClass(status) {
    switch (status) {
      case "Open":
        return "status-open";
      case "In Progress":
        return "status-in-progress";
      case "Resolved":
        return "status-resolved";
      case "Closed":
        return "status-closed";
      default:
        return "status-open";
    }
  }

  // Get priority CSS class
  function getPriorityClass(priority) {
    switch (priority) {
      case "Low":
        return "priority-low";
      case "Medium":
        return "priority-medium";
      case "High":
        return "priority-high";
      case "Urgent":
        return "priority-urgent";
      default:
        return "priority-medium";
    }
  }

  // Get type CSS class
  function getTypeClass(type) {
    switch (type) {
      case "Bug":
        return "type-bug";
      case "Feature Request":
        return "type-feature";
      case "Support":
        return "type-support";
      case "Task":
        return "type-task";
      default:
        return "type-support";
    }
  }

 
</script>