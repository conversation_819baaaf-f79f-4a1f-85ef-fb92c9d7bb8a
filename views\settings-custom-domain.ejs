<%- contentFor('HeaderCss') %> <%-include("partials/title-meta",{"title":"Custom Domain Settings" }) %>
<style>
  .domain-setup-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .step-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .step-card:hover {
    box-shadow: 0 8px 30px rgba(81, 86, 190, 0.15);
    transform: translateY(-2px);
  }

  .step-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    padding: 25px 30px;
    border-bottom: none;
  }

  .step-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
  }

  .step-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 5px 0 0 0;
    color: #ffffff;
  }

  .step-body {
    padding: 30px;
  }

  .current-url-display {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin: 20px 0;
  }

  .current-url {
    font-size: 18px;
    font-weight: 600;
    color: #5156be;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  }

  .dns-record-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  }

  .dns-record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
  }

  .dns-record-item:last-child {
    border-bottom: none;
  }

  .dns-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
  }

  .dns-value {
    color: #5156be;
    font-weight: 500;
    flex: 1;
    text-align: right;
  }

  .status-message {
    padding: 15px 20px;
    border-radius: 8px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    font-weight: 500;
  }

  .status-waiting {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
  }

  .status-success {
    background: #d1edff;
    border: 1px solid #74b9ff;
    color: #155724;
  }

  .status-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
  }

  .status-icon {
    margin-right: 10px;
    font-size: 18px;
  }

  .btn-primary-custom {
    background: #5156be;
    border: none;
    color: #ffffff;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
  }

  .btn-primary-custom:hover {
    background: #4c51b8;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(81, 86, 190, 0.3);
  }

  .btn-secondary-custom {
    background: #6c757d;
    border: none;
    color: #ffffff;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-left: 10px;
  }

  .btn-secondary-custom:hover {
    background: #5a6268;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .form-control-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
  }

  .form-control-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .domain-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
  }

  .status-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
  }

  .status-item-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
  }

  .status-item-value {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }

  .status-item.verified .status-item-value {
    color: #28a745;
  }

  .support-links {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
  }

  .support-links a {
    color: #5156be;
    text-decoration: none;
    font-weight: 500;
  }

  .support-links a:hover {
    text-decoration: underline;
  }

  .step-hidden {
    display: none;
  }

  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #5156be;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .progress-bar-custom {
    background: #e9ecef;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin: 20px 0;
  }

  .progress-fill {
    background: linear-gradient(90deg, #5156be 0%, #6366f1 100%);
    height: 100%;
    transition: width 0.5s ease;
  }

  @media (max-width: 768px) {
    .domain-setup-container {
      padding: 10px;
    }

    .step-body {
      padding: 20px;
    }

    .dns-record-item {
      flex-direction: column;
      align-items: flex-start;
    }

    .dns-value {
      text-align: left;
      margin-top: 5px;
    }
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title",{"title": "Custom Domain Setup" , "pagetitle": "Settings" }) %>

<div class="domain-setup-container">
  <!-- Progress Bar -->
  <div class="progress-bar-custom">
    <div class="progress-fill" id="progressFill" style="width: 20%"></div>
  </div>

  <!-- Step 0: Overview Panel -->
  <div class="step-card" id="step0">
    <div class="step-header">
      <h2 class="step-title">Custom Domain Setup</h2>
      <p class="step-subtitle">White-label your portal with your own domain</p>
    </div>
    <div class="step-body">
      <div class="current-url-display">
        <p style="margin: 0 0 10px 0; color: #6c757d">
          Your current portal URL is:
        </p>
        <div class="current-url" id="currentPortalUrl">
          clientname.mixcommerce.co
        </div>
      </div>

      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin: 25px 0;
        "
      >
        You can use your own custom subdomain, such as
        <strong>certificates.yourcompany.com</strong>, to fully white-label your
        portal experience.
      </p>

      <div style="text-align: center; margin-top: 30px">
        <button class="btn-primary-custom" onclick="goToStep(1)">
          Map Custom Domain
        </button>
      </div>
    </div>
  </div>

  <!-- Step 1: Enter Custom Domain -->
  <div class="step-card step-hidden" id="step1">
    <div class="step-header">
      <h2 class="step-title">Step 1: Enter Your Custom Domain</h2>
      <p class="step-subtitle">Choose your desired subdomain</p>
    </div>
    <div class="step-body">
      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin-bottom: 20px;
        "
      >
        Please enter the subdomain you want to use.
      </p>
      <p style="font-size: 14px; color: #6c757d; margin-bottom: 25px">
        Example: certificates.yourcompany.com
      </p>

      <div class="form-group">
        <label
          for="customDomain"
          style="
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            display: block;
          "
        >
          Custom Domain
        </label>
        <input
          type="text"
          id="customDomain"
          class="form-control-custom"
          style="width: 100%"
          placeholder="certificates.yourcompany.com"
        />
      </div>

      <div id="domainStatus" class="status-message" style="display: none"></div>

      <div style="text-align: center; margin-top: 30px">
        <button class="btn-primary-custom" onclick="checkDomainAvailability()">
          Check Availability
        </button>
      </div>
    </div>
  </div>

  <!-- Step 2: DNS Configuration -->
  <div class="step-card step-hidden" id="step2">
    <div class="step-header">
      <h2 class="step-title">Step 2: Configure Your DNS Settings</h2>
      <p class="step-subtitle">Set up your DNS records</p>
    </div>
    <div class="step-body">
      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin-bottom: 20px;
        "
      >
        To connect your domain, please create the following DNS record in your
        domain registrar panel:
      </p>

      <!-- Hidden input field for subdomain -->
      <input type="hidden" id="subdomainValue" value="" />

      <div class="dns-record-box">
        <div class="dns-record-item">
          <span class="dns-label">Type:</span>
          <span class="dns-value">CNAME</span>
        </div>
        <div class="dns-record-item">
          <span class="dns-label">Host/Name:</span>
          <span class="dns-value" id="subdomainDisplay">certificates</span>
        </div>
        <div class="dns-record-item">
          <span class="dns-label">Value/Target:</span>
          <span class="dns-value" id="dnsTarget"
            >clientname.mixcommerce.co</span
          >
        </div>
        <div class="dns-record-item">
          <span class="dns-label">TTL:</span>
          <span class="dns-value">Auto or 3600</span>
        </div>
      </div>

      <p style="font-size: 14px; color: #6c757d; margin: 20px 0">
        Note: DNS propagation may take up to 15–30 minutes.
      </p>

      <div class="support-links">
        Need help? View DNS setup guides for
        <a href="#" onclick="showDnsGuide('godaddy')">GoDaddy</a>,
        <a href="#" onclick="showDnsGuide('namecheap')">Namecheap</a>,
        <a href="#" onclick="showDnsGuide('cloudflare')">Cloudflare</a>, and
        others.
      </div>

      <div id="dnsStatus" class="status-message" style="display: none"></div>

      <div style="text-align: center; margin-top: 30px">
        <button class="btn-primary-custom" id="createCnameBtn" onclick="createCnameRecord()">
          Create CNAME Record
        </button>
        <button class="btn-primary-custom" id="retryDnsBtn" onclick="checkDnsVerification()" disabled>
          Retry DNS Check
        </button>
      </div>
    </div>
  </div>

  <!-- Step 3: SSL Certificate -->
  <!-- <div class="step-card step-hidden" id="step3">
    <div class="step-header">
      <h2 class="step-title">Step 3: Securing Your Domain</h2>
      <p class="step-subtitle">Setting up SSL certificate</p>
    </div>
    <div class="step-body">
      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin-bottom: 20px;
        "
      >
        We are now issuing an SSL certificate to secure your custom domain with
        HTTPS. This usually takes a few seconds.
      </p>

      <div id="sslStatus" class="status-message" style="display: none"></div>

      <div style="text-align: center; margin-top: 30px">
        <button class="btn-primary-custom" onclick="issueSslCertificate()">
          Issue SSL Certificate
        </button>
      </div>
    </div>
  </div> -->

  <!-- Step 3: Final Confirmation -->
  <div class="step-card step-hidden" id="step3">
    <div class="step-header">
      <h2 class="step-title">Step 4: Final Confirmation</h2>
      <p class="step-subtitle">Complete your domain setup</p>
    </div>
    <div class="step-body">
      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin-bottom: 20px;
        "
      >
        Your custom domain is now live and secured.
      </p>

      <div class="domain-status-grid">
        <div class="status-item">
          <div class="status-item-label">Mapped Domain</div>
          <div class="status-item-value" id="finalDomain">
            certificates.yourcompany.com
          </div>
        </div>
     
        <div class="status-item">
          <div class="status-item-label">DNS Status</div>
          <div class="status-item-value">Verified</div>
        </div>
      </div>

     

      <div style="text-align: center">
        <button class="btn-primary-custom" onclick="finalizeSetup()">
          Finalize Setup
        </button>
       
      </div>
    </div>
  </div>

  <!-- Step 4: Live Domain Dashboard -->
  <div class="step-card step-hidden" id="step4">
    <div class="step-header">
      <h2 class="step-title">Your Domain Configuration</h2>
      <p class="step-subtitle">Domain setup complete</p>
    </div>
    <div class="step-body">
      <p
        style="
          font-size: 16px;
          line-height: 1.6;
          color: #495057;
          margin-bottom: 20px;
        "
      >
        Your portal is currently live at:
        <strong id="liveDomain">https://certificates.yourcompany.com</strong>
      </p>
      <p style="font-size: 14px; color: #6c757d; margin-bottom: 25px">
        This is your public-facing domain for certificates, verification, and
        sharing.
      </p>

      <div class="domain-status-grid">
        <div class="status-item">
          <div class="status-item-label">Domain</div>
          <div class="status-item-value" id="dashboardDomain">
            certificates.yourcompany.com
          </div>
        </div>
        <div class="status-item">
          <div class="status-item-label">DNS</div>
          <div class="status-item-value">✅ Verified</div>
        </div>
       
      </div>

    
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>


<script>
  let currentStep = 0;
  const totalSteps = 4;
  let cnameRecordCreated = false;

  function updateProgress() {
    const progress = ((currentStep + 1) / totalSteps) * 100;
    $("#progressFill").css("width", progress + "%");
  }

  function goToStep(step) {
    $(".step-card").addClass("step-hidden");
    $("#step" + step).removeClass("step-hidden");
    currentStep = step;
    updateProgress();
  }

  function showStatus(elementId, message, type) {
    const $statusElement = $("#" + elementId);
    $statusElement.show().removeClass().addClass("status-message status-" + type);

    let icon = "";
    switch (type) {
      case "waiting":
        icon = '<div class="loading-spinner"></div>';
        break;
      case "success":
        icon = "✅";
        break;
      case "error":
        icon = "❌";
        break;
    }

    $statusElement.html(icon + " " + message);
  }

  // Check domain availability
  function checkDomainAvailability() {
    const domain = $("#customDomain").val();
    if (!domain) {
      showStatus("domainStatus", "Please enter a domain name", "error");
      return;
    }
    showStatus("domainStatus", "Checking availability...", "waiting");
    
    $.ajax({
      url: "/api/domain/check",
      type: "POST",
      data: { domain: domain },
      success: function (response) {
        console.log(response);
        if (response.isAvailable && response.isValid) {
          showStatus("domainStatus", "Domain is valid and available.", "success");
          const subdomain = extractSubdomain(domain);
          $("#subdomainDisplay").text(subdomain);
          $("#subdomainValue").val(subdomain);
          $("#dnsTarget").text(subdomain + ".mixcommerce.co");
          goToStep(2);
        } else {
          showStatus("domainStatus", "This domain is already mapped by another account.", "error");
        }
      },
      error: function (error) {
        console.log(error);
        showStatus("domainStatus", "This domain is already mapped by another account.", "error");
      },
    });
  }

  function extractSubdomain(fullDomain) {
    const parts = fullDomain.split(".");
    if (parts.length <= 2) return "";
    return parts.slice(0, parts.length - 2).join(".");
  }

  // Create CNAME record
  function createCnameRecord() {
    const subdomain = $("#subdomainValue").val();
    if (!subdomain) {
      showStatus("dnsStatus", "Subdomain not found. Please go back to step 1.", "error");
      return;
    }

    showStatus("dnsStatus", "Creating CNAME record...", "waiting");
    
    $.ajax({
      url: "/api/domain/subdomains",
      type: "POST",
      data: {
        name: subdomain,
        content: 'harvard' + ".mixcommerce.co",
        type: "CNAME",
        ttl: 3600,
        proxied: false
      },
      success: function (response) {
        console.log("CNAME record created:", response);
        showStatus("dnsStatus", "CNAME record created successfully!", "success");
        cnameRecordCreated = true;
        $("#createCnameBtn").prop("disabled", true).text("CNAME Record Created");
        $("#retryDnsBtn").prop("disabled", false);
      },
      error: function (error) {
        console.log("CNAME creation error:", error);
        showStatus("dnsStatus", "Failed to create CNAME record or may be the record exists. Please try again.", "error");
      },
    });
  }

  // Check DNS verification
  function checkDnsVerification() {
    if (!cnameRecordCreated) {
      showStatus("dnsStatus", "Please create CNAME record first.", "error");
      return;
    }

    const subdomain = $("#subdomainValue").val();
    const fullDomain = `${subdomain}.housemirror.com`;
    if (!subdomain) {
      showStatus("dnsStatus", "Subdomain not found. Please go back to step 1.", "error");
      return;
    }

    showStatus("dnsStatus", "Waiting for DNS verification...", "waiting");

    $.ajax({
      url: "/api/domain/verify",
      method: "POST",
      data: { domain: fullDomain },
      success: function (response) {
        if (response.cnameFound) {
          showStatus("dnsStatus", "DNS record found!", "success");
           $("#finalDomain").text( subdomain + ".mixcommerce.co")
          goToStep(3);
        } else {
          showStatus("dnsStatus", "We couldn't detect the CNAME. Please double-check your settings", "error");
        }
      },
      error: function (error) {
        showStatus("dnsStatus", "DNS record not found!", "error");
      },
    });
  }

  // function issueSslCertificate() {
  //   const subdomain = $("#subdomainValue").val();
  //   const fullDomain = `${subdomain}.housemirror.com`;
  //   showStatus("sslStatus", "Issuing SSL certificate...", "waiting");

  //   $.ajax({
  //     url:"/api/domain/ssl",
  //     method:"POST",
  //     data:{domain:fullDomain},
  //     success:function(response){
  //       console.log(response);
  //       showStatus("sslStatus", "SSL certificate successfully issued.", "success");
  //        goToStep(4)
  //     },
  //     error:function(error){
  //       console.log(error);
  //       showStatus("sslStatus", "SSL issuance failed. Please try again or contact support.", "error");
  //     }
  //   })

   
    
  // }

  function finalizeSetup() {
    const domain = $("#customDomain").val();
     function extractSubdomain() {
    const parts = domain.split(".");
    if (parts.length <= 2) return "";
    return parts.slice(0, parts.length - 2).join(".");
  }
  const subDomain=extractSubdomain()
    const setAsDefault = $("#setAsDefault").is(":checked");

    // Update final domain display
    $("#finalDomain").text(subDomain+ ".mixcommerce.co");
    $("#liveDomain").text("https://" + subDomain + ".mixcommerce.co" );
    $("#dashboardDomain").text(subDomain+ ".mixcommerce.co");

    goToStep(4);
  }

  function revertToDefault() {
    if (confirm("Are you sure you want to revert to the default domain?")) {
      goToStep(0);
    }
  }

  function switchToDefault() {
    if (confirm("Are you sure you want to switch back to the default domain?")) {
      goToStep(0);
    }
  }

  function showDnsGuide(provider) {
    // Mock function to show DNS guide
    alert("Opening " + provider + " DNS setup guide...");
  }

  // Initialize
  $(document).ready(function() {
    updateProgress();
  });
</script>
