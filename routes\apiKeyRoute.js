const express = require("express");
const router = express.Router();
const apiKeyController = require("../controllers/apiKeyController.js");
const { checkPermissions } = require("../middleware/middleware.js");

router.post(
  "/",
  checkPermissions("ApiKey", ["create"]),
  apiKeyController.createApiKey
);
router.get(
  "/",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getApiKeys
);
router.get(
  "/:id",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getApiKeyById
);
router.put(
  "/:id",
  checkPermissions("ApiKey", ["update"]),
  apiKeyController.updateApiKey
);
router.delete(
  "/:id",
  checkPermissions("ApiKey", ["delete"]),
  apiKeyController.deleteApiKey
);

module.exports = router;
