const express = require("express");
const router = express.Router();
const apiKeyController = require("../controllers/apiKeyController.js");
const { checkPermissions } = require("../middleware/middleware.js");

router.post(
  "/",
  checkPermissions("ApiKey", ["create"]),
  apiKeyController.createApiKey
);
// Stats route must come before /:id route to avoid conflicts
router.get(
  "/all/stats",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getApiKeyStats
);

router.get(
  "/",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getApiKeys
);
router.get(
  "/:id",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getApiKeyById
);
router.put(
  "/:id",
  checkPermissions("ApiKey", ["update"]),
  apiKeyController.updateApiKey
);
router.delete(
  "/:id",
  checkPermissions("ApiKey", ["delete"]),
  apiKeyController.deleteApiKey
);

router.patch(
  "/revoke/:id",
  checkPermissions("ApiKey", ["delete"]),
  apiKeyController.revokeApiKey
);

router.post(
  "/send-otp",
  checkPermissions("ApiKey", ["update"]),
  apiKeyController.sendOtpViaEmail
);

router.post(
  "/verify-otp",
  checkPermissions("ApiKey", ["update"]),
  apiKeyController.verifyOtp
);

router.get(
  "/reset-tobe-shown",
  checkPermissions("ApiKey", ["update"]),
  apiKeyController.resetToBeShownApiKey
);

router.get(
  "/secure-view/:id",
  checkPermissions("ApiKey", ["read"]),
  apiKeyController.getSecureApiKey
);

module.exports = router;

// module.exports = async function (req, res, next) {
//   const key = req.headers["x-api-key"];
//   if (!key) return res.status(401).json({ message: "API key required" });

//   const apiKey = await ApiKey.findOne({ key });

//   if (!apiKey) return res.status(403).json({ message: "Invalid API key" });

//   // Check expiry
//   if (apiKey.expiresAt && apiKey.expiresAt <= new Date()) {
//     apiKey.status = "expired";
//     await apiKey.save();
//     return res.status(403).json({ message: "API key expired" });
//   }

//   // Update last used
//   apiKey.lastUsedAt = new Date();
//   await apiKey.save();

//   req.apiKey = apiKey;
//   next();
// };
