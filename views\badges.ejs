<%- contentFor('HeaderCss') %>
<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>
<%-include("partials/title-meta", { "title": "Badges" }) %>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- Dropzone CSS -->
<link href="https://cdn.jsdelivr.net/npm/dropzone@5.9.3/dist/dropzone.css" rel="stylesheet">
<!-- Dropzone JS -->
<script src="https://cdn.jsdelivr.net/npm/dropzone@5.9.3/dist/dropzone.js"></script>
<style>
    .badge-img {
        width: 300px;
        height: 300px;
        object-fit: cover;
    }
    .dz-preview .dz-error-message {
        display: none !important;
    }
</style>
<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Badges" , "pagetitle": "All" }) %>


 <!-- end row -->
<hr class="py-1">
 
    <!-- Add Badge Button -->
     <div class="d-flex justify-content-end gap-2">
         <button id="badgeModalBtn" class="btn btn-light my-2" data-bs-toggle="modal" data-bs-target="#badgeModal">Add New Badge</button>
         <a href="/bulk-design-generator/" class="btn btn-light my-2">Bulk Generate</a>

     </div>
<div class="table-responsive mb-0">
<table id="badgeTable" class="table table-striped table-bordered nowrap" style="width:100%">
    <thead>
        <tr>
            <th>Image</th>
            <th>Title</th>
            <th>Description</th>
            <th>Date Issued</th>
            <th>Date Expiry</th>
            <th>Skill</th>
            <th>Verification Status</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <!-- Rows with badge data will be appended here dynamically -->
    </tbody>
   
</table class="table table-striped">
    <!-- Pagination -->
    <div class="row mt-4">
        <div class="col-12">
          <nav aria-label="Certificate template pagination">
            <ul
              class="pagination justify-content-center align-items-center"
              id="pagination"
            >
            
              <!-- Pagination will be dynamically inserted here -->
            </ul>
          </nav>
        </div>
      </div>
      <div id="loadingIndicator" style=" text-align: center; margin: 20px;">
        <span class="spinner-border text-primary"></span>
    </div>   
</div>

<!-- Badge Modal -->
<div class="modal fade" id="badgeModal" tabindex="-1" aria-labelledby="badgeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="badgeModalLabel">Add/Edit Badge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="badgeForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="skill" class="form-label">Skill</label>
                        <input type="text" class="form-control" id="skill" name="skill" required>
                    </div>
                    <div class="mb-3">
                        <label for="dateOfIssue" class="form-label">Date of Issue</label>
                        <input type="date" class="form-control" id="dateOfIssue" name="dateOfIssue" required>
                    </div>
                    <div class="mb-3">
                        <label for="dateOfExpiry" class="form-label">Date of Expiry</label>
                        <input type="date" class="form-control" id="dateOfExpiry" name="dateOfExpiry">
                    </div>
                    <div class="mb-3">
                        <label for="badgeFile" class="form-label">Badge Image</label>
                        <div id="dropzone" class="dropzone"></div>
                    </div>
                    <div class="mb-3">
                        <input type="text" id="badgeURL" class="d-none">
                        <input type="text" id="badgeId" class="d-none">
                        
                    </div>
                    <button type="submit" class="btn btn-primary">Save Badge</button>
                </form>
            </div>
        </div>
    </div>

</div>
</div>


 
<%- contentFor('FooterJs') %>


<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>

<script>
    
    let totalPages=1;
    const itemsPerPage = 4;
    let nameSpaceCode='';
    Dropzone.autoDiscover = false;
      $.ajax({
    url: "/api/users/profile",
    method: "GET",
    success: function (response) {
      if (response && response.businessId) {
        fetchNameSpaceFromBusinessData(response.businessId);
      }
    },
    error: function (error) {
      console.log(error);
    },
  });
  function fetchNameSpaceFromBusinessData(businessId) {
    $.ajax({
      url: "/api/business/" + businessId,
      method: "GET",
      success: function (response) {
        if (response && response.nameSpaceCode) {
          nameSpaceCode = response.nameSpaceCode;
           loadBadges();
          
        }
      },
      error: function (error) {
        console.log(error);
      },
    });
  }

    $("#badgeModalBtn").on("click",function(){
        $("#title").val(''),
            $("#description").val(''),
           $("#skill").val(''),
             $("#dateOfIssue").val(''),
         $("#dateOfExpiry").val(''),
         $("#dropzone").text('')
            
        $("badgeModal").show()
    })

    const myDropzone = new Dropzone("#dropzone", {
        url: "/api/files/upload/", // Set the URL to your server's upload handler
        autoProcessQueue: false,
        acceptedFiles: "image/*",
        maxFiles: 1,
        addRemoveLinks: true,

        init: function () {
            this.on("error", function (file, message) {
                showToast(message,"danger");
                this.removeFile(file);
            });
        }
    });


    //handle save button click
    $("#badgeForm").on("submit", function (e) {
    e.preventDefault();

    // Get existing badge URL and ID
    const existingBadgeURL = $("#badgeURL").val(); // Hidden field to store the existing badge URL
    const existingBadgeId = $("#badgeId").val(); // Hidden field to store the existing badge ID

    if (myDropzone.getQueuedFiles().length > 0) {
        // If there are files in the queue, process them
        myDropzone.on("success", function (file, response) {
            // When upload is successful, save or update badge based on ID
            if (existingBadgeId) {
                updateBadge(response.file.url, existingBadgeId);
            } else {
                console.log('called');
                console.log(response.file.url);
                
                saveBadge(response.file.url);
            }
        });
        myDropzone.processQueue();
    } else {
        // If no files in the queue, directly save or update badge based on ID
        if (existingBadgeId) {
            updateBadge(existingBadgeURL, existingBadgeId);
        } else {
            saveBadge(existingBadgeURL);
        }
    }
});


    function saveBadge(badgeURL = '') {
        const badgeData = {
             title:  DOMPurify.sanitize($("#title").val()),
            description: DOMPurify.sanitize($("#description").val()),
            skill:  DOMPurify.sanitize($("#skill").val()),
            dateOfIssue: $("#dateOfIssue").val(),
            dateOfExpiry: $("#dateOfExpiry").val(),
            entityType: "Badge",
            badgeURL: badgeURL // Pass the uploaded or existing badge URL
        };

        $.ajax({
            type: "POST",
            url: "/api/badges/",
            data: badgeData,
            success: function (response) {
                $("#badgeModal").modal('hide');
                showToast("Badge saved successfully","primary");
                loadBadges();
            },
            error: function (error) {
                console.error(error);
                showToast("All fields are required","danger");
            }
        });
    }
    function updateBadge(badgeURL = '',existingBadgeId) {
        const badgeData = {
            title:  DOMPurify.sanitize($("#title").val()),
            description: DOMPurify.sanitize($("#description").val()),
            skill:  DOMPurify.sanitize($("#skill").val()),
            dateOfIssue: $("#dateOfIssue").val(),
            dateOfExpiry: $("#dateOfExpiry").val(),
            entityType: "Badge",
            badgeURL: badgeURL // Pass the uploaded or existing badge URL
        };
        console.log(badgeData,'badgeData')

        $.ajax({
            type: "PUT",
            url: "/api/badges/"+existingBadgeId,
            data: badgeData,
            success: function (response) {
            //    console.log(response);

            // Clear the Dropzone preview after the badge is updated
            const dropzoneInstance = Dropzone.forElement("#dropzone");
            dropzoneInstance.removeAllFiles(true); // Remove all files including previews
           


                $("#badgeModal").modal('hide');
                
                loadBadges();
                showToast("Badge updated successfully","primary")
                $("#badgeForm")[0].reset();

                
            },
            error: function (error) {
                console.error(error);
            }
        });
    }

    function loadBadges() {
        $("#loadingIndicator").show();
        $.ajax({
            type: "GET",
            url: `/api/badges?page=${currentPage}&limit=${itemsPerPage}`,
         success: function (result) {
            const{badges,total}=result;
           
                const badgeTableBody = $("#badgeTable tbody");
                badgeTableBody.empty(); // Clear existing rows

                badges.forEach(badge => {
                    badgeTableBody.append(`
                        <tr>
                            <td><img src="${badge.badgeURL}" class="badge-img" style="width: 50px; height: 50px;"></td>
                            <td><a href="/badges/${badge._id}">${badge.title}</a></td>
                            <td>${badge.description}</td>
                            <td>${new Date(badge.dateOfIssue).toLocaleDateString()}</td>
                            <td>${new Date(badge.dateOfExpiry).toLocaleDateString()}</td>
                            <td>${badge.skill}</td>
                            <td>${badge.verified ? 'Verified' : 'Not Verified'}</td>
                            <td>
                                <a href="/${nameSpaceCode}/badge/${badge._id}" target="_blank" class="btn btn-sm mt-2 btn-primary">View</a>
                                <button class="btn btn-sm mt-2 btn-primary" onclick="editBadge('${badge._id}')">Edit</button>
                                <button class="btn btn-sm mt-2 btn-primary" onclick="deleteBadge('${badge._id}')">Delete</button>
                            </td>
                        </tr>
                    `);
                });
                
                totalPages = Math.ceil(total / itemsPerPage);
                updatePagination()
                $("#loadingIndicator").hide();
                
                // $('#badgeTable').DataTable().clear().destroy(); 
                // $('#badgeTable').DataTable({
                //     responsive: true
                // });
            },
            error: function (error) {
                console.error(error);
                $("#loadingIndicator").hide();
            }
        });
    }

         // Update pagination controls
        function updatePagination() {
        const pagination = $("#pagination");
       pagination.empty();
 
      // Previous button
        pagination.append(`
        <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
            <a class="page-link hover" href="#" data-page="${
              currentPage - 1
            }">Previous</a>
        </li>
    `);

      pagination.append(`
        <li class="ms-2 me-2">${currentPage} of ${totalPages}</li>
    `);

      // Next button
      pagination.append(`
        <li class="page-item ${currentPage === totalPages ? "disabled" : ""}">
            <a class="page-link hover" href="#" data-page="${
              currentPage + 1
            }">Next</a>
        </li>
    `);

    console.log(totalPages)
      // Add click handlers
      pagination.find(".page-link").on("click", function (e) {
        e.preventDefault();
        const newPage = $(this).data("page");
        if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
          currentPage = newPage;
          loadBadges();
        }
      });
    }


    function editBadge(id) {
        $.ajax({
            type: "GET",
            url: `/api/badges/${id}`,
            success: function (badge) {
                $("#title").val(badge.title);
                $("#description").val(badge.description);
                $("#skill").val(badge.skill);
                $("#dateOfIssue").val(badge.dateOfIssue.split('T')[0]);
                $("#dateOfExpiry").val(badge.dateOfExpiry.split('T')[0]);
                $("#badgeURL").val(badge.badgeURL); // Store the existing badge URL
                $("#badgeId").val(badge._id); // Store the existing badge URL

                const dropzoneInstance = Dropzone.forElement("#dropzone");
                dropzoneInstance.removeAllFiles(); // Clear existing files
                // Set the existing badge image in Dropzone
                const mockFile = { name: badge.badgeURL.split('/').pop(), size: 12345 }; // Adjust size if you have actual data
                dropzoneInstance.emit("addedfile", mockFile);
                dropzoneInstance.emit("thumbnail", mockFile, badge.badgeURL);
                dropzoneInstance.emit("complete", mockFile);
                dropzoneInstance.files.push(mockFile); // Add the file to Dropzone's files array

                // Show the modal
              
                $("#badgeModal").modal('show');
            },
            error: function (error) {
                console.error(error);
            }
        });
    }

    function deleteBadge(id) {
        if (confirm("Are you sure you want to delete this badge?")) {
            $.ajax({
                type: "DELETE",
                url: `/api/badges/${id}`,
                success: function (response) {
                    loadBadges();
                    showToast("Badge deleted successfully","danger")
                },
                error: function (error) {
                    console.error(error);
                }
            });
        }
    }


</script>

 