<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title": "Import Contact - MixCertificate" }) %>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" />

<style>
  .import-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
  }

  .import-steps::before {
    content: '';
    position: absolute;
    top: 24px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
  }

  .step {
    position: relative;
    z-index: 2;
    background: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid #e9ecef;
    color: #6c757d;
  }

  .step-label {
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    font-size: 0.85rem;
    font-weight: 500;
    color: #6c757d;
  }

  .step.active {
    background: #5156be;
    color: #fff;
    border-color: #5156be;
  }

  .step.active .step-label {
    color: #5156be;
    font-weight: 600;
  }

  .step.completed {
    background: #5156be;
    color: #fff;
    border-color: #5156be;
  }

  .step-content {
    display: none;
  }

  .step-content.active {
    display: block;
  }

  .dropzone {
    border: 2px dashed #5156be;
    border-radius: 8px;
    background: rgba(81, 86, 190, 0.05);
    min-height: 150px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .dropzone:hover {
    background: rgba(81, 86, 190, 0.1);
  }

  .dropzone .dz-message {
    margin: 0;
    text-align: center;
  }

  .dropzone .dz-message .dz-button {
    background: none;
    color: #5156be;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
  }

  .mapping-table {
    width: 100%;
    border-collapse: collapse;
  }

  .mapping-table th, .mapping-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
  }

  .mapping-table th {
    background-color: #f8f9fa;
    font-weight: 500;
  }

  .preview-table {
    width: 100%;
    border-collapse: collapse;
  }

  .preview-table th, .preview-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }

  .preview-table th {
    background-color: #f8f9fa;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .preview-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
  }

  .duplicate-row {
    background-color: rgba(255, 193, 7, 0.1);
  }

  .btn-primary {
    background-color: #5156be;
    border-color: #5156be;
  }

  .btn-primary:hover {
    background-color: #4347a1;
    border-color: #4347a1;
  }

  .step-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
  }

  .sample-fields {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  .sample-fields ul {
    margin-bottom: 0;
    padding-left: 0;
  }

  .sample-fields .required {
    font-weight: 600;
    color: #dc3545;
  }

  .sample-fields .optional {
    font-weight: normal;
    color: #6c757d;
  }

  .sample-fields li {
    margin-bottom: 6px;
    line-height: 1.4;
  }

  .sample-fields .fw-bold {
    color: #5156be;
    font-size: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 5px;
  }
</style>

<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Import Contact" , "pagetitle": "MixCertificate" }) %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <!-- Steps Indicator -->
          <div class="import-steps">
            <div class="step active" id="step1-indicator">
              1
              <span class="step-label">Overview</span>
            </div>
            <div class="step" id="step2-indicator">
              2
              <span class="step-label">Upload CSV</span>
            </div>
            <div class="step" id="step3-indicator">
              3
              <span class="step-label">Map Fields</span>
            </div>
            <div class="step" id="step4-indicator">
              4
              <span class="step-label">Custom Variables</span>
            </div>
            <div class="step" id="step5-indicator">
              5
              <span class="step-label">Import</span>
            </div>
          </div>

          <!-- Step 1: Download Sample CSV -->
          <div class="step-content active pt-4" id="step1-content">
            <h4 class="mb-4">Step 1: Download Sample CSV</h4>
            <p>Download our sample CSV file to see the required format for importing contacts.</p>


            <ol class="mb-4">
              <li>
                <a href="https://www.mixcommerce.co/wp-content/uploads/2025/05/mixcertificate-contact-import-sample-format.csv" download>
                  <i class="bx bx-download me-1"></i> Download Sample CSV
                </a>
              </li>
              <li>Fill the data (mandatory fields, custom variable)</li>
              <li>Upload the data</li>
            </ol>



            <div class="step-buttons">
              <div></div> <!-- Empty div for spacing -->
              <button type="button" class="btn btn-primary next-step" data-current="1" data-next="2">
                Next <i class="bx bx-right-arrow-alt ms-1"></i>
              </button>
            </div>

          </div>

          <!-- Step 2: Upload CSV File -->
          <div class="step-content pt-4" id="step2-content">
            <h4 class="mb-4">Step 2: Upload Your CSV File</h4>
            <p>Upload your completed CSV file with contact information.</p>

            <form action="/contacts/import/upload" method="post" enctype="multipart/form-data" class="dropzone" id="csvDropzone">
              <div class="dz-message">
                <div class="mb-3">
                  <i class="bx bx-cloud-upload" style="font-size: 3rem; color: #5156be;"></i>
                </div>
                <h5>Drop your file here or click to browse</h5>
                <p class="text-muted">Accepted format: .csv</p>
              </div>
            </form>

            <div id="upload-error" class="alert alert-danger mt-3 d-none">
              <i class="bx bx-error-circle me-1"></i>
              <span id="error-message">Error uploading file. Please try again.</span>
            </div>

            <div class="step-buttons">
              <button type="button" class="btn btn-light prev-step" data-current="2" data-prev="1">
                <i class="bx bx-left-arrow-alt me-1"></i> Previous
              </button>
              <button type="button" class="btn btn-primary next-step" data-current="2" data-next="3" id="upload-next-btn" disabled>
                Next <i class="bx bx-right-arrow-alt ms-1"></i>
              </button>
            </div>
          </div>

          <!-- Step 3: Map Fields -->
          <div class="step-content pt-4" id="step3-content">
            <h4 class="mb-4">Step 3: Map Your CSV Columns to Contact Fields</h4>
            <p>Match your CSV columns with our contact fields to ensure data is imported correctly.</p>

            <div class="table-responsive">
              <table class="mapping-table">
                <thead>
                  <tr>
                    <th>CSV Column</th>
                    <th>Contact Field</th>
                    <th>Sample Data</th>
                  </tr>
                </thead>
                <tbody id="mapping-fields">
                  <!-- Mapping fields will be populated dynamically -->
                </tbody>
              </table>
            </div>

            <div class="step-buttons">
              <button type="button" class="btn btn-light prev-step" data-current="3" data-prev="2">
                <i class="bx bx-left-arrow-alt me-1"></i> Previous
              </button>
              <button type="button" class="btn btn-primary next-step" data-current="3" data-next="4" id="mapping-next-btn">
                Next <i class="bx bx-right-arrow-alt ms-1"></i>
              </button>
            </div>
          </div>

          <!-- Step 4: Custom Variables -->
          <div class="step-content pt-4" id="step4-content">
            <h4 class="mb-4">Step 4: Map Custom Variables</h4>


            <div class="alert alert-info mb-4">
              <div class="d-flex">
                <i class="bx bx-info-circle me-3" style="font-size: 1.5rem;"></i>
                <div>
                  <p class="mb-2"><strong>How to use custom variables:</strong></p>
                  <ul class="mb-0">
                    <li>Custom variables are stored in the contact's "variables" map</li>
                    <li>They can be used in email templates, certificates, and other communications</li>
                    <li>CSV columns with names starting with "var_" will be automatically detected</li>
                    <li>You can also manually add custom variables below</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="card mb-4">
              <div class="card-header bg-light">
                <h5 class="mb-0">Detected Custom Variables</h5>
              </div>
              <div class="card-body">
                <div id="detected-variables-container">
                  <p id="no-variables-message" class="text-muted">No custom variables detected in your CSV. Custom variables should have column names prefixed with "var_".</p>
                  <div id="detected-variables-table-container" class="table-responsive d-none">
                    <table class="table table-bordered" id="detected-variables-table">
                      <thead>
                        <tr>
                          <th>CSV Column</th>
                          <th>Variable Name</th>
                          <th>Sample Value</th>
                        </tr>
                      </thead>
                      <tbody id="detected-variables-list">
                        <!-- Will be populated dynamically -->
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <div class="card mb-4">
              <div class="card-header bg-light">
                <h5 class="mb-0">Add Custom Variables</h5>
              </div>
              <div class="card-body">
                <p>Add additional custom variables that will be applied to all imported contacts.</p>
                <div class="row mb-3">
                  <div class="col-md-5">
                    <input type="text" class="form-control" id="new-variable-name" placeholder="Variable Name">
                  </div>
                  <div class="col-md-5">
                    <input type="text" class="form-control" id="new-variable-value" placeholder="Variable Value">
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-primary w-100" id="add-variable-btn">
                      <i class="bx bx-plus me-1"></i> Add
                    </button>
                  </div>
                </div>

                <div id="manual-variables-container">
                  <p id="no-manual-variables-message" class="text-muted">No custom variables added yet.</p>
                  <div id="manual-variables-table-container" class="table-responsive d-none">
                    <table class="table table-bordered" id="manual-variables-table">
                      <thead>
                        <tr>
                          <th>Variable Name</th>
                          <th>Variable Value</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody id="manual-variables-list">
                        <!-- Will be populated dynamically -->
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <div class="step-buttons">
              <button type="button" class="btn btn-light prev-step" data-current="4" data-prev="3">
                <i class="bx bx-left-arrow-alt me-1"></i> Previous
              </button>
              <button type="button" class="btn btn-primary next-step" data-current="4" data-next="5" id="variables-next-btn">
                Next <i class="bx bx-right-arrow-alt ms-1"></i>
              </button>
            </div>
          </div>

          <!-- Step 5: Review & Confirm -->
          <div class="step-content pt-4" id="step5-content">
            <h4 class="mb-4">Step 5: Review & Confirm</h4>
            <p>Preview your contacts before confirming the import. Duplicates are identified via the business email.</p>

            <div class="alert alert-info mb-3">
              <div class="d-flex align-items-center">
                <i class="bx bx-info-circle me-2" style="font-size: 1.5rem;"></i>
                <div>
                  <strong>Preview showing first 5 contacts</strong><br>
                  <span id="total-contacts">0</span> contacts will be imported. <span id="duplicate-contacts">0</span> potential duplicates found.
                </div>
              </div>
            </div>

            <div class="preview-container">
              <table class="preview-table">
                <thead id="preview-header">
                  <!-- Preview header will be populated dynamically -->
                </thead>
                <tbody id="preview-data">
                  <!-- Preview data will be populated dynamically -->
                </tbody>
              </table>
            </div>

            <div class="step-buttons">
              <button type="button" class="btn btn-light prev-step" data-current="5" data-prev="4">
                <i class="bx bx-left-arrow-alt me-1"></i> Previous
              </button>
              <button type="button" class="btn btn-success" id="import-btn">
                <i class="bx bx-check me-1"></i> Import Now
              </button>
            </div>
          </div>

          <!-- Success Message (Hidden by default) -->
          <div class="step-content" id="success-content">
            <div class="text-center py-5">
              <div class="mb-4">
                <i class="bx bx-check-circle" style="font-size: 5rem; color: #28a745;"></i>
              </div>
              <h3>Import Successful!</h3>
              <p class="mb-4"><span id="imported-count">0</span> contacts have been imported.</p>
              <p class="mb-4">
                <span id="ignored-count">0</span> contacts have been ignored
                <span id="ignore-reason" class="text-danger fw-bold d-none">(duplicate business email)</span>.
              </p>
              <div>
                <a href="/contacts" class="btn btn-primary me-2">View Contacts</a>
                <button type="button" class="btn btn-light" id="import-another-btn">Import Another File</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<!-- Make sure we're only loading Dropzone once -->
<% if (typeof dropzoneLoaded === 'undefined') { %>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
<% } %>
<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
<script>
  // Ensure Dropzone doesn't auto-discover
  if (typeof Dropzone !== 'undefined') {
    Dropzone.autoDiscover = false;
  }
</script>

<script>
  $(document).ready(function() {
    // Variables to store data
    let csvData = null;
    let csvHeaders = [];
    let mappedFields = {};
    let uploadedFileName = '';
    let duplicateEmails = [];
    let detectedVariables = {}; // Store detected custom variables from CSV
    let manualVariables = {}; // Store manually added custom variables

    // Required fields
    const requiredFields = ['firstName', 'lastName', 'businessEmail'];

    // Available contact fields for mapping
    const contactFields = [
      { value: 'firstName', label: 'First Name', required: true },
      { value: 'lastName', label: 'Last Name', required: true },
      { value: 'businessEmail', label: 'Business Email', required: true },
      { value: 'personalEmail', label: 'Personal Email', required: false },
      { value: 'fullName', label: 'Full Name', required: false },
      { value: 'jobTitle', label: 'Job Title', required: false },
      { value: 'phone', label: 'Phone', required: false },
      { value: 'personalPhone', label: 'Personal Phone', required: false },
      { value: 'companyName', label: 'Company Name', required: false },
      { value: 'companySize', label: 'Company Size', required: false },
      { value: 'linkedin', label: 'LinkedIn', required: false },
      { value: 'twitter', label: 'Twitter', required: false },
      { value: 'facebook', label: 'Facebook', required: false },
      { value: 'website', label: 'Website', required: false },
      { value: 'blog', label: 'Blog', required: false },
      { value: 'city', label: 'City', required: false },
      { value: 'state', label: 'State', required: false },
      { value: 'country', label: 'Country', required: false },
      { value: 'serviceInterest', label: 'Service Interest', required: false },
      { value: 'industry', label: 'Industry', required: false },
      { value: 'source', label: 'Source', required: false },
      { value: 'referral', label: 'Referral', required: false },
      { value: 'preferredContactMethod', label: 'Preferred Contact Method', required: false },
      { value: 'tags', label: 'Tags (comma-separated)', required: false },
      { value: 'notes', label: 'Notes', required: false },
      { value: 'likes', label: 'Likes', required: false },
      { value: 'contactStatus', label: 'Contact Status', required: false }
    ];

    // Make sure Dropzone is not auto-discovering
    if (typeof Dropzone !== 'undefined') {
      Dropzone.autoDiscover = false;
    }

    // Check if Dropzone is already attached to the element
    let myDropzone;
    if ($("#csvDropzone").hasClass("dz-clickable")) {
      // Dropzone is already attached, get the instance
      myDropzone = Dropzone.forElement("#csvDropzone");
      // Reset the dropzone
      myDropzone.removeAllFiles(true);
    } else {
      // Initialize new Dropzone instance
      myDropzone = new Dropzone("#csvDropzone", {
        url: "/api/contacts/import/upload",
        acceptedFiles: ".csv",
        maxFiles: 1,
        autoProcessQueue: true, // Automatically upload the file when added
        addRemoveLinks: true,
        dictRemoveFile: "Remove",
        timeout: 60000, // 1 minute timeout for large files
        paramName: "file", // The name that will be used to transfer the file (must match express-fileupload)
        uploadMultiple: false,
        createImageThumbnails: false,
        init: function() {
          this.on("addedfile", function(file) {

            // Enable the next button when a file is added
            $("#upload-next-btn").prop("disabled", false);
            uploadedFileName = file.name;

            // Parse the CSV file
            Papa.parse(file, {
              header: true,
              skipEmptyLines: true,
              complete: function(results) {
                csvData = results.data;
                csvHeaders = results.meta.fields;
                console.log("CSV Headers:", csvHeaders);
                console.log("CSV Data:", csvData);
              }
            });
          });

          this.on("removedfile", function() {
            // Disable the next button when the file is removed
            $("#upload-next-btn").prop("disabled", true);
            csvData = null;
            csvHeaders = [];
          });

          this.on("error", function(file, errorMessage) {
            $("#upload-error").removeClass("d-none");
            $("#error-message").text(errorMessage);
            // Disable the next button on error
            $("#upload-next-btn").prop("disabled", true);
          });

          this.on("success", function(file, response) {
            console.log("File uploaded successfully:", response);
            // Store the file URL for later use
            file.serverData = response;

            // Store the response in the file object for later use
            if (typeof response === 'string') {
              try {
                file.serverData = JSON.parse(response);
              } catch (e) {
                console.error("Error parsing response:", e);
                console.error("Raw response:", response);
                // Show the actual error to user
                alert("Server error: " + response.substring(0, 200) + "...");
                file.serverData = { file: { url: null } };
                return;
              }
            } else {
              file.serverData = response;
            }

            console.log("File server data:", file.serverData);

            // Check if upload was actually successful
            if (!file.serverData.success || !file.serverData.file || !file.serverData.file.url) {
              console.error("Upload failed:", file.serverData);
              alert("Upload failed: " + (file.serverData.error || "Unknown error"));
              return;
            }

            // Enable the next button
            $("#upload-next-btn").prop("disabled", false);
          });

          this.on("error", function(file, errorMessage, xhr) {
            console.error("Upload error:", errorMessage);
            console.error("XHR status:", xhr ? xhr.status : "No XHR");
            console.error("XHR response:", xhr ? xhr.responseText : "No response");

            // Show detailed error message
            let displayMessage = "Upload failed: ";
            if (typeof errorMessage === 'string') {
              displayMessage += errorMessage;
            } else if (errorMessage && errorMessage.error) {
              displayMessage += errorMessage.error;
            } else if (xhr && xhr.responseText) {
              try {
                const errorResponse = JSON.parse(xhr.responseText);
                displayMessage += errorResponse.error || errorResponse.message || "Unknown error";
              } catch (e) {
                displayMessage += xhr.responseText.substring(0, 200) + "...";
              }
            } else {
              displayMessage += "Unknown error occurred";
            }

            alert(displayMessage);
            $("#upload-error").removeClass("d-none");
          });

          this.on("complete", function(file) {
            if (file.status === "error") {
              // If there was an error, show it
              $("#upload-error").removeClass("d-none");
            }
          });
        }
      });
    }

    // Navigation between steps
    $(".next-step").click(function() {
      const currentStep = parseInt($(this).data("current"));
      const nextStep = parseInt($(this).data("next"));

      // Validate before moving to next step
      if (currentStep === 2 && !csvData) {
        alert("Please upload a CSV file first.");
        return;
      }

      if (currentStep === 3) {
        // Validate mapping before moving to custom variables step
        const missingRequiredFields = validateMapping();
        if (missingRequiredFields.length > 0) {
          alert("Please map the following required fields: " + missingRequiredFields.join(", "));
          return;
        }
      }

      if (currentStep === 4) {
        // Generate preview data when moving from custom variables to review
        generatePreview();
      }

      // Hide current step, show next step
      $(`#step${currentStep}-content`).removeClass("active");
      $(`#step${nextStep}-content`).addClass("active");

      // Update step indicators
      $(`#step${currentStep}-indicator`).removeClass("active").addClass("completed");
      $(`#step${nextStep}-indicator`).addClass("active");
    });

    $(".prev-step").click(function() {
      const currentStep = parseInt($(this).data("current"));
      const prevStep = parseInt($(this).data("prev"));

      // Hide current step, show previous step
      $(`#step${currentStep}-content`).removeClass("active");
      $(`#step${prevStep}-content`).addClass("active");

      // Update step indicators
      $(`#step${currentStep}-indicator`).removeClass("active");
      $(`#step${prevStep}-indicator`).removeClass("completed").addClass("active");
    });

    // Generate mapping fields when moving to step 3
    $("#upload-next-btn").click(function() {
      if (csvHeaders.length > 0) {
        generateMappingFields();
      }
    });

    // Detect custom variables when moving to step 4
    $("#mapping-next-btn").click(function() {
      detectCustomVariables();
    });

    // Add custom variable button click handler
    $("#add-variable-btn").click(function() {
      addManualVariable();
    });

    // Enter key on variable value field should trigger add
    $("#new-variable-value").keypress(function(e) {
      if (e.which === 13) { // Enter key
        addManualVariable();
      }
    });

    // Function to generate mapping fields
    function generateMappingFields() {
      const mappingTable = $("#mapping-fields");
      mappingTable.empty();

      // Auto-map fields based on header names
      mappedFields = autoMapFields(csvHeaders);
      const headersWithoutCustomVariables = csvHeaders.filter(header=>!(header.toLowerCase().startsWith('var_')));

      // Generate mapping rows
      headersWithoutCustomVariables.forEach((header, index) => {
        const sampleData = csvData.length > 0 ? csvData[0][header] : '';
        const row = $("<tr>");

        // Check if this is a birthday field
        const isBirthdayField = header.toLowerCase() === 'birthday' ||
                               header.toLowerCase() === 'birth date' ||
                               header.toLowerCase() === 'birth_date' ||
                               header.toLowerCase() === 'dob';

        // CSV Column
        row.append($("<td>").text(header));

        // Contact Field dropdown
        const selectCell = $("<td>");
        const select = $("<select>").addClass("form-select").attr("data-csv-header", header);

        // Add empty option
        select.append($("<option>").val("").text("-- Select Field --"));

        // Add options for each contact field
        contactFields.forEach(field => {
          const option = $("<option>")
            .val(field.value)
            .text(field.label + (field.required ? ' *' : ''));

          // Set as selected if auto-mapped
          if (mappedFields[header] === field.value) {
            option.prop("selected", true);
          }

          select.append(option);
        });

        // Handle change event
        select.on("change", function() {
          const csvHeader = $(this).data("csv-header");
          const selectedValue = $(this).val();

          // Update mappedFields
          if (selectedValue) {
            mappedFields[csvHeader] = selectedValue;
          } else {
            delete mappedFields[csvHeader];
          }

          console.log("Updated mappings:", mappedFields);
        });

        selectCell.append(select);

        // If this is a birthday field, create a disabled dropdown with Birthday selected
        if (isBirthdayField) {
          // Clear the existing dropdown
          select.empty();

          // Add only the Birthday option and select it
          const birthdayOption = $("<option>")
            .val("birthday")
            .text("Birthday")
            .prop("selected", true);

          select.append(birthdayOption);

          // Disable the dropdown so user can't change it
          select.prop("disabled", true);

          // Set the mapping for birthday field
          mappedFields[header] = 'birthday';

          // Add a note about birthday format
          selectCell.append($("<div>").addClass("small text-muted mt-1").text("Format: DD-MM-YYYY"));
        }

        row.append(selectCell);

        // Sample Data
        row.append($("<td>").text(sampleData));

        mappingTable.append(row);
      });
    }

    // Auto-map fields based on header names
    function autoMapFields(headers) {
      const mapping = {};
      const headerMap = {
        'firstname': 'firstName',
        'first name': 'firstName',
        'first_name': 'firstName',
        'lastname': 'lastName',
        'last name': 'lastName',
        'last_name': 'lastName',
        'email': 'businessEmail',
        'businessemail': 'businessEmail',
        'business email': 'businessEmail',
        'business_email': 'businessEmail',
        'fullname': 'fullName',
        'full name': 'fullName',
        'full_name': 'fullName',
        'jobtitle': 'jobTitle',
        'job title': 'jobTitle',
        'job_title': 'jobTitle',
        'phone': 'phone',
        'phonenumber': 'phone',
        'phone number': 'phone',
        'phone_number': 'phone',
        'company': 'companyName',
        'companyname': 'companyName',
        'company name': 'companyName',
        'company_name': 'companyName',
        'companysize': 'companySize',
        'company size': 'companySize',
        'company_size': 'companySize',
        'linkedin': 'linkedin',
        'linkedin url': 'linkedin',
        'linkedin_url': 'linkedin',
        'twitter': 'twitter',
        'twitter handle': 'twitter',
        'twitter_handle': 'twitter',
        'facebook': 'facebook',
        'facebook url': 'facebook',
        'facebook_url': 'facebook',
        'website': 'website',
        'web': 'website',
        'url': 'website',
        'blog': 'blog',
        'blog url': 'blog',
        'blog_url': 'blog',
        'city': 'city',
        'state': 'state',
        'province': 'state',
        'country': 'country',
        'personalemail': 'personalEmail',
        'personal email': 'personalEmail',
        'personal_email': 'personalEmail',
        'personalphone': 'personalPhone',
        'personal phone': 'personalPhone',
        'personal_phone': 'personalPhone',
        'serviceinterest': 'serviceInterest',
        'service interest': 'serviceInterest',
        'service_interest': 'serviceInterest',
        'interest': 'serviceInterest',
        'industry': 'industry',
        'source': 'source',
        'lead source': 'source',
        'lead_source': 'source',
        'referral': 'referral',
        'referred by': 'referral',
        'referred_by': 'referral',
        'preferredcontactmethod': 'preferredContactMethod',
        'preferred contact method': 'preferredContactMethod',
        'preferred_contact_method': 'preferredContactMethod',
        'contact method': 'preferredContactMethod',
        'contact_method': 'preferredContactMethod',
        'tags': 'tags',
        'tag': 'tags',
        'categories': 'tags',
        'notes': 'notes',
        'note': 'notes',
        'comments': 'notes',
        'comment': 'notes',
        'likes': 'likes',
        'like': 'likes',
        'interests': 'likes',
        'contactstatus': 'contactStatus',
        'contact status': 'contactStatus',
        'contact_status': 'contactStatus',
        'status': 'contactStatus'
      };

      headers.forEach(header => {
        const lowerHeader = header.toLowerCase();
        if (headerMap[lowerHeader]) {
          mapping[header] = headerMap[lowerHeader];
        }
      });

      return mapping;
    }

    // Validate mapping to ensure all required fields are mapped
    function validateMapping() {
      const missingRequiredFields = [];
      const mappedValues = Object.values(mappedFields);

      requiredFields.forEach(field => {
        if (!mappedValues.includes(field)) {
          const fieldLabel = contactFields.find(f => f.value === field).label;
          missingRequiredFields.push(fieldLabel);
        }
      });

      return missingRequiredFields;
    }

    // Generate preview data for step 4
    function generatePreview() {
      const previewHeader = $("#preview-header");
      const previewData = $("#preview-data");
      const previewLimit = 5; // Show only first 5 contacts

      previewHeader.empty();
      previewData.empty();

      // Create header row
      const headerRow = $("<tr>");

      // Add standard contact fields
      contactFields.forEach(field => {
        headerRow.append($("<th>").text(field.label));
      });

      // Add birthday field if it's mapped
      const hasBirthdayMapping = Object.values(mappedFields).includes('birthday');
      if (hasBirthdayMapping) {
        headerRow.append($("<th>").text("Birthday"));
      }

      // Add custom variables headers
      // First add a separator header for detected variables
      if (Object.keys(detectedVariables).length > 0) {
        headerRow.append($("<th>").addClass("bg-light text-primary").text("Custom Variables"));

        // Add each detected variable as a column
        Object.values(detectedVariables).forEach(variableName => {
          headerRow.append($("<th>").text(variableName));
        });
      }

      // Add manual variables if any
      if (Object.keys(manualVariables).length > 0) {
        // Only add the separator if we didn't already add one for detected variables
        if (Object.keys(detectedVariables).length === 0) {
          headerRow.append($("<th>").addClass("bg-light text-primary").text("Custom Variables"));
        }

        // Add each manual variable as a column
        Object.keys(manualVariables).forEach(variableName => {
          headerRow.append($("<th>").text(variableName));
        });
      }

      previewHeader.append(headerRow);

      // Check for duplicates
      duplicateEmails = findDuplicateEmails(csvData, mappedFields);

      // Create data rows
      const previewRows = csvData.slice(0, previewLimit);
      previewRows.forEach(row => {
        const dataRow = $("<tr>");

        // Check if this row is a duplicate
        const businessEmailField = Object.keys(mappedFields).find(key => mappedFields[key] === 'businessEmail');
        const email = businessEmailField ? row[businessEmailField] : '';

        if (duplicateEmails.includes(email)) {
          dataRow.addClass("duplicate-row");
        }

        // Add cells for each standard field
        contactFields.forEach(field => {
          const csvHeader = Object.keys(mappedFields).find(key => mappedFields[key] === field.value);
          const value = csvHeader ? row[csvHeader] : '';
          dataRow.append($("<td>").text(value || ''));
        });

        // Add birthday field if it's mapped
        if (hasBirthdayMapping) {
          const birthdayHeader = Object.keys(mappedFields).find(key => mappedFields[key] === 'birthday');
          const birthdayValue = birthdayHeader ? row[birthdayHeader] : '';
          dataRow.append($("<td>").text(birthdayValue || ''));
        }

        // Add detected custom variables values
        if (Object.keys(detectedVariables).length > 0) {
          // Add a separator cell for the first custom variable column
          dataRow.append($("<td>").addClass("bg-light"));

          // Add each detected variable value
          Object.entries(detectedVariables).forEach(([csvHeader, variableName]) => {
            const value = row[csvHeader] || '';
            dataRow.append($("<td>").text(value));
          });
        }

        // Add manual variables values (these are the same for all rows)
        if (Object.keys(manualVariables).length > 0) {
          // Only add the separator if we didn't already add one for detected variables
          if (Object.keys(detectedVariables).length === 0) {
            dataRow.append($("<td>").addClass("bg-light"));
          }

          // Add each manual variable value
          Object.entries(manualVariables).forEach(([variableName, variableValue]) => {
            dataRow.append($("<td>").text(variableValue || ''));
          });
        }

        previewData.append(dataRow);
      });

      // Update summary information
      $("#total-contacts").text(csvData.length);
      $("#duplicate-contacts").text(duplicateEmails.length);
    }

    // Find duplicate emails in the CSV data
    function findDuplicateEmails(data, mapping) {
      const emails = [];
      const duplicates = [];

      // Find the CSV header that maps to businessEmail
      const businessEmailField = Object.keys(mapping).find(key => mapping[key] === 'businessEmail');

      if (businessEmailField) {
        data.forEach(row => {
          const email = row[businessEmailField].toLowerCase().trim();
          if (emails.includes(email)) {
            duplicates.push(email);
          } else {
            emails.push(email);
          }
        });
      }

      return duplicates;
    }

    // Detect custom variables from CSV headers
    function detectCustomVariables() {
      detectedVariables = {};
      const variablePrefix = 'var_';

      // Clear previous detected variables
      $("#detected-variables-list").empty();

      // Find headers that start with var_
      const customVarHeaders = csvHeaders.filter(header =>
        header.toLowerCase().startsWith(variablePrefix.toLowerCase())
      );

      if (customVarHeaders.length > 0) {
        // Show the table and hide the "no variables" message
        $("#no-variables-message").addClass("d-none");
        $("#detected-variables-table-container").removeClass("d-none");

        // Add each detected variable to the table
        customVarHeaders.forEach(header => {
          const variableName = header.substring(variablePrefix.length);
          const sampleValue = csvData.length > 0 ? csvData[0][header] || '' : '';

          // Store in detectedVariables object
          detectedVariables[header] = variableName;

          // Add to the table
          const row = $("<tr>");
          row.append($("<td>").text(header));
          row.append($("<td>").text(variableName));
          row.append($("<td>").text(sampleValue));

          $("#detected-variables-list").append(row);
        });
      } else {
        // Show the "no variables" message and hide the table
        $("#no-variables-message").removeClass("d-none");
        $("#detected-variables-table-container").addClass("d-none");
      }
    }

    // Add a manual custom variable
    function addManualVariable() {
      const variableName = $("#new-variable-name").val().trim();
      const variableValue = $("#new-variable-value").val().trim();
      const variableNameClean = DOMPurify.sanitize(variableName);
      const variableValueClean = DOMPurify.sanitize(variableValue);
    console.log(variableNameClean);
    console.log(variableValueClean);
      // Validate inputs
      if (!variableNameClean) {
        alert("Please enter a variable name");
        $("#new-variable-name").focus();
        return;
      }

      if (!variableValueClean) {
        alert("Please enter a variable value");
        $("#new-variable-value").focus();
        return;
      }

      // Check for duplicate variable name
      if (manualVariables[variableNameClean]) {
        alert("A variable with this name already exists. Please use a different name.");
        $("#new-variable-name").focus();
        return;
      }

      // Add to manualVariables object
      manualVariables[variableNameClean] = variableValueClean;

      // Update the UI
      updateManualVariablesTable();

      // Clear the input fields
      $("#new-variable-name").val('').focus();
      $("#new-variable-value").val('');
    }

    // Update the manual variables table
    function updateManualVariablesTable() {
      const variablesList = $("#manual-variables-list");
      variablesList.empty();

      const variableNames = Object.keys(manualVariables);

      if (variableNames.length > 0) {
        // Show the table and hide the "no variables" message
        $("#no-manual-variables-message").addClass("d-none");
        $("#manual-variables-table-container").removeClass("d-none");

        // Add each manual variable to the table
        variableNames.forEach(name => {
          const value = manualVariables[name];

          const row = $("<tr>");
          row.append($("<td>").text(name));
          row.append($("<td>").text(value));

          // Add delete button
          const deleteBtn = $("<button>")
            .addClass("btn btn-sm btn-danger")
            .html('<i class="bx bx-trash"></i>')
            .attr("title", "Delete variable")
            .on("click", function() {
              delete manualVariables[name];
              updateManualVariablesTable();
            });

          row.append($("<td>").addClass("text-center").append(deleteBtn));
          variablesList.append(row);
        });
      } else {
        // Show the "no variables" message and hide the table
        $("#no-manual-variables-message").removeClass("d-none");
        $("#manual-variables-table-container").addClass("d-none");
      }
    }

    // Handle import button click
    $("#import-btn").click(function() {
      $(this).prop("disabled", true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Importing...');

      // Get the file URL from the Dropzone instance
      let fileUrl = '';
      if (myDropzone.files && myDropzone.files.length > 0) {
        const file = myDropzone.files[0];

        // Try different ways to get the file URL
        if (file.xhr && file.xhr.response) {
          try {
            const response = JSON.parse(file.xhr.response);
            if (response.file && response.file.url) {
              fileUrl = response.file.url;
            }
          } catch (e) {
            console.error("Error parsing server response:", e);
          }
        } else if (file.serverData && file.serverData.file) {
          fileUrl = file.serverData.file.url;
        } else if (file.dataURL) {
          // If we have a data URL, we can use that as a fallback
          console.log("Using data URL as fallback");
          fileUrl = file.dataURL;
        }
      }

      // If we couldn't get the URL from the server response, process the file client-side
      if (!fileUrl) {
        alert("File upload failed. Please try again.");
        $(this).prop("disabled", false).html('<i class="bx bx-check me-1"></i> Import Now');
        return;
      }

      // Prepare data for import
      const importData = {
        fileName: uploadedFileName,
        mapping: mappedFields,
        data: csvData,
        file: {
          url: fileUrl
        },
        detectedVariables: detectedVariables,
        manualVariables: manualVariables
      };

      console.log("Importing with data:", importData);

      // Make actual API call to import contacts
      $.ajax({
        url: "/api/contacts/import-csv",
        method: "POST",
        data: JSON.stringify({
          csvFileUrl: importData.file.url,
          mapping: importData.mapping,
          detectedVariables: importData.detectedVariables,
          manualVariables: importData.manualVariables
        }),
        contentType: "application/json",
        success: function(response) {
          console.log("Import successful:", response);

          // Check if there were any errors
          if (response.importErrors && response.importErrors.length > 0) {
            // Show partial success message
            let errorMessage = `Import partially completed. ${response.totalImported} contacts were imported, but ${response.totalIgnored} were ignored.\n\n`;
            errorMessage += "Some common issues:\n";
            errorMessage += "- Date format for birthday should be DD-MM-YYYY\n";
            errorMessage += "- Referral should be 'true' or 'false'\n";
            errorMessage += "- Contact Status should be one of: New, In Progress, Closed, etc.\n\n";

            // Show the first few errors
            if (response.importErrors.length > 0) {
              errorMessage += "Sample errors:\n";
              response.importErrors.slice(0, 3).forEach((err, index) => {
                errorMessage += `${index + 1}. ${err.contact}: ${err.error}\n`;
              });

              if (response.importErrors.length > 3) {
                errorMessage += `...and ${response.importErrors.length - 3} more errors.\n`;
              }
            }

            alert(errorMessage);

            // Enable the import button again but change text to "Continue"
            $("#import-btn").prop("disabled", false).html('<i class="bx bx-check me-1"></i> Continue');

            // Add a click handler to continue to success screen
            $("#import-btn").off('click').on('click', function() {
              // Hide step 5 content
              $("#step5-content").removeClass("active");

              // Show success message
              $("#success-content").addClass("active");
              $("#imported-count").text(response.totalImported);
              $("#ignored-count").text(response.totalIgnored);

              // Show or hide the ignore reason based on whether there are ignored contacts
              if (response.totalIgnored > 0) {
                $("#ignore-reason").removeClass("d-none");
              } else {
                $("#ignore-reason").addClass("d-none");
              }

              // Reset step indicators
              $(".step").removeClass("active completed");

              // Reset the import button state
              $(this).prop("disabled", false).html('<i class="bx bx-check me-1"></i> Import Now');
            });
          } else {
            // Complete success - proceed to success screen
            // Hide step 5 content
            $("#step5-content").removeClass("active");

            // Show success message
            $("#success-content").addClass("active");
            $("#imported-count").text(response.totalImported);
            $("#ignored-count").text(response.totalIgnored);

            // Show or hide the ignore reason based on whether there are ignored contacts
            if (response.totalIgnored > 0) {
              $("#ignore-reason").removeClass("d-none");
            } else {
              $("#ignore-reason").addClass("d-none");
            }

            // Reset step indicators
            $(".step").removeClass("active completed");

            // Reset the import button state
            $("#import-btn").prop("disabled", false).html('<i class="bx bx-check me-1"></i> Import Now');
          }
        },
        error: function(error) {
          console.error("Import failed:", error);

          // Enable the import button again
          $("#import-btn").prop("disabled", false).html('<i class="bx bx-check me-1"></i> Import Now');

          // Show detailed error message if available
          let errorMessage = "Failed to import contacts. ";

          if (error.responseJSON && error.responseJSON.error) {
            errorMessage += error.responseJSON.error;
          } else {
            errorMessage += "Please check your CSV file format and try again.";
          }

          alert(errorMessage);
        }
      });
    });

    // Handle "Import Another File" button click
    $("#import-another-btn").click(function() {
      // Reset everything
      csvData = null;
      csvHeaders = [];
      mappedFields = {};
      uploadedFileName = '';
      duplicateEmails = [];
      detectedVariables = {};
      manualVariables = {};

      // Reset Dropzone properly
      if (myDropzone) {
        myDropzone.removeAllFiles(true);
      }

      // Reset UI
      $("#upload-next-btn").prop("disabled", true);
      $("#import-btn").prop("disabled", false).html('<i class="bx bx-check me-1"></i> Import Now');
      $("#upload-error").addClass("d-none");

      // Reset custom variables UI
      $("#detected-variables-list").empty();
      $("#no-variables-message").removeClass("d-none");
      $("#detected-variables-table-container").addClass("d-none");
      $("#manual-variables-list").empty();
      $("#no-manual-variables-message").removeClass("d-none");
      $("#manual-variables-table-container").addClass("d-none");
      $("#new-variable-name").val('');
      $("#new-variable-value").val('');

      // Show step 1
      $(".step-content").removeClass("active");
      $("#step1-content").addClass("active");

      // Reset step indicators
      $(".step").removeClass("active completed");
      $("#step1-indicator").addClass("active");
    });
  });
</script>
