// controllers/fileController.js
const File = require("../models/File");
const path = require("path");
const fs = require("fs");
const nodemailer = require("nodemailer");
const { createLog } = require("./logController");
// Use the host URL from environment variables
const HOST_URL = process.env.HOST;

async function sendMail(
  email,
  totalRecords,
  successfulRecords,
  skippedRecords,
  failedRecords,
  errorReportLink = "",
  firstName
) {
  const config = {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
      user: process.env.EMAIL_USERNAME,
      pass: process.env.EMAIL_PASSWORD,
    },
  };
  const emailBody = `
    <p>Hi, ${firstName}</p>
    <p>Your data import into <strong>MixCertificate</strong> has been successfully completed. Below is a summary of the import:</p>
    
    <h3>Import Summary</h3>
    <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
      <tr>
        <th style="text-align: left;">Import Summary</th>
        <th style="text-align: left;">Count</th>
      </tr>
      <tr>
        <td>Total Records Processed</td>
        <td>${totalRecords}</td>
      </tr>
      <tr>
        <td>Successfully Imported</td>
        <td>${successfulRecords}</td>
      </tr>
      <tr>
        <td>Skipped/Duplicate Records</td>
        <td>${skippedRecords}</td>
      </tr>
      <tr>
        <td>Failed Imports</td>
        <td>${failedRecords}</td>
      </tr>
    </table>

    ${
      errorReportLink
        ? `<p>For any records that were skipped or failed, you can <a href="${errorReportLink}" target="_blank">review the detailed report</a> and make necessary corrections.</p>`
        : ""
    }

    <h3>Next Steps:</h3>
    <ul>
      <li>Review your imported data in the <strong>MixCertificate</strong> dashboard.</li>
      <li>Fix any issues with failed records.</li>
      <li>Generate and manage certificates seamlessly.</li>
    </ul>

    <p>If you need any assistance, feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or visit our <a href="https://www.mixcommerce.co">help center</a>.</p>

    <p>Best regards,</p>
    <p><strong>MixCertificate</strong><br>
    <a href="mailto:<EMAIL>"><EMAIL></a><br>
    <a href="https://www.mixcommerce.co">www.mixcommerce.co</a></p>
  `;
  try {
    const transporter = nodemailer.createTransport(config);
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: email,
      subject: `Data Import Completed - MixCertificate`,
      html: emailBody,
    });
    return info.messageId;
  } catch (error) {
    throw new Error("Error sending email");
  }
}
// Upload a single file
exports.uploadFile = async (req, res) => {
  //console.log("File upload request received: ", req.files, req.body);

  if (!req.files || Object.keys(req.files).length === 0) {
    return res.status(400).json({ message: "No file attached." });
  }

  const file = req.files.file;

  // Ensure the upload directory exists
  const uploadDir = path.join(__dirname, "../public/uploads");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Define the file path and save the file
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(uploadDir, fileName);
  file.mv(filePath, async (err) => {
    if (err) {
      console.log(err);
      return res.status(500).json({ message: err.message });
    }

    try {
      // Create a file entry in the database
      const fileData = {
        fileName: fileName,
        fileType: file.mimetype,
        fileSize: file.size,
        url: `/uploads/${fileName}`,
        uploadedBy: req.user._id,
        associatedEntity: req.body.associatedEntity || [],
        entityType: req.body.entityType,
        tags: req.body.tags || [],
        customFields: req.body.customFields || {},
      };

      //console.log("fileData",fileData)

      const savedFile = await File.create(fileData);
      await createLog(
        {
          eventType: "File Upload",
          action: "Upload",
          target: savedFile.url,
        },
        {
          user: {
            _id: req.user?._id,
            businessId: req.user?.businessId,
            role: req.user?.role,
          },
          ip: req.ip,
          headers: req.headers["user-agent"] || "Unknown",
        },
        res
      );
      await sendMail(req.user.email, 1, 1, 0, 0, "", req.user.firstName);
      res.status(200).json({
        message: "File uploaded successfully.",
        file: savedFile,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
};

// Upload design file for thumbnail
exports.uploadDesignFile = async (req, res) => {
  //console.log("File upload request received: ", req.files, req.body);

  console.log("request recieved on upload design file");

  if (!req.files || Object.keys(req.files).length === 0) {
    return res.status(400).json({ message: "No file attached." });
  }

  const file = req.files.file;

  // Ensure the upload directory exists
  const uploadDir = path.join(__dirname, "../public/assets/images/designs/");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Define the file path and save the file
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(uploadDir, fileName);

  file.mv(filePath, async (err) => {
    if (err) {
      console.log(err);
      return res.status(500).json({ message: err.message });
    }

    try {
      // Create a file entry in the database
      const fileData = {
        access: "private",
        fileName: fileName,
        fileType: file.mimetype,
        fileSize: file.size,
        url: `/assets/images/designs/${fileName}`,
        uploadedBy: req.user._id,
        associatedEntity: req.body.associatedEntity || [],
        entityType: req.body.entityType,
        tags: req.body.tags || ["design", "thumbnail"],
        customFields: req.body.customFields || {},
      };

      //console.log("fileData",fileData)

      const savedFile = await File.create(fileData);
      //console.log("savedFile",savedFile)

      res.status(200).json({
        message: "Design Thumbnail Saved successfully.",
        file: savedFile,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
};

exports.uploadTemplateFile = async (req, res) => {
  //console.log("File upload request received: ", req.files, req.body);

  if (!req.files || Object.keys(req.files).length === 0) {
    return res.status(400).json({ message: "No file attached." });
  }

  const file = req.files.file;

  // Ensure the upload directory exists
  const uploadDir = path.join(__dirname, "../public/uploads");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Define the file path and save the file
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(uploadDir, fileName);

  file.mv(filePath, async (err) => {
    if (err) {
      console.log(err);
      return res.status(500).json({ message: err.message });
    }

    try {
      // Create a file entry in the database
      const fileData = {
        fileName: fileName,
        fileType: file.mimetype,
        fileSize: file.size,
        url: `/uploads/${fileName}`,
        uploadedBy: req.user._id,
        associatedEntity: req.body.associatedEntity || [],
        entityType: req.body.entityType,
        tags: ["template", "certificate", "certificate background"], //req.body.tags || [],
        customFields: req.body.customFields || {},
      };

      //console.log("fileData",fileData)

      const savedFile = await File.create(fileData);
      //console.log("savedFile",savedFile)

      res.status(200).json({
        message: "File uploaded successfully.",
        file: savedFile,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
};
// Upload a single file
exports.uploadPrivateFile = async (req, res) => {
  //console.log("File upload request received: ", req.files, req.body);

  if (!req.files || Object.keys(req.files).length === 0) {
    return res.status(400).json({ message: "No file attached." });
  }

  const file = req.files.file;

  // Ensure the upload directory exists
  const uploadDir = path.join(__dirname, "../mixcertificate-content/uploads");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Define the file path and save the file
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(uploadDir, fileName);

  file.mv(filePath, async (err) => {
    if (err) {
      console.log(err);
      return res.status(500).json({ message: err.message });
    }

    try {
      // Create a file entry in the database
      const fileData = {
        fileName: fileName,
        fileType: file.mimetype,
        fileSize: file.size,
        url: `/uploads/${fileName}`,
        uploadedBy: req.user._id,
        associatedEntity: req.body.associatedEntity || [],
        entityType: req.body.entityType,
        tags: req.body.tags || [],
        customFields: req.body.customFields || {},
      };

      //console.log("fileData",fileData)

      const savedFile = await File.create(fileData);
      //console.log("savedFile",savedFile)

      res.status(200).json({
        message: "File uploaded successfully.",
        file: savedFile,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
};

// Upload multiple files
exports.uploadFiles = async (req, res) => {
  if (!req.files || Object.keys(req.files).length === 0) {
    return res.status(400).json({ message: "No files uploaded." });
  }

  const files = Array.isArray(req.files.files)
    ? req.files.files
    : [req.files.files];
  const savedFiles = [];

  for (const file of files) {
    const fileName = `${Date.now()}-${file.name}`;
    const filePath = path.join(__dirname, "../public/uploads", fileName);

    // Ensure the upload directory exists
    if (!fs.existsSync(path.join(__dirname, "../public/uploads"))) {
      fs.mkdirSync(path.join(__dirname, "../public/uploads"), {
        recursive: true,
      });
    }

    try {
      await file.mv(filePath);

      const fileData = {
        fileName: fileName,
        fileType: file.mimetype,
        fileSize: file.size,
        url: `/uploads/${fileName}`,
        //uploadedBy: req.body.uploadedBy,
        uploadedBy: req.user._id,

        associatedEntity: req.body.associatedEntity || [],
        entityType: req.body.entityType,
        tags: req.body.tags || [],
        customFields: req.body.customFields || {},
      };

      const savedFile = await File.create(fileData);
      savedFiles.push(savedFile);
    } catch (error) {
      //console.log("upload file error:",error)
      return res.status(500).json({ message: error.message });
    }
  }

  res.status(200).json({
    message: "Files uploaded successfully.",
    files: savedFiles,
  });
};

// Retrieve all image files uploaded by user
exports.getUploadsImages = async (req, res) => {
  try {
    // Define image file types you want to filter
    const imageFileTypes = [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/svg+xml",
      "image/gif",
    ];

    // Find all files where the fileType matches an image type
    const images = await File.find({
      fileType: { $in: imageFileTypes },
      tags: { $nin: ["thumbnail"] },
    }).sort({ createdAt: -1 });

    //const files = await File.find({uploadedBy: uploadedBy,tags: { $nin: ["thumbnail"] } }).sort({ createdAt: -1 });

    res.status(200).json(images);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Retrieve recent uploaded files
exports.myUploads = async (req, res) => {
  try {
    uploadedBy = req.user._id;
    console.log("createdBy files", uploadedBy);

    //const files = await File.find({uploadedBy:uploadedBy}).sort({ createdAt: -1}); // Sort by most recent
    const files = await File.find({
      uploadedBy: uploadedBy,
      tags: { $nin: ["thumbnail"] },
    }).sort({ createdAt: -1 });

    res.status(200).json(files);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getFiles = async (req, res) => {
  try {
    const { page = 1, limit = 32, searchTerm } = req.query;
    console.log(searchTerm, "searchTerm");
    const parsedLimit = parseInt(limit);
    const parsedPage = parseInt(page);
    const skip = (parsedPage - 1) * parsedLimit;

    const searchQuery = searchTerm
      ? {
          $or: [
            { fileName: { $regex: searchTerm, $options: "i" } },
            { fileType: { $regex: searchTerm, $options: "i" } },
          ],
        }
      : {};

    const [files, totalFiles] = await Promise.all([
      File.find(searchQuery)
        .skip(skip)
        .limit(parsedLimit)
        .sort({ createdAt: -1 }),
      File.countDocuments(),
    ]);

    res.status(200).json({
      files,
      totalPages: Math.ceil(totalFiles / parsedLimit),
      currentPage: parsedPage,
      totalFiles,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Retrieve a specific file
exports.getFile = async (req, res) => {
  try {
    const file = await File.findById(req.params.id);
    if (!file) {
      return res.status(404).json({ message: "File not found." });
    }
    res.status(200).json(file);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update a specific file
exports.updateFile = async (req, res) => {
  try {
    const file = await File.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });
    if (!file) {
      return res.status(404).json({ message: "File not found." });
    }
    res.status(200).json(file);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Delete a specific file
exports.deleteFile = async (req, res) => {
  try {
    const file = await File.findById(req.params.id);
    if (!file) {
      return res.status(404).json({ message: "File not found." });
    }
    fs.unlinkSync(path.join(__dirname, "../public/uploads", file.fileName));
    await File.findByIdAndDelete(req.params.id);
    await createLog(
      {
        eventType: "File Upload",
        action: "Delete",
        target: file._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.status(200).json({ message: "File deleted successfully." });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Delete multiple files
exports.deleteMultipleFiles = async (req, res) => {
  try {
    const fileIds = req.body.fileIds;
    const files = await File.find({ _id: { $in: fileIds } });

    // Delete files from filesystem
    files.forEach((file) => {
      fs.unlinkSync(path.join(__dirname, "../public/uploads", file.fileName));
    });

    // Delete files from database
    await File.deleteMany({ _id: { $in: fileIds } });
    res.status(200).json({ message: "Files deleted successfully." });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Retrieve and search image files
exports.getPublicImagesAll = async (req, res) => {
  //console.log("req.query: ",req.query)
  try {
    // Define image file types to filter
    const imageFileTypes = [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/svg+xml",
      "image/gif",
    ];

    // Build a query object based on the incoming request
    const query = {
      fileType: { $in: imageFileTypes },
      access: "public", // Ensure 'access' status is correctly set in the database
    };

    // Check if there is a file name search term
    if (req.query.fileName) {
      query.fileName = { $regex: req.query.fileName, $options: "i" }; // Case-insensitive search
    }

    // Check if there are any tags to search for
    if (req.query.tags) {
      const tagsArray = req.query.tags.split(","); // Assuming tags are comma-separated
      query.tags = { $in: tagsArray };
    }

    // Filter by the uploadedBy field (user ID)
    if (req.query.uploadedBy) {
      query.uploadedBy = req.query.uploadedBy;
    }

    // Filter by associatedEntity (if provided)
    if (req.query.associatedEntity) {
      query.associatedEntity = req.query.associatedEntity;
    }

    // Filter by businessId (if provided)
    if (req.query.businessId) {
      query.businessId = req.query.businessId;
    }

    // Find files matching the query and sort them by creation date (newest first)
    const images = await File.find(query).sort({ createdAt: -1 });

    // Return the list of images
    res.status(200).json(images);
  } catch (error) {
    // Return error message if something goes wrong
    res.status(500).json({ message: error.message });
  }
};

// Retrieve and search public image files with pagination
exports.getPublicImagesPaginated = async (req, res) => {
  try {
    // Define image file types to filter
    const imageFileTypes = [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/svg+xml",
      "image/gif",
    ];

    // Build a query object based on the incoming request
    const query = {
      fileType: { $in: imageFileTypes },
      access: "public", // Ensure 'public' status is correctly set in the database
    };

    // Check if there is a file name search term
    if (req.query.fileName) {
      query.fileName = { $regex: req.query.fileName, $options: "i" }; // Case-insensitive search
    }

    // Check if there are any tags to search for
    if (req.query.tags) {
      const tagsArray = req.query.tags.split(","); // Assuming tags are comma-separated
      query.tags = { $in: tagsArray };
    }

    // Filter by the uploadedBy field (user ID)
    if (req.query.uploadedBy) {
      query.uploadedBy = req.query.uploadedBy;
    }

    // Filter by associatedEntity (if provided)
    if (req.query.associatedEntity) {
      query.associatedEntity = req.query.associatedEntity;
    }

    // Filter by businessId (if provided)
    if (req.query.businessId) {
      query.businessId = req.query.businessId;
    }

    // Pagination setup: default to page 1 and limit to 20 results per page, can be overridden by query
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20; // Allow limit to be passed in query
    const skip = (page - 1) * limit;

    // Debug: log the query to check if it's correct
    console.log("Query:", query);

    // Find files matching the query, sort them by creation date, and paginate results
    const images = await File.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Debug: log the returned images
    console.log("Found images:", images);

    // Count the total number of images for pagination info
    const totalImages = await File.countDocuments(query);

    // Return the list of images and pagination data
    res.status(200).json({
      images,
      totalPages: Math.ceil(totalImages / limit),
      currentPage: page,
    });
  } catch (error) {
    // Log the error for better debugging
    console.error("Error fetching public images:", error);

    // Return error message if something goes wrong
    res.status(500).json({ message: error.message });
  }
};

// Change the access field to 'public' for files owned by a specific owner
exports.changeFileAccessByOwner = async (req, res) => {
  try {
    const ownerId = req.query.ownerId; // Get ownerId from query

    // Validate ownerId
    if (!ownerId) {
      return res.status(400).json({ message: "ownerId is required" });
    }

    // Update the files, setting access to 'public', and creating the field if it doesn't exist
    const result = await File.updateMany(
      { tags: "certificate" }, // Filter by owner
      //{ uploadedBy: ownerId }, // Filter by owner
      { $set: { access: "public" } }, // Set the access field to 'public'
      { upsert: true, multi: true } // Upsert option to create the field if it doesn't exist
    );

    // Return success response
    res
      .status(200)
      .json({ message: "Access changed to public for owner files", result });
  } catch (error) {
    // Return error message if something goes wrong
    res.status(500).json({ message: error.message });
  }
};
