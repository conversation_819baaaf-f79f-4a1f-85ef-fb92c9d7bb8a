const User = require("../models/User");
const sendEmail = require("../utils/email");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");
const Notification = require("../models/Notification");
const sanitizer = require("sanitizer");
const { createLog } = require("../controllers/logController");
// Create a new user
exports.createUser = async (req, res) => {
  try {
    const sanitizedInputs = {
      firstName: sanitizer.sanitize(req.body.firstName || ""),
      lastName: sanitizer.sanitize(req.body.lastName || ""),
      email: sanitizer.sanitize(req.body.email || ""),
      password: sanitizer.sanitize(req.body.password || ""),
    };
    const user = await User.create({
      ...sanitizedInputs,
      displayName: sanitizer.sanitize(req.body.displayName || "Mixer"),
      phone: sanitizer.sanitize(req.body.phone || ""),
      role: sanitizer.sanitize(req.body.role || "Admin"),
      isVerified: true,
      verificationToken: null,
    });
    if (!user) return res.status(400).json({ error: "User Not Created" });

    const formattedDate = new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
    }).format(new Date());

    // Send welcome email to the newly created user
    await sendEmail({
      email: user.email,
      subject: "Your MixCertificate Account Has Been Created",
      message: `<h1>Welcome to MixCertificate, ${user.firstName} ${user.lastName}!</h1>
      <p>We are pleased to inform you that your account has been successfully created by our administrator,
      ${req.user.firstName} ${req.user.lastName}, on ${formattedDate}.</p>
      <p>You can now log in and start using our services.</p>
      <p>Best regards, <br>MixCertificate Team</p>`,
    });

    // Notify the admin about the new user creation
    const newNotification = await Notification.create({
      businessId: req.user.businessId,
      createdBy: req.user._id,
      createdFor: req.user._id,
      title: "New User Account Created",
      message: `A new user, ${user.firstName} ${user.lastName}, has been successfully created by you on ${formattedDate}.`,
      type: "UserCreation",
      icon: "user-plus",
      createdAt: Date.now(),
    });

    if (!newNotification)
      return res.status(400).json({ error: "Notification Not Created" });

    // Send email notification to the admin
    await sendEmail({
      email: req.user.email,
      subject: "Confirmation: User Account Successfully Created",
      message: `<h1>Hello ${req.user.firstName} ${req.user.lastName},</h1>
      <p>This is to confirm that you have successfully created a new user account for
      ${user.firstName} ${user.lastName} on ${formattedDate}.</p>
      <p>Thank you for managing your team with MixCertificate.</p>
      <p>Best regards, <br>MixCertificate Team</p>`,
    });

    res.status(201).json(user);
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: err.message });
  }
};

// Get all users
exports.getUsers = async (req, res) => {
  try {
    const users = await User.find().select("-password -resetPasswordToken"); //.select('-password'); // Exclude the password field;
    res.status(200).json(users);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Controller function to get the current user's information
exports.getCurrentUserInfo = async (req, res) => {
  //console.log("profile info request received")
  //console.log("req.user._id",req.user._id)

  try {
    // Assuming the user's ID is available via req.user._id (based on your authentication middleware)
    const userId = req.user._id;
    //console.log("req.user._id",req.user._id)

    // Find the user by ID but exclude the password field
    const user = await User.findById(userId).select(
      "-password -resetPasswordToken"
    );

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Send the user info as a response, without password
    res.status(200).json(user);
  } catch (error) {
    console.error("Error fetching user info:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get a user by ID
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select(
      "-password -resetPasswordToken"
    ); // Exclude the password field;
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }
    res.status(200).json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Update a user by ID - with function to hash the password if it's modified
exports.updateUserById = async (req, res) => {
  try {
    console.log("req.body - update user", req.body);

    const user = await User.findById(req.params.id).select(
      "-password -resetPasswordToken"
    );
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const oldRole = user.role;
    const newRole = req.body.role;

    const fieldsToUpdate = [
      "firstName",
      "lastName",
      "displayName",
      "email",
      "phone",
      "role",
    ];

    fieldsToUpdate.forEach((field) => {
      if (req.body[field]) user[field] = req.body[field];
    });

    if (req.body.password) {
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(req.body.password, salt);
    }

    await user.save();

    if (newRole && newRole !== oldRole) {
      try {
        await createLog(
          {
            eventType: "User Management",
            action: "Role Update",
            target: user.email,
          },
          {
            user: {
              _id: req.user?._id,
              businessId: req.user?.businessId,
              role: req.user?.role,
            },
            ip: req.ip,
            headers: req.headers["user-agent"] || "Unknown",
          },
          res
        );
      } catch (logErr) {
        console.error("Role change log failed:", logErr);
      }
    }

    res.status(200).json(user);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

//update own user id
exports.updateCurrentUserProfile = async (req, res) => {
  try {
    const userId = req.user.id; // Get the current user's ID
    const updateData = req.body; // Get the data to update from the request body

    // Assuming you have a User model and a method to find and update the user
    const updatedUser = await User.findByIdAndUpdate(userId, updateData, {
      new: true,
    }).select("-password -resetPasswordToken");

    if (!updatedUser) {
      return res.status(404).json({ message: "User not found." });
    }

    res.status(200).json({
      message: "User profile updated successfully.",
      user: updatedUser,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error updating user profile." });
  }
};

// Delete a user by ID
exports.deleteUserById = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Log the delete action
    try {
      await createLog(
        {
          eventType: "User Management",
          action: "Remove User",
          target: user.email,
        },
        {
          user: {
            _id: req.user?._id,
            businessId: req.user?.businessId,
            role: req.user?.role,
          },
          ip: req.ip,
          headers: req.headers["user-agent"] || "Unknown",
        },
        res
      );
    } catch (logErr) {
      console.error("User deletion log failed:", logErr);
    }

    res.status(200).json({ message: "User deleted successfully" });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getUserCount = async (req, res) => {
  try {
    const totalCount = await User.countDocuments();
    res.json({ totalCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getEstimatedUserCount = async (req, res) => {
  try {
    const estimatedCount = await User.estimatedDocumentCount();
    res.json({ estimatedCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.verifyPasswordAndSendOtpForAccountDeletion = async (req, res) => {
  try {
    const rawPassword = req.body.password;
    if (!rawPassword) {
      return res.status(400).json({ message: "Password is required" });
    }
    const existingUser = await User.findOne({ _id: req.user._id });
    const isPasswordCorrect = await existingUser.comparePassword(rawPassword);
    console.log(isPasswordCorrect);
    if (!isPasswordCorrect) {
      return res.status(401).json({ message: "Incorrect password" });
    }
    const otpLength = 6;
    let generatedOtp = "";
    for (let i = 0; i < otpLength; i++) {
      const char = Math.floor(Math.random() * 10);
      generatedOtp += char;
    }
    existingUser.otp = generatedOtp;
    await existingUser.save();
    await sendEmail({
      email: existingUser.email,
      subject: "OTP for Account Deletion",
      message: `<h1>OTP for Account Deletion</h1>
      <p>Your OTP for account deletion is: ${generatedOtp}</p>
      <p>Please use this OTP to complete the account deletion process.</p>
      <p>Best regards, <br>MixCertificate Team</p>`,
    });

    res.status(200).json({ message: "Password verified", success: true });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
exports.veryOtpForAccountDeletion = async (req, res) => {
  try {
    const { otp } = req.body;
    const existingUser = await User.findOne({ _id: req.user._id, otp });
    if (!existingUser) {
      return res.status(401).json({ message: "User not found or OTP expired" });
    }
    if (existingUser.otp !== otp) {
      return res.status(401).json({ message: "Incorrect OTP" });
    }
    res.status(200).json({ message: "OTP verified", success: true });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.resendOtpForAccountDeletion = async (req, res) => {
  try {
    const existingUser = await User.findOne({ _id: req.user._id });
    if (!existingUser) {
      return res.status(401).json({ message: "User not found" });
    }
    const otpLength = 6;
    let generatedOtp = "";
    for (let i = 0; i < otpLength; i++) {
      const char = Math.floor(Math.random() * 10);
      generatedOtp += char;
    }
    existingUser.otp = generatedOtp;
    await existingUser.save();
    await sendEmail({
      email: existingUser.email,
      subject: "OTP for Account Deletion",
      message: `<h1>OTP for Account Deletion</h1>
      <p>Your OTP for account deletion is: ${generatedOtp}</p>
      <p>Please use this OTP to complete the account deletion process.</p>
      <p>Best regards, <br>MixCertificate Team</p>`,
    });
    res.status(200).json({ message: "OTP resent", success: true });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.markAccountForDeletion = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Mark the account for deletion and set the deletion date
    user.isAccountDeleted = true;
    user.accountDeletionDate = new Date(); // Optional: set the deletion date to the current date/time
    user.otp = null;
    await user.save();

    // Send confirmation email
    await sendEmail({
      email: user.email,
      subject: "Account Deletion Confirmation",
      message: `<h1>Account Deletion Confirmation</h1>
      <p>Dear ${user.firstName} ${user.lastName},</p>
      <p>We confirm that your account has been marked for deletion. Your account and all associated data will be permanently deleted after 35 days.</p>
      <p>If you wish to cancel this process, please contact our support team before the 35-day period ends.</p>
      <p>Best regards,<br>MixCertificate Team</p>`,
    });

    res.status(200).json({
      message:
        "Account marked for deletion. It will be permanently deleted after 35 days.",
    });
  } catch (error) {
    console.error("Error marking account for deletion:", error);
    res.status(500).json({ message: "Server error" });
  }
};

// Admin invites a user
exports.inviteUser = async (req, res) => {
  try {
    const { email, role, firstName, lastName } = req.body;

    let user = await User.findOne({ email });

    const token = crypto.randomBytes(32).toString("hex");
    const tokenExpiry = Date.now() + 7 * 24 * 60 * 60 * 1000;
    if (user) {
      if (user.inviteStatus === "Pending") {
        user.inviteToken = token;
        user.inviteTokenExpires = tokenExpiry;
        await user.save();
      } else {
        return res
          .status(400)
          .json({ message: "User already exists or accepted invite." });
      }
    } else {
      user = await User.create({
        email,
        firstName,
        lastName,
        password: crypto.randomBytes(16).toString("hex"),
        role,
        status: "inactive",
        isVerified: false,
        inviteToken: token,
        inviteTokenExpires: tokenExpiry,
        inviteStatus: "Pending",
      });
    }

    const link = `${req.protocol}://${req.get("host")}/accept-invite/${token}`;

    const htmlMessage = `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; background-color: #ffffff; color: #333;">
      <h2 style="color: #333;">You're invited to join MixCertificate</h2>
      <p>Hello ${firstName || "there"},</p>
      <p>
        You have been invited to join <strong>MixCertificate</strong> as a
        <strong>${role}</strong>.
      </p>
      <p>
        Click the button below to accept your invitation and set your password:
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <a
          href="${link}"
          style="display: inline-block; padding: 12px 20px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 5px; font-size: 16px;"
        >
          Accept Invitation
        </a>
      </div>
      <p style="font-size: 14px; color: #777;">
        This link will expire in 7 days.
      </p>
    </div>`;
    await sendEmail({
      email,
      subject: "You're invited to join MixCertificate",
      message: htmlMessage,
    });

    res.status(200).json({ message: "Invitation email sent successfully." });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

exports.reinviteUser = async (req, res) => {
  try {
    const { email, role, firstName, lastName } = req.body;

    let user = await User.findOne({ email });

    if (!user || user.inviteStatus === "Accepted") {
      return res.status(400).json({
        message: "User already accepted invitation or does not exist.",
      });
    }
    if (role) {
      user.role = role;
    }
    if (firstName) {
      user.firstName = firstName;
    }
    if (lastName) {
      user.lastName = lastName;
    }

    const token = crypto.randomBytes(32).toString("hex");
    user.inviteToken = token;
    user.inviteTokenExpires = Date.now() + 7 * 24 * 60 * 60 * 1000;
    user.inviteStatus = "Pending";

    await user.save();

    const link = `${req.protocol}://${req.get("host")}/accept-invite/${token}`;

    const htmlMessage = `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; background-color: #ffffff; color: #333;">
  <h2 style="color: #333;">You're re-invited to join MixCertificate</h2>
  <p>Hi ${user.firstName || "there"},</p>
  <p>
    You are being re-invited to join <strong>MixCertificate</strong> as a 
    <strong>${user.role}</strong>.
  </p>
  <p>
    Click the button below to accept your invitation and set your password:
  </p>
  <div style="text-align: center; margin: 30px 0;">
    <a href="${link}" 
       style="display: inline-block; padding: 12px 20px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 5px; font-size: 16px;">
      Accept Invitation
    </a>
  </div>
  <p style="font-size: 14px; color: #777;">
    This link will expire in 7 days.
  </p>
</div>`;

    await sendEmail({
      email: user.email,
      subject: "Re-invitation to join MixCertificate",
      message: htmlMessage,
    });

    res.status(200).json({ message: "Re-invitation email sent successfully." });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
