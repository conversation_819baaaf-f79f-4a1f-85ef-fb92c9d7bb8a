const express = require("express");
const router = express.Router();
const ticketController = require("../controllers/ticketController");
const { checkPermissions } = require("../middleware/middleware");

router.post(
  "/",
  checkPermissions("Ticket", ["create"]),
  ticketController.createTicket
);
router.get(
  "/",
  checkPermissions("Ticket", ["read"]),
  ticketController.getTickets
);
router.get(
  "/:id",
  checkPermissions("Ticket", ["read"]),
  ticketController.getTicketById
);
router.put(
  "/:id",
  checkPermissions("Ticket", ["update"]),
  ticketController.updateTicket
);
router.delete(
  "/:id",
  checkPermissions("Ticket", ["delete"]),
  ticketController.deleteTicket
);
router.put(
  "/toggle/visibility",
  checkPermissions("Ticket", ["update"]),
  ticketController.toggleAllVisibility
);
router.put(
  "/:id/toggle/visibility",
  checkPermissions("Ticket", ["update"]),
  ticketController.toggleSingleVisibility
);


module.exports = router;
