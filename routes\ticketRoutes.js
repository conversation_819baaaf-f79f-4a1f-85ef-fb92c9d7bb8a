const express = require("express");
const router = express.Router();
const ticketController = require("../controllers/ticketController");
const { checkPermissions } = require("../middleware/middleware");

router.post(
  "/",
  checkPermissions("Ticket", ["create"]),
  ticketController.createTicket
);
router.get(
  "/",
  checkPermissions("Ticket", ["read"]),
  ticketController.getTickets
);
router.get(
  "/:id",
  checkPermissions("Ticket", ["read"]),
  ticketController.getTicketById
);
router.put(
  "/:id",
  checkPermissions("Ticket", ["update"]),
  ticketController.updateTicket
);
router.delete(
  "/:id",
  checkPermissions("Ticket", ["delete"]),
  ticketController.deleteTicket
);
router.put(
  "/toggle/visibility",
  checkPermissions("Ticket", ["update"]),
  ticketController.toggleAllVisibility
);
router.put(
  "/:id/toggle/visibility",
  checkPermissions("Ticket", ["update"]),
  ticketController.toggleSingleVisibility
);
router.get(
  "/public/all",
  checkPermissions("Ticket", ["read"]),
  ticketController.getPublicTickets
);

router.post(
  "/:id/comment",
  checkPermissions("Ticket", ["update"]),
  ticketController.addComment
);
router.get(
  "/export/all",
  checkPermissions("Ticket", ["read"]),
  ticketController.exportTicketsToCSV
);

router.post(
  "/import/all",
  checkPermissions("Ticket", ["create"]),
  ticketController.importTicketsFromCSV
);


router.post(
  "/import/upload",
  checkPermissions("Ticket", ["create"]),
  ticketController.uploadCSVFile
);

module.exports = router;
