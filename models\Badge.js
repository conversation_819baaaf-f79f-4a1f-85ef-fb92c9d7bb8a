const mongoose = require('mongoose');

const BadgeSchema = new mongoose.Schema({
  title: { type: String, required: true },
  badgeId: { type: String, required: true },    
  description: { type: String },
  badgeURL: { type: String},
  badgeImage: { type: String},
  badgePDF: { type: String},
  skill: { type: String, required: true },
  dateOfIssue: { type: Date, required: true },
  dateOfExpiry: { type: Date },
  verified: { type: Boolean, default: false },
  //Add public profiles
  socialCounts: {
    linkedin: { type: Number, default: 0 },
    twitter: { type: Number, default: 0 },
    facebook: { type: Number, default: 0 },
    widgetClick: { type: Number, default: 0 },
    totalSocialShare: { type: Number, default: 0 }, // New field to track total shares
  },
  contactId: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Contact' }],
  createdBy: {type: mongoose.Schema.Types.ObjectId,ref: 'User',required: false},
  updatedBy: {type: mongoose.Schema.Types.ObjectId,ref: 'User',required: false},
  businessId: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
}, { timestamps: true });

BadgeSchema.methods.updateSocialClicks = async function(platform) {
  if (this.socialCounts[platform] !== undefined) {
    this.socialCounts[platform]++;
    this.socialCounts.totalSocialShare++;
    await this.save();
  } else {
    throw new Error('Invalid platform');
  }
};

const Badge = mongoose.model('Badge', BadgeSchema);
BadgeSchema.index({ createdAt: 1 });

module.exports = Badge;
