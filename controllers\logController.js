const Log = require("../models/Log");
const { v4: uuidv4 } = require("uuid");
exports.getLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      user,
      role,
      eventType,
      action,
      target,
      ipAddress,
      userAgent,
      keyword,
      startDate,
      endDate,
      sortField = "timestamp",
      sortOrder = "desc",
    } = req.query;

    const filter = {
      businessId: req.user.businessId,
    };

    if (user) filter.user = user;
    if (role) filter.role = { $regex: role, $options: "i" };
    if (eventType) filter.eventType = { $regex: eventType, $options: "i" };
    if (action) filter.action = { $regex: action, $options: "i" };
    if (target) filter.target = { $regex: target, $options: "i" };
    if (ipAddress) filter.ipAddress = { $regex: ipAddress, $options: "i" };
    if (userAgent) filter.userAgent = { $regex: userAgent, $options: "i" };

    if (startDate && endDate)
      filter.timestamp = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };

    if (keyword) {
      filter.$or = [
        { target: { $regex: keyword, $options: "i" } },
        { action: { $regex: keyword, $options: "i" } },
        { eventType: { $regex: keyword, $options: "i" } },
      ];
    }

    const parsedPage = parseInt(page);
    const parsedLimit = parseInt(limit);

    const sortOptions = {};
    sortOptions[sortField] = sortOrder === "asc" ? 1 : -1;

    const logs = await Log.find(filter)
      .populate("user", "name email")
      .sort(sortOptions)
      .skip((parsedPage - 1) * parsedLimit)
      .limit(parsedLimit);

    const totalLogs = await Log.countDocuments(filter);

    res.status(200).json({
      logs,
      totalLogs,
      totalPages: Math.ceil(totalLogs / parsedLimit),
      currentPage: parsedPage,
    });
  } catch (error) {
    console.error("Error fetching logs:", error.message);
    res.status(500).json({ error: "Failed to fetch logs" });
  }
};

exports.createLog = async (data, req, res) => {
  try {
    const { _id: userId, businessId, role } = req.user;
    const ipAddress = req.ip;
    const userAgent = req.headers;
    const requestId = uuidv4();
    const newLog = await Log.create({
      ...data,
      user: userId,
      businessId,
      role,
      ipAddress,
      userAgent,
      requestId,
    });
    if (!newLog) throw new Error("Failed to create log");

    return newLog;
  } catch (error) {
    console.error("Error creating log:", error.message);
    throw new Error("Failed to create log");
  }
};
