const { v4: uuidv4 } = require("uuid");

const Badge = require("../models/Badge");
const Contact = require("../models/Contact");
const sanitizer = require("sanitizer");
const path = require("path");
const fs = require("fs");

// Function to create the public/badges directory if it doesn't exist
const ensureBadgeDirectory = () => {
  const dir = path.join(__dirname, "../public/badges");
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Create Badge with file upload
exports.createBadge = async (req, res) => {
  try {
    ensureBadgeDirectory();
    const {
      title,
      description,
      skill,
      dateOfIssue,
      dateOfExpiry,
      verified,
      contactId,
      badgeURL,
    } = req.body;
    

    // Generate a unique badgeId
    const badgeId = uuidv4(); // UUID is already a unique identifier

    const newBadge = new Badge({
      title: sanitizer.sanitize(title || ""),
      badgeId,
      description: sanitizer.sanitize(description || ""),
      skill: sanitizer.sanitize(skill || ""),
      dateOfIssue,
      dateOfExpiry,
      verified,
      badgeURL: sanitizer.sanitize(badgeURL || ""),
      contactId,
    });
    console.log("newBadge", newBadge);

    newBadge.createdBy = req.user._id;

    await newBadge.save();
    res.status(201).json(newBadge);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all badges
exports.getAllBadges = async (req, res) => {
  try {
    const { page = 1, limit = 4 } = req.query;

    const parsedPage = parseInt(page);
    const parsedLimit = parseInt(limit);

    const totalBadges = await Badge.countDocuments();

    const badges = await Badge.find()
      .populate("contactId")
      .skip((parsedPage - 1) * parsedLimit)
      .limit(parsedLimit);

    res.status(200).json({
      badges,
      total: totalBadges,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get a single badge by ID
exports.getBadgeById = async (req, res) => {
  try {
    console.log("req.params.id geetam", req.params.id);
    const badge = await Badge.findById(req.params.id).populate("contactId");
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    res.status(200).json(badge);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

//
exports.iscontactExistsInBadge = async (req, res) => {
  try {
    const { contactId, badgeId } = req.params;
    const badge = await Badge.findOne({
      _id: badgeId,
    });
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    const existingContact = badge.contactId.filter(
      (contact) => contact.toString() === contactId.toString()
    );

    if (existingContact.length === 0)
      return res
        .status(200)
        .json({ message: "Contact not found", exists: false });
    res.status(200).json({ message: "Contact found", exists: true });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
// Get a single badge by ID
exports.getPublicBadgeById = async (req, res) => {
  try {
    const badge = await Badge.findById(req.params.id).populate("contactId");
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    res.status(200).json(badge);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
// Remove a single contact from a single badge
exports.removeContactFromBadge = async (req, res) => {
  const { id: badgeId, contactId } = req.params;
  try {
    const badge = await Badge.findById(badgeId);
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    badge.contactId = badge.contactId.filter(
      (contact) => contact.toString() !== contactId.toString()
    );
    await badge.save();

    res
      .status(200)
      .json({ message: "Contact removed from badge successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Add a single contact to a single badge
exports.addContactToBadge = async (req, res) => {
  try {
    const { id: badgeId } = req.params;
    const badge = await Badge.findById(badgeId).populate("contactId");
    if (!badge) return res.status(404).json({ message: "Badge not found" });

    if (req.body.businessEmail) {
      const existingContactInBadge = badge.contactId.filter(
        (contact) => contact.businessEmail === req.body.businessEmail
      );
      if (existingContactInBadge.length > 0) {
        return res
          .status(200)
          .json({ message: "Contact already exists in badge" });
      }
      const existingContact = await Contact.findOne({
        businessEmail: req.body.businessEmail,
      });

      if (existingContact) {
        return res
          .status(200)
          .json({ message: "Contact already exists!  Please add" });
      }
    }
    let newContact = "";
    if (!req.body.contactId) {
      newContact = await Contact.create({
        ...req.body,
        creatorId: req.user._id,
      });
      if (!newContact)
        return res.status(404).json({ message: "Error creating contact" });
    }
    console.log("newContact", newContact);
    console.log("contactId", req.body.contactId);
    badge.contactId.push(newContact ? newContact._id : req.body.contactId);
    await badge.save();

    res.status(200).json({ message: "Contact added to badge successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get a single page badge by ID
exports.getPublicPageBadgeById = async (req, res) => {
  try {
    const badge = await Badge.findById(req.params.id).populate("contactId");

    console.log("badge req received: ");

    if (!badge) return res.status(404).json({ message: "Badge not found" });

    //res.status(200).json(badge);
    //render public html page here
    //res.sendFile(path.join(__dirname,'../' ,'views', 'certificateBadge.html'));

    res.render("public-badge", {
      title: "Badge",
      layout: "layouts/publicPages",
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update a badge
exports.updateBadge = async (req, res) => {
  try {
    const badge = await Badge.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        badgeURL: sanitizer.sanitize(req.body.badgeURL || ""),
        description: sanitizer.sanitize(req.body.description || ""),
        skill: sanitizer.sanitize(req.body.skill || ""),
        title: sanitizer.sanitize(req.body.title || ""),
      },
      {
        new: true,
        runValidators: true,
      }
    );
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    res.status(200).json(badge);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete a badge
exports.deleteBadge = async (req, res) => {
  try {
    const badge = await Badge.findByIdAndDelete(req.params.id);
    if (!badge) return res.status(404).json({ message: "Badge not found" });
    res.status(200).json({ message: "Badge deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update social clicks and total social shares
exports.updateSocialClicks = async (req, res) => {
  try {
    const { id, platform } = req.params;
    const badge = await Badge.findById(id);

    if (!badge) return res.status(404).json({ message: "Badge not found" });

    // Update social clicks for the specified platform
    await badge.updateSocialClicks(platform);

    res.status(200).json({
      message: `${platform} click updated successfully`,
      totalSocialShare: badge.socialCounts.totalSocialShare,
      socialCounts: badge.socialCounts,
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get total count of Badge documents
exports.getBadgeCount = async (req, res) => {
  try {
    const totalCount = await Badge.countDocuments();
    res.json({ totalCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get estimated count of Badge documents
exports.getEstimatedBadgeCount = async (req, res) => {
  try {
    const estimatedCount = await Badge.estimatedDocumentCount();
    res.json({ estimatedCount });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
