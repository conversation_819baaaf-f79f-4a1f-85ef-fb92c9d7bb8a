<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor - MixCertificate</title>
    <!-- Google Fonts for various text styles -->
    <link
        href="https://fonts.googleapis.com/css2?family=Alex+Brush&family=Crimson+Text&family=Great+Vibes&family=Lora&family=Pacifico&family=Roboto&family=Times+New+Roman&display=swap&font-display=swap"
        rel="stylesheet">
    <!-- Material Icons for various UI icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    <!-- Font Awesome for additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- jQuery CDN -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    <!-- Fabric.js CDN for canvas manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js" crossorigin="anonymous"></script>
    <!-- jsPDF CDN for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- jQuery UI for drag-and-drop or other UI interactions -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.contextMenu.min.css">


    <!-- QRCode.js for QR code generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode.js/1.0.0/qrcode.min.js"></script>
    <!-- Tailwind CSS (Browser version for simplicity) -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <!-- Spectrum Color Picker CSS -->
    <link rel="stylesheet" type="text/css"
        href="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.css">
    <!-- Spectrum Color Picker JS -->
    <script src="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.js"></script>
    <style>
        body {
            font-family: "Inter", sans-serif;
            overflow: hidden;
        }

        .sidebar {

            color: #cbd5e0;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .sidebar::-webkit-scrollbar {
            width: 0;
            height: 0;
        }

        .sidebar {
            scrollbar-width: none;
        }

        .sidebar {
            -ms-overflow-style: none;
        }

        .sidebar .editor-sidebar-items:hover {

            color: #ffffff;
        }

        .sidebar .editor-sidebar-items.active {

            color: #ffffff;
        }

        .editor-sidebar-items {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        .sidebar-content-area {

            color: #cbd5e0;
            transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
            opacity: 0;
            pointer-events: none;
            transform: translateY(100%);
            height: 90%;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }

        .sidebar-inner-area::-webkit-scrollbar {
            width: 12px;
            background-color: #374151;
            border-radius: 6px;
        }

        .sidebar-inner-area::-webkit-scrollbar-track {
            background-color: #374151;
            border-radius: 6px;
        }

        .sidebar-inner-area::-webkit-scrollbar-thumb {
            background-color: #9ca3af;
            border-radius: 6px;
            border: 3px solid #374151;

        }

        .sidebar-inner-area::-webkit-scrollbar-thumb:hover {
            background-color: #aeb5bf;
        }


        @media (min-width: 768px) {
            .sidebar-content-area {
                transform: translateX(-100%);
                width: 0;
                height: auto;
                overflow-y: auto;
            }

            .sidebar-content-area.sidebar-open {
                opacity: 1;
                pointer-events: auto;
                transform: translateX(0%);

            }
        }

        .sidebar-content-area h2 {
            color: #ffffff;
        }

        .sidebar-content-area p {
            color: #cbd5e0;
        }

        #close-sidebar-content {
            color: #9ca3af;
        }

        #close-sidebar-content:hover {
            color: #ffffff;
        }

        #backgrounds-content input[type="text"] {
            background-color: #424751;
            border: 1px solid #4a5568;
            color: #ffffff;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            flex: 1;
            font-size: 0.875rem;
        }

        #backgrounds-content input[type="text"]::placeholder {
            color: #a0aec0;
        }

        #backgrounds-content button {
            background-color: #4a5568;
            color: #ffffff;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: background-color 0.2s;
        }

        #backgrounds-content button:hover {
            background-color: #64748b;
        }

        .material-icons,
        .fas,
        .far,
        .fab {
            color: inherit;
        }

        .editor-sidebar-items {
            min-height: 50px;
        }

        .draggable-item {
            cursor: grab;
            user-select: none;
            -webkit-user-drag: element;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #5b5f67;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #5b5f67;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
        }

        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: -0.125em;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to {
                transform: rotate(360deg);
            }
        }

        .canvas-container {
            position: absolute !important;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: block;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background-color: white;
        }

        .canvas-container .lower-canvas,
        .canvas-container .upper-canvas {
            position: absolute !important;
            left: 0 !important;
            top: 0 !important;
            width: 100% !important;
            height: 100% !important;
        }



        #object-toolbar.center-global {
            justify-content: center;
        }

        .icon-sm,
        .icon-sm-2 {
            font-size: 8px;
            cursor: pointer;
        }

        .btn-icon,
        .btn-icon-2 {
            padding: 0px 8px;
        }

        #drop-area.highlight {
            border-color: #6366f1;

            background-color: #374151;
            /* Gray-700 */
        }

        .imageWrapper:hover .btn-icon-2 {
            opacity: 1;
        }

        .context-menu-item {
            padding: 2px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #333;

        }

        /* Style for separators */
        .context-menu-item.context-menu-separator {

            border-top: 1px solid #eee;
            padding: 0;
        }

        /* Style for the shortcut text (e.g., Ctrl+C) */
        .context-menu-item .shortcut {
            margin-left: auto;
            font-size: 0.8em;
            color: #999;
        }

        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: -0.125em;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="w-full h-full bg-[#f5f5f5]">
        <!-- Navbar -->
        <nav class="bg-white flex items-center justify-between py-2 sm:py-3 px-2 sm:px-4 shadow-sm relative z-20">
            <div class="flex items-center gap-2">
                <a href="/designs/" class="flex items-center">
                    <img src="/assets/images/logo-sm.svg" alt="Logo" width="30" height="30" class="w-[25px]"
                        title="mixcertificate">
                    <span class="font-bold hidden">MixCertificate</span>
                </a>
                <input type="text" id="designName" placeholder="Design Name" value="Untitled Design"
                    class="h-10 leading-10 text-gray-600 text-left outline-none w-32 sm:w-48 border-none focus:ring-0 focus:outline-none bg-transparent"
                    onfocus="this.style.border='none'" onblur="this.style.border='none'">
            </div>
            <!-- Undo/Redo Buttons -->
            <div class="flex items-center gap-2 sm:gap-2 px-2" data-group-name="History">
                <button id="undo-button"
                    class="flex items-center justify-center rounded-md hover:bg-gray-100 text-gray-700 p-2"
                    title="Undo">
                    <i class="material-icons text-xl">undo</i>
                </button>
                <button id="redo-button"
                    class="flex items-center justify-center rounded-md hover:bg-gray-100 text-gray-700 p-2"
                    title="Redo">
                    <i class="material-icons text-xl">redo</i>
                </button>
            </div>
            <div class="flex items-center gap-2">
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button"
                        class="text-gray-700 focus:outline-none p-2 rounded-md hover:bg-gray-100">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
                <!-- Desktop Action Buttons -->
                <div id="desktop-action-buttons" class="hidden md:flex items-center gap-2">
                    <button id="saveCanvas"
                        class="flex items-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>Save
                    </button>
                    <button id="previewBtn"
                        class="flex items-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200">
                        <i class="fas fa-eye mr-2"></i>Preview
                    </button>
                    <!-- Download Dropdown -->
                    <div class="relative group">
                        <button
                            class="flex items-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200"
                            type="button" id="downloadDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download mr-2"></i>Download
                        </button>
                        <ul class="absolute hidden bg-white shadow-lg rounded-md mt-1 w-48 z-10 group-hover:block right-0"
                            aria-labelledby="downloadDropdown">
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadPngBtn"
                                    href="#">Download PNG</a></li>
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadJpegBtn"
                                    href="#">Download JPEG</a></li>
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadPdfBtn"
                                    href="#">Download PDF</a></li>
                            <li>
                                <hr class="border-t border-gray-200 my-1">
                            </li>
                            <li class="px-4 py-2 text-xs text-gray-500 uppercase tracking-wider">For Print (300 DPI)
                            </li>
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadPngBtn"
                                    href="#">Download PNG</a></li>
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadJpegBtn"
                                    href="#">Download JPEG</a></li>
                            <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadPdfBtn"
                                    href="#">Download PDF</a></li>
                        </ul>
                    </div>
                    <!-- Create Dropdown -->
                    <div class="relative group">
                        <button
                            class="flex items-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200"
                            type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-plus mr-2"></i>Create
                        </button>
                        <ul class="absolute hidden bg-white shadow-lg rounded-md mt-1 w-64 z-10 right-0 group-hover:block"
                            aria-labelledby="dropdownMenuButton">
                            <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                    data-type="horizontal" onclick="openEditor('horizontal')">
                                    <i class="fas fa-arrows-alt-h mr-2"></i> Horizontal Certificate
                                </a></li>
                            <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                    data-type="vertical" onclick="openEditor('vertical')">
                                    <i class="fas fa-arrows-alt-v mr-2"></i> Vertical Certificate
                                </a></li>
                            <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                    data-type="badge" onclick="openEditor('badge')">
                                    <i class="fas fa-badge mr-2"></i> Badge
                                </a></li>
                            <li>
                                <hr class="border-t border-gray-200 my-1">
                            </li>
                            <li>
                                <div class="px-3 py-2">
                                    <input type="number" id="customWidth"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md mb-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Width (px)">
                                    <input type="number" id="customHeight"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Height (px)">
                                    <button
                                        class="w-full mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                                        onclick="createCustomDesign()">Create</button>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Mobile Menu Dropdown -->
        <div id="mobile-menu-dropdown"
            class="md:hidden bg-white shadow-lg absolute top-[56px] left-0 right-0 z-10 hidden">
            <div class="flex flex-col p-4 gap-2">
                <button id="saveCanvas-mobile"
                    class="flex items-center justify-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200 w-full">
                    <i class="fas fa-save mr-2"></i>Save
                </button>
                <button id="previewBtn-mobile"
                    class="flex items-center justify-center px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200 w-full">
                    <i class="fas fa-eye mr-2"></i>Preview
                </button>
                <div class="relative w-full">
                    <button
                        class="flex items-center justify-center w-full px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200"
                        type="button" id="downloadDropdown-mobile" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <ul class="hidden bg-white shadow-lg rounded-md mt-1 w-full z-10 absolute"
                        aria-labelledby="downloadDropdown-mobile">
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadPngBtn-mobile"
                                href="#">Download PNG</a></li>
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadJpegBtn-mobile"
                                href="#">Download JPEG</a></li>
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="downloadPdfBtn-mobile"
                                href="#">Download PDF</a></li>
                        <li>
                            <hr class="border-t border-gray-200 my-1">
                        </li>
                        <li class="px-4 py-2 text-xs text-gray-500 uppercase tracking-wider">For Print (300 DPI)</li>
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadPngBtn-mobile"
                                href="#">Download PNG</a></li>
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadJpegBtn-mobile"
                                href="#">Download JPEG</a></li>
                        <li><a class="block px-4 py-2 text-gray-700 hover:bg-gray-100" id="printDownloadPdfBtn-mobile"
                                href="#">Download PDF</a></li>
                    </ul>
                </div>
                <div class="relative w-full">
                    <button
                        class="flex items-center justify-center w-full px-4 py-2 bg-white text-gray-800 rounded-md border border-gray-300 hover:bg-gray-100 shadow-sm transition-colors duration-200"
                        type="button" id="dropdownMenuButton-mobile" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-plus mr-2"></i>Create
                    </button>
                    <ul class="hidden bg-white shadow-lg rounded-md mt-1 w-full z-10 absolute"
                        aria-labelledby="dropdownMenuButton-mobile">
                        <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                data-type="horizontal" onclick="openEditor('horizontal')"><i
                                    class="fas fa-arrows-alt-h mr-2"></i> Horizontal Certificate</a></li>
                        <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                data-type="vertical" onclick="openEditor('vertical')"><i
                                    class="fas fa-arrows-alt-v mr-2"></i> Vertical Certificate</a></li>
                        <li><a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100" href="#"
                                data-type="badge" onclick="openEditor('badge')"><i class="fas fa-badge mr-2"></i>
                                Badge</a></li>
                        <li>
                            <hr class="border-t border-gray-200 my-1">
                        </li>
                        <li>
                            <div class="px-3 py-2">
                                <input type="number" id="customWidth-mobile"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md mb-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Width (px)">
                                <input type="number" id="customHeight-mobile"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Height (px)">
                                <button
                                    class="w-full mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                                    onclick="createCustomDesign()">Create</button>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- Main Editor Area -->
        <div class="fixed  w-full h-full">
            <div class="flex w-full bg-zinc-800 h-full max-h-[100%]">

                <!-- Sidebar -->
                <div class="sidebar shadow-lg overflow-y-auto   flex-nowrap whitespace-nowrap flex md:flex-col  md:py-4 md:space-y-2 md:px-4 md:static md:w-26 
            fixed bottom-0 left-0 w-full min-w-[90px] bg-zinc-900 z-50 flex-row items-center gap-2 py-1 px-1"
                    id="sidebar">
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group active md:w-auto md:p-3"
                        data-target="#design-content">
                        <span class="material-icons text-2xl group-hover:text-white">home</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Design</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 hover:text-white transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#ai-templates-panel">
                        <span class="material-icons text-2xl group-hover:text-white">auto_awesome</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">AI Designer</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#badges-content">
                        <span class="material-icons text-2xl group-hover:text-white">verified</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Background</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#elements-content">
                        <span class="material-icons text-2xl group-hover:text-white">category</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Elements</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#text-content">
                        <span class="material-icons text-2xl group-hover:text-white">text_fields</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Text</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#brand-content">
                        <span class="material-icons text-2xl group-hover:text-white">branding_watermark</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Brand</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#image-upload-content">
                        <span class="material-icons text-2xl group-hover:text-white">cloud_upload</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Uploads</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#frames-content">
                        <span class="material-icons text-2xl group-hover:text:white block md:block">crop</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Frames</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#qr-code-content">
                        <span class="material-icons text-2xl group-hover:text-white">grid_on</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">QR Code</div>
                    </button>

                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#photos-content">
                        <span class="material-icons text-2xl group-hover:text-white">wallpaper</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Photos</div>


                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#shapes-elements-content">
                        <span class="material-icons text-2xl group-hover:text-white">view_module</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Icons</div>
                    </button>
                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#variable-gifs-content">
                        <span class="material-icons text-2xl group-hover:text-white">insert_emoticon</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Variables</div>
                    </button>

                    <button
                        class="editor-sidebar-items flex flex-col items-center justify-center p-3 w-full text-gray-400 transition-colors duration-200 group md:w-auto md:p-3"
                        data-target="#stickers-gifs-content">
                        <span class="material-icons text-2xl group-hover:text-white">insert_emoticon</span>
                        <div class="sidebar-text text-xs mt-1 group-hover:text-white block md:block">Stickers &amp; GIFs
                        </div>
                    </button>
                </div>

                <!-- Sidebar Content Area -->
                <div id="sidebar-content-area"
                    class=" bg-zinc-800 min-w-[280px]   shadow-lg  md:relative transition-transform duration-300 ease-in-out md:w-[30%] w-full text-white z-20 absolute bottom-0 h-[80%] md:h-[auto] ">
                    <button id="close-sidebar-content"
                        class="cursor-pointer absolute left-1/2 -translate-x-1/2 md:-translate-y-1/2 md:top-1/2 md:-right-[10px] md:left-[unset] top-1 p-1 rounded-full bg-zinc-600 shadow-md flex items-center justify-center focus:outline-none z-10">
                        <span class="material-icons rotate-[271deg] md:rotate-0 text-white ">chevron_left</span>
                    </button>
                    <div class="overflow-y-auto h-full sidebar-inner-area px-4 pb-38 pt-9 md:pb-18">
                        <!-- Ai Template -->
                        <div id="ai-templates-panel" class="sidebar-content hidden">
                            <div class="h-full flex flex-col">
                                <div class="flex justify-between items-center mb-6">
                                    <h2 class="text-xl font-bold text-white">Generate Certificate Template with AI</h2>
                                </div>

                                <div class="flex mb-4 border-b border-gray-700">
                                    <button id="automatic-tab-btn"
                                        class="flex-1 py-2 text-center text-white font-semibold border-b-2 border-blue-500 hover:bg-zinc-700 transition-colors duration-200"
                                        data-tab="automatic">Professional</button>
                                    <button id="manual-tab-btn"
                                        class="flex-1 py-2 text-center text-gray-400 hover:text-white hover:bg-zinc-700 transition-colors duration-200"
                                        data-tab="manual">Quick</button>
                                </div>

                                <div class="flex-1 overflow-y-auto pr-2 custom-scrollbar-ai">
                                    <div id="automatic-tab-content" class="tab-content">
                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 1: Choose Certificate
                                                Type</h3>
                                            <div class="relative">
                                                <select id="certificate-type-dropdown"
                                                    class="w-full p-2  rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500 appearance-none">
                                                    <option value="completion">Completion</option>
                                                    <option value="achievement">Achievement</option>
                                                    <option value="participation">Participation</option>
                                                    <option value="appreciation">Appreciation</option>
                                                    <option value="graduation">Graduation</option>
                                                    <option value="custom">Custom</option>
                                                </select>
                                                <div
                                                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                                                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                                        viewBox="0 0 20 20">
                                                        <path
                                                            d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 2: Define Purpose
                                            </h3>
                                            <input type="text" id="certificate-purpose-input"
                                                class="w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                                                placeholder="e.g., To certify completion of the program">
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 3: Event Name</h3>
                                            <input type="text" id="event-name-input"
                                                class="w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                                                placeholder="e.g., Winter Commencement 2024">
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 4: Select Style
                                                (Theme)</h3>
                                            <div class="grid grid-cols-2 gap-3" id="style-selection-container">
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="professional">Professional</button>
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="minimal">Minimal</button>
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="elegant">Elegant</button>
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="modern">Modern</button>
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="creative">Creative</button>
                                                <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="academic">Academic</button> <button
                                                    class="ai-style-pill py-1 px-2 rounded-lg bg-zinc-700 text-white hover:bg-zinc-600 transition-colors duration-200 text-center"
                                                    data-style="classic">Classic</button>
                                            </div>
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 5: Add Logo (URL)
                                            </h3>
                                            <input type="url" id="logo-image-url-input"
                                                class="w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                                                placeholder="e.g., https://example.com/your_logo.png">
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 6: Signatures</h3>
                                            <div class="flex items-center mb-3">
                                                <label for="num-signatures-input" class="text-white text-sm mr-2">Number
                                                    of Signatures:</label>
                                                <input type="number" id="num-signatures-input" min="0" max="5" value="1"
                                                    class="w-20 p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500">
                                            </div>
                                            <div id="signatures-container">
                                            </div>
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Step 7: Add Optional Brand
                                                Color</h3>
                                            <p class="text-gray-400 text-sm mb-2">Optional: Let AI blend your brand’s
                                                color into the design</p>
                                            <input type="text" id="brand-color-picker"
                                                class="w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                                                placeholder="Pick a color (e.g., #FF0000, blue)">
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3"> Step 8: Position
                                                Preference</h3>
                                            <div class="space-y-2">
                                                <label class="flex items-center text-white cursor-pointer">
                                                    <input type="radio" name="position-preference" value="centered"
                                                        class="form-radio h-4 w-4 text-blue-500 border-gray-600 bg-zinc-700 focus:ring-blue-500">
                                                    <span class="ml-2 text-sm">Centered Layout</span>
                                                </label>
                                                <label class="flex items-center text-white cursor-pointer">
                                                    <input type="radio" name="position-preference" value="left-aligned"
                                                        class="form-radio h-4 w-4 text-blue-500 border-gray-600 bg-zinc-700 focus:ring-blue-500">
                                                    <span class="ml-2 text-sm">Left-Aligned</span>
                                                </label>
                                                <label class="flex items-center text-white cursor-pointer">
                                                    <input type="radio" name="position-preference" value="right-aligned"
                                                        class="form-radio h-4 w-4 text-blue-500 border-gray-600 bg-zinc-700 focus:ring-blue-500">
                                                    <span class="ml-2 text-sm">Right-Aligned</span>
                                                </label>
                                                <label class="flex items-center text-white cursor-pointer">
                                                    <input type="radio" name="position-preference" value="ai-decide"
                                                        class="form-radio h-4 w-4 text-blue-500 border-gray-600 bg-zinc-700 focus:ring-blue-500"
                                                        checked>
                                                    <span class="ml-2 text-sm">Let AI decide</span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-6 flex items-center">
                                            <input type="checkbox" id="smart-mode-toggle"
                                                class="form-checkbox h-5 w-5 text-blue-500 rounded border-gray-600 bg-zinc-700 focus:ring-blue-500"
                                                checked>
                                            <label for="smart-mode-toggle"
                                                class="ml-2 text-sm text-white cursor-pointer">Let AI optimize layout &
                                                fonts for me</label>
                                        </div>

                                        <div class="mt-auto pt-4 border-t border-gray-700">
                                            <button id="generate-automatic-template-btn"
                                                class="w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-blue-700 transition-colors duration-200">
                                                Generate Professional Template
                                            </button>
                                            <div id="ai-loading-spinner-automatic"
                                                class="hidden text-center text-white mt-3">
                                                <div class="spinner-border text-blue-500" role="status">
                                                    <span class="sr-only">Loading...</span>
                                                </div>
                                                <p class="mt-2 text-gray-300">Designing your certificate...</p>
                                            </div>
                                            <button id="regenerate-automatic-template-btn"
                                                class="hidden w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-blue-700 transition-colors duration-200 mt-2">
                                                Regenerate Professional
                                            </button>
                                            <button id="revise-automatic-template-btn"
                                                class="hidden w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-blue-700 transition-colors duration-200 mt-2">
                                                Revise Professional Design
                                            </button>
                                        </div>
                                    </div>

                                    <div id="manual-tab-content" class="tab-content hidden">
                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Enter Custom Prompt</h3>
                                            <textarea id="manual-prompt-input"
                                                class="w-full p-3 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500 h-32"
                                                placeholder="Describe the certificate template you want to generate. E.g., 'A modern certificate for a coding competition winner with a blue and white theme and a minimalist layout.'"></textarea>
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Upload Logo
                                            </h3>
                                            <p class="text-gray-400 text-sm mb-2">Upload a logo for AI to use as a
                                                reference.</p>
                                            <div id="manual-upload-area"
                                                class="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center text-gray-400 cursor-pointer hover:border-blue-500 hover:text-white transition-colors duration-200">
                                                <span class="material-icons text-4xl mb-2">cloud_upload</span>
                                                <p class="mb-2">Drag & drop image here or</p>
                                                <input type="file" id="manual-file-upload-input" class="hidden"
                                                    accept="image/*">
                                                <button id="manual-browse-files-button"
                                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none">
                                                    Browse Files
                                                </button>
                                                <p class="text-xs mt-2 text-gray-500">Supported formats: JPG, PNG</p>
                                            </div>
                                            <div id="manual-image-preview-container" class="mt-4 hidden">
                                                <img id="manual-image-preview" src="" alt="Uploaded Image Preview"
                                                    class="max-w-full h-auto rounded-md border border-gray-600">
                                                <p id="manual-image-filename"
                                                    class="text-sm text-gray-300 mt-2 truncate"></p>
                                                <p id="manual-image-resolution" class="text-xs text-gray-400"></p>
                                                <button id="manual-remove-image-btn"
                                                    class="mt-2 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors duration-200">Remove
                                                    Image</button>
                                            </div>
                                        </div>

                                        <div class="mb-6">
                                            <h3 class="text-lg font-semibold text-white mb-3">Optional Settings</h3>
                                            <div class="mb-4">
                                                <p class="text-gray-400 text-sm mb-2">Let AI blend your brand’s color
                                                    into the design</p>
                                                <input type="text" id="manual-brand-color-picker"
                                                    class="w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                                                    placeholder="Pick a color">
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="manual-smart-mode-toggle"
                                                    class="form-checkbox h-5 w-5 text-blue-500 rounded border-gray-600 bg-zinc-700 focus:ring-blue-500"
                                                    checked>
                                                <label for="manual-smart-mode-toggle"
                                                    class="ml-2 text-sm text-white cursor-pointer">Let AI optimize
                                                    layout & fonts for me</label>
                                            </div>
                                        </div>

                                        <div class="mt-auto pt-4 border-t border-gray-700">
                                            <button id="generate-manual-template-btn"
                                                class="w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-blue-700 transition-colors duration-200">
                                                Generate Quick Template
                                            </button>
                                            <div id="ai-loading-spinner-manual"
                                                class="hidden text-center text-white mt-3">
                                                <div class="spinner-border text-green-500" role="status">
                                                    <span class="sr-only">Loading...</span>
                                                </div>
                                                <p class="mt-2 text-gray-300">Generating based on your prompt...</p>
                                            </div>
                                            <button id="regenerate-manual-template-btn"
                                                class="hidden w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-blue-700 transition-colors duration-200 mt-2">
                                                Regenerate Quick
                                            </button>
                                            <button id="revise-manual-template-btn"
                                                class="hidden w-full py-2 bg-blue-600 text-white font-bold rounded-md hover:bg-bluetransition-colors duration-200 mt-2">
                                                Revise Quick Design
                                            </button>
                                        </div>
                                    </div>

                                    <div id="ai-generated-design-preview"
                                        class="hidden mt-6 pt-4 border-t border-gray-700">
                                        <h3 class="text-lg font-semibold text-white mb-3">Generated Design Preview</h3>
                                        <div class="bg-zinc-700 p-3 rounded-md flex justify-center items-center">
                                            <img id="generated-design-img" src="" alt="Generated Certificate Design"
                                                class="max-w-full h-auto rounded-md border border-gray-600">
                                        </div>
                                        <p id="generated-design-message"
                                            class="text-green-400 text-sm mt-2 text-center hidden">Template generated
                                            successfully!</p>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Text Content -->
                        <div id="text-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Text Tools</h2>
                            <p class="text-gray-300">Add and format text.</p>
                            <div class="grid gap-2 my-2">
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-4xl draggable-item"
                                    id="addH1" data-header="h1" data-type="text-heading" data-text="Add a Heading">
                                    Header 1
                                </button>
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-3xl draggable-item"
                                    id="addH2" data-header="h2" data-type="text-heading" data-text="Add a Subheading">
                                    Header 2
                                </button>
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-2xl draggable-item"
                                    id="addH3" data-header="h3" data-type="text-heading" data-text="Add a Title">
                                    Header 3
                                </button>
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-xl draggable-item"
                                    id="addH4" data-header="h4" data-type="text-heading"
                                    data-text="Add a Section Title">
                                    Header 4
                                </button>
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-lg draggable-item"
                                    id="addH5" data-header="h5" data-type="text-heading" data-text="Add a Small Title">
                                    Header 5
                                </button>
                                <button
                                    class="w-full text-white border border-gray-400 hover:bg-zinc-700 p-2 rounded-md text-base my-1 draggable-item"
                                    id="addpara" data-header="p" data-type="text-body" data-text="Add body text">
                                    Paragraph
                                </button>

                            </div>
                        </div>
                        <!-- Elements Content (Shapes) -->
                        <div id="elements-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Elements</h2>
                            <p class="text-gray-300">Add shapes, lines, and other elements.</p>
                            <div id="shape-toolbar-bordered" class="flex flex-wrap gap-2 mt-2" role="group"
                                aria-label="Shape Toolbar Bordered">
                                <button id="addRectangleBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-blue-500 text-[#007bff] p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="rect" data-fill="#007bff" data-stroke="#007bff" data-stroke-width="2">
                                    <span class="material-icons">rectangle</span>
                                </button>
                                <button id="addCircleBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-green-500 text-[#28a745] p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="circle" data-fill="#28a745" data-stroke="#28a745" data-stroke-width="2">
                                    <i class="fas fa-circle"></i>
                                </button>
                                <button id="addTriangleBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-yellow-500 p-2 text-[#ffc107] rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="triangle" data-fill="#ffc107" data-stroke="#ffc107"
                                    data-stroke-width="2">
                                    <i class="fas fa-caret-up"></i>
                                </button>
                                <button id="addEllipseBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-purple-500 p-2 text-[#6f42c1] rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="ellipse" data-fill="#6f42c1" data-stroke="#6f42c1" data-stroke-width="2">
                                    <span class="material-icons">ellipse</span>
                                </button>
                                <button id="addLineBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-orange-500 p-2 text-[#fd7e14] rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="line" data-stroke="#fd7e14" data-stroke-width="2">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button id="addPolygonBorderedBtn" type="button"
                                    class="w-[60px] h-[60px]  border-2 border-red-500 p-2 text-[#dc3545] rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="polygon" data-fill="#dc3545" data-stroke="#dc3545" data-stroke-width="2">
                                    <i class="fas fa-square"></i>
                                </button>
                            </div>
                            <div id="shape-toolbar-filled" class="mt-2 flex flex-row flex-wrap gap-2" role="group"
                                aria-label="Shape Toolbar Filled">
                                <button id="addRectangleBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="rect" data-fill="#6c757d">
                                    <span class="material-icons">rectangle</span>
                                </button>
                                <button id="addCircleBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="circle" data-fill="#6c757d">
                                    <span class="material-icons">circle</span>
                                </button>
                                <button id="addTriangleBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="triangle" data-fill="#6c757d">
                                    <span class="material-icons">change_history</span>
                                </button>
                                <button id="addEllipseBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="ellipse" data-fill="#6c757d">
                                    <span class="material-icons">circle</span>
                                </button>
                                <button id="addLineBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="line" data-stroke="#6c757d" data-stroke-width="2">
                                    <span class="material-icons">remove</span>
                                </button>
                                <button id="addPolygonBtn" type="button"
                                    class="w-[60px] h-[60px] bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md flex items-center justify-center text-4xl draggable-item"
                                    data-type="polygon" data-fill="#6c757d">
                                    <i class="fa-solid fa-square"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Design Content (Backgrounds) -->
                        <div id="design-content" class="sidebar-content">
                            <h5 class="text-xl font-semibold text-white mb-4">Certificate Backgrounds</h5>
                            <div class="flex items-center gap-2 mb-4 flex-wrap">
                                <input type="text" id="designSearchQuery" placeholder="Search backgrounds..."
                                    value="certificate"
                                    class="flex-1 p-2 rounded-md bg-[#424751] text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:border-blue-500">
                                <button id="designSearchButton"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">Search</button>
                            </div>
                            <div id="designSearchResults" class="mt-4 grid grid-cols-2 gap-2">
                                <!-- Background templates will be loaded here -->
                                <div id="designLoading" class="hidden text-center mt-4 w-full">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="badges-content" class="sidebar-content">
                            <h5 class="text-xl font-semibold text-white mb-4">Background</h5>
                            <div class="flex items-center gap-2 mb-4  flex-wrap">
                            </div>

                        </div>
                        <!-- Photos -->
                        <div id="photos-content" class="sidebar-content hidden">
                            <h5 class="text-xl font-semibold text-white mb-4">Photos</h5>
                            <div class="flex items-center gap-2 mb-4 flex-wrap">
                                <input type="text" id="photosSearchQuery" placeholder="Search Photos..." value="peace"
                                    class="flex-1 p-2 rounded-md bg-[#424751] text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:border-blue-500">
                                <button id="photoSearchButton"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">Search</button>
                            </div>
                            <div id="photoSearchResults" class="mt-4 grid grid-cols-2 gap-2 ">
                                <!-- Photo search results will be displayed here -->
                            </div>
                            <div id="photoLoading" class="hidden text-center mt-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <!-- Brand Kit Content -->
                        <div id="brand-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Brand Kit</h2>
                            <p class="text-gray-300">Manage your brand logos, colors, and fonts.</p>
                        </div>
                        <!-- Image Upload Content -->
                        <div id="image-upload-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Upload Your Images</h2>
                            <div id="drop-area"
                                class="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center text-gray-400 cursor-pointer hover:border-gyay-700 hover:text-white transition-colors duration-200">
                                <span class="material-icons text-4xl mb-2">cloud_upload</span>
                                <p class="mb-2">Drag and drop your images here</p>
                                <p class="mb-2">or</p>
                                <input type="file" id="file-upload-input" class="hidden" accept="image/*" multiple>
                                <button id="browse-files-button"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none">
                                    Click to browse files
                                </button>
                                <p class="text-xs mt-2 text-gray-500">Supported formats: JPG, PNG, GIF</p>
                            </div>
                            <div id="image-loader" class=" justify-center items-center py-4 hidden">
                                <div class="spinner-border " role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                            <div id="uploadedImages" class="mt-3 grid grid-cols-2 gap-2">
                                <!-- Uploaded image previews will be added here -->
                                <div id="uploadedImagesLoading" class="hidden text-center mt-4 w-full">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Frames Content -->
                        <div id="frames-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Frames</h2>
                            <p class="text-gray-300">Choose from various frames for your images.</p>
                        </div>
                        <!-- QR Code Content -->
                        <div id="qr-code-content" class="sidebar-content hidden">
                            <h5 class="text-xl font-semibold mb-4 text-white">Generate QR codes for your links</h5>
                            <div class="flex items-center gap-2 mb-4 flex-wrap">
                                <input type="text" id="qrCodeUrlInput" placeholder="Enter URL here"
                                    class="flex-1 p-2 rounded-md bg-[#424751] text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:border-blue-500">
                                <button id="generateQrCodeBtn"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">Generate</button>
                            </div>
                            <div id="qrCodeDisplay" class="mt-4 flex justify-center items-center">
                                <!-- QR Code will be displayed here -->
                                <div id="qrCodeLoading" class="hidden text-center mt-4 w-full">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Shapes & Elements Content (Icons) -->
                        <div id="shapes-elements-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Icons</h2>
                            <div class="flex items-center gap-2 mb-4 flex-wrap ">
                                <input type="text" id="icons-search-input" placeholder="Search icons..."
                                    class="flex-1 p-2 rounded-md bg-[#424751] text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:border-blue-500 "
                                    value="medal">
                                <button id="searchIconBtn"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">Search</button>
                            </div>
                            <div id="iconResults" class="mt-1 flex flex-wrap gap-1 ">
                                <!-- Icon search results will be displayed here -->
                            </div>
                            <div id="iconLoading" class="hidden text-center mt-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <!-- Variable Gifs Content -->
                        <div id="variable-gifs-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Variables</h2>
                            <div class="flex items-center gap-2 mb-4 flex-wrap ">
                                <input type="text" id="variable-name-input" placeholder="Add Variable"
                                    class="flex-1 p-2 rounded-md bg-[#424751] text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button id="add-variable-btn"
                                    class="px-4 py-2 bg-zinc-600 text-white rounded-md hover:bg-gray-500 transition-colors duration-200">Add
                                    Variable</button>
                            </div>
                            <div id="components-list" class="mt-4 flex flex-wrap gap-2">
                                <!-- Predefined and custom variables will be rendered here -->
                                <div id="variablesLoading" class="hidden text-center mt-4 w-full">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Stickers & Gifs Content -->
                        <div id="stickers-gifs-content" class="sidebar-content hidden">
                            <h2 class="text-xl font-semibold mb-4 text-white">Stickers & GIFs</h2>
                            <p class="text-gray-300">Add fun stickers and GIFs.</p>
                            <!-- Removed duplicate icon search elements from here -->
                        </div>
                    </div>
                </div>
                <!-- Main Content Wrapper -->
                <div id="main-content-wrapper"
                    class=" flex flex-col  w-full transition-all duration-300 ease-in-out h-[80vh] md:h-auto">
                    <!-- Object Toolbar (Dynamic based on selection) -->


                    <div id="object-toolbar"
                        class="bg-gradient-to-r from-zinc-700 to-zinc-900 py-3 px-6 shadow-lg md:justify-center flex items-center gap-6 z-10 overflow-y-hidden overflow-x-auto whitespace-nowrap max-w-full w-full text-white center-global"
                        style="scrollbar-width: thin; scrollbar-color: #9ca3af #374151;">


                        <!-- Global Controls -->
                        <div id="global-controls" class="flex items-center gap-3 " data-toolbar-group="global">
                            <label for="bgColorPicker" class="text-xs md:text-sm font-medium">Background:</label>
                            <input type="color" id="bgColorPicker" value="#ffffff"
                                class="w-10 h-10 rounded-md cursor-pointer border-none">
                        </div>

                        <!-- Text Formatting Controls -->
                        <div id="text-formatting" class="flex items-center gap-3 whitespace-nowrap  "
                            data-toolbar-group="text">
                            <select id="fontFamily"
                                class="bg-zinc-600 text-white py-1 px-2 rounded-md text-xs md:text-sm ">
                                <option value="Roboto">Roboto</option>
                                <option value="Arial">Arial</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Pacifico">Pacifico</option>
                                <option value="Great Vibes">Great Vibes</option>
                                <option value="Lora">Lora</option>
                                <option value="Crimson Text">Crimson Text</option>
                            </select>
                            <input type="number" id="fontSizeInput" min="8" max="100" value="24"
                                class="w-16 bg-zinc-600 text-white py-1 px-2 rounded-md text-sm ">
                            <input type="color" id="fontColorPicker" value="#000000"
                                class="w-10 h-10 rounded-md cursor-pointer border-none">

                            <!-- Text Styles -->
                            <button id="boldBtn" class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"><i
                                    class="fas fa-bold"></i></button>
                            <button id="italicBtn" class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"><i
                                    class="fas fa-italic"></i></button>
                            <button id="underlineBtn"
                                class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"><i
                                    class="fas fa-underline"></i></button>
                            <button id="strikethroughBtn"
                                class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"><i
                                    class="fas fa-strikethrough"></i></button>

                            <div class="relative">
                                <button id="textAlignToggle"
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"
                                    title="Text Alignment">
                                    <i class="fas fa-align-left"></i>
                                    <!-- Default icon, will update based on selection -->
                                </button>

                            </div>


                            <!-- Merged Text Properties Control (Font Thickness, Line Height, Letter Spacing) -->
                            <div class="relative">
                                <button id="textPropertiesToggle"
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors"
                                    title="Text Properties">
                                    <i class="fas fa-text-height"></i> <!-- Generic icon for text properties -->
                                </button>

                            </div>

                        </div>

                        <!-- Element Colors and Border Controls -->
                        <div id="element-colors" class="flex items-center gap-3 whitespace-nowrap"
                            data-toolbar-group="element">
                            <label for="elementFillPicker" class="text-xs md:text-sm font-medium">Fill:</label>
                            <input type="color" id="elementFillPicker" value="#cccccc"
                                class="w-10 h-10 rounded-md cursor-pointer border-none">
                            <label for="borderColorPicker" class="text-xs md:text-sm font-medium">Border:</label>
                            <input type="color" id="borderColorPicker" value="#000000"
                                class="w-10 h-10 rounded-md cursor-pointer border-none">
                            <label for="borderThickness" class="text-xs md:text-sm font-medium">Thickness:</label>
                            <input type="number" id="borderThickness" min="0" max="10" value="0"
                                class="w-16 bg-zinc-600 text-white p-2 rounded-md text-sm ">
                            <select id="borderType" class="bg-zinc-600 text-white p-2 rounded-md text-sm ">
                                <option value="solid">Solid</option>
                                <option value="dashed">Dashed</option>
                                <option value="dotted">Dotted</option>
                            </select>
                            <label for="elementBorderRadius" class="text-xs md:text-sm font-medium">Radius:</label>
                            <input type="number" id="elementBorderRadius" min="0" max="100" value="0"
                                class="w-16 bg-zinc-600 text-white p-2 rounded-md text-sm ">
                        </div>

                        <!-- Image Controls -->
                        <div id="image-controls" class="flex items-center gap-3 whitespace-nowrap "
                            data-toolbar-group="image">
                            <label for="imageBorderColor" class="text-xs md:text-sm  font-medium">Border:</label>
                            <input type="color" id="imageBorderColor" value="#000000"
                                class="w-10 h-10 rounded-md cursor-pointer border-none">
                            <label for="imageBorderThickness" class="text-xs md:text-sm font-medium">Thickness:</label>
                            <input type="number" id="imageBorderThickness" min="0" max="10" value="0"
                                class="w-16 bg-zinc-600 text-white p-2 rounded-md text-sm ">
                            <label for="imageBorderRadius" class="text-sm font-medium">Radius:</label>
                            <input type="number" id="imageBorderRadius" min="0" max="100" value="0"
                                class="w-16 bg-zinc-600 text-white p-2 rounded-md text-sm ">

                            <!-- Flip -->
                            <button id="flipXBtn"
                                class="p-2 rounded-md hover:bg-gray-500 transition-colors text-xs md:text-sm"
                                title="Flip Horizontal">
                                <i class="fas fa-arrows-alt-h"></i> Flip X
                            </button>
                            <button id="flipYBtn"
                                class="p-2 rounded-md hover:bg-gray-500 transition-colors text-xs md:text-sm"
                                title="Flip Vertical">
                                <i class="fas fa-arrows-alt-v"></i> Flip Y
                            </button>
                            <!-- Crop Button -->
                            <button id="cropImageBtn"
                                class="p-2 rounded-md hover:bg-gray-500 transition-colors text-xs md:text-sm"
                                title="Crop Image">
                                <i class="fas fa-crop-alt"></i> Crop
                            </button>
                        </div>
                        <!-- Crop Actions (shown only during cropping) -->
                        <div id="crop-actions" class="flex items-center gap-2 whitespace-nowrap "
                            data-toolbar-group="crop">
                            <button id="applyCropBtn"
                                class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm">
                                <i class="fas fa-check mr-1"></i>Apply
                            </button>
                            <button id="cancelCropBtn"
                                class="px-3 py-1 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors duration-200 text-sm">
                                <i class="fas fa-times mr-1"></i>Cancel
                            </button>
                        </div>

                        <!-- General Actions -->
                        <div id="general-element-actions"
                            class="flex items-center gap-3 whitespace-nowrap  max-w-full relative "
                            data-toolbar-group="general-actions">
                            <svg id="toogleButton" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24"
                                class="text-gray-300 hover:text-white cursor-pointer transition-colors">
                                <g fill="currentColor" fill-rule="evenodd">
                                    <path
                                        d="M3 2h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1z">
                                    </path>
                                    <path
                                        d="M11 2h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1z"
                                        opacity=".45"></path>
                                    <path
                                        d="M19 2h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1z"
                                        opacity=".15"></path>
                                    <path
                                        d="M7 6h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1z"
                                        opacity=".7"></path>
                                    <path
                                        d="M15 6h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1zm0 8h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1z"
                                        opacity=".3"></path>
                                </g>
                            </svg>
                        </div>


                        <!-- Opacity Range -->
                        <!-- Opacity Range -->
                        <div id="inputContainer"
                            class="absolute top-12 bg-zinc-800 py-2 px-4 rounded-lg shadow-xl hidden z-40">
                            <div id="opacity-range" class="hidden z-40">
                                <label for="opacity" class="text-white text-xs md:text-sm font-medium">Opacity:</label>
                                <input type="range" id="opacity" min="0" max="1" value="1" step="0.05"
                                    class="w-24 h-1.5 bg-zinc-600 rounded-lg cursor-pointer accent-blue-500">
                            </div>
                            <div id="textPropertiesDropdown" class=" flex flex-col gap-1 hidden z-40">
                                <div class="mb-1">
                                    <div class="flex justify-between">
                                        <label for="fontWeightSlider"
                                            class="block text-white text-sm font-medium mb-1">Font Weight</label>
                                        <span id="fontWeightValue"
                                            class="text-white text-xs mt-1 block text-right">400</span>

                                    </div>
                                    <input type="range" id="fontWeightSlider" min="100" max="900" step="100" value="400"
                                        class="w-full h-1.5 bg-zinc-600 rounded-lg cursor-pointer accent-blue-500">
                                </div>
                                <div class="mb-1">
                                    <div class="flex justify-between">
                                        <label for="lineHeightSlider"
                                            class="block text-white text-sm font-medium mb-1">Line Height:</label>

                                        <span id="lineHeightValue"
                                            class="text-white text-xs mt-1 block text-right">1.2</span>
                                    </div>

                                    <input type="range" id="lineHeightSlider" min="0.5" max="3.0" step="0.1" value="1.2"
                                        class="w-full h-1.5 bg-zinc-600 rounded-lg cursor-pointer accent-blue-500">

                                </div>
                                <div>
                                    <div class="flex justify-between">
                                        <label for="charSpacingSlider"
                                            class="block text-white text-sm font-medium mb-1">Letter
                                            Spacing:</label>
                                        <span id="charSpacingValue"
                                            class="text-white text-xs mt-1 block text-right">0</span>
                                    </div>
                                    <input type="range" id="charSpacingSlider" min="-100" max="500" step="10" value="0"
                                        class="w-full h-1.5 bg-zinc-600 rounded-lg cursor-pointer accent-blue-500">

                                </div>
                            </div>
                            <div id="textAlignDropdown" class=" flex hidden gap-1 z-40">
                                <button
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors text-white text-sm block w-full text-left"
                                    data-align="left"><i class="fas fa-align-left mr-2"></i>Left</button>
                                <button
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors text-white text-sm block w-full text-left"
                                    data-align="center"><i class="fas fa-align-center mr-2"></i>Center</button>
                                <button
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors text-white text-sm block w-full text-left"
                                    data-align="right"><i class="fas fa-align-right mr-2"></i>Right</button>
                                <button
                                    class="py-1 px-2 rounded-md hover:bg-gray-500 transition-colors text-white text-sm block w-full text-left"
                                    data-align="justify"><i class="fas fa-align-justify mr-2"></i>Justify</button>
                            </div>

                        </div>
                    </div>

                    <!-- Main Canvas Area -->
                    <div id="main-content-area" style=" width: 100%; height: 100%; " class="bg-[#f5f5f5] p-4">
                        <div class="relative flex overflow-hidden w-full mt-10 md:mt-5 h-[80%]">


                            <canvas id="canvas-editor"></canvas>
                            <!-- Floating Object Controls -->
                            <div id="floating-object-controls"
                                class="absolute bg-zinc-700 text-white rounded-md shadow-lg p-1  items-center top-5 left-0 gap-1 hidden">
                                <button id="lockUnlockFloatingBtn" class="p-2 rounded hover:bg-zinc-600"
                                    title="Lock/Unlock Object">
                                    <i class="fas fa-unlock" id="lockUnlockFloatingIcon"></i>
                                </button>
                                <button id="duplicateFloatingBtn" class="p-2 rounded hover:bg-zinc-600"
                                    title="Duplicate Object">
                                    <i class="fas fa-clone"></i>
                                </button>
                                <button id="deleteFloatingBtn" class="p-2 rounded hover:bg-zinc-600 "
                                    title="Delete Object">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                    </div>
                    <!-- Canvas Footer (Zoom, Fullscreen) -->
                    <div id="canvas-footer"
                        class="bg-white py-0  px-4 shadow-md hidden md:flex items-center justify-end  fixed bottom-0 right-0 w-full">
                        <div class="flex items-center gap-4">
                            <label for="zoomSlider" class="text-gray-700 text-sm">Zoom:</label>
                            <input type="range" class="form-range me-2" id="zoomSlider" min="0.5" max="2" step="0.1"
                                value="1">
                            <span id="zoomValue" class="text-gray-700 text-sm">1.0x</span>
                        </div>
                        <button id="fullscreenToggle"
                            class="flex items-center justify-center p-2 rounded-md hover:bg-gray-100 text-gray-700"
                            title="Toggle Fullscreen">
                            <i class="material-icons text-xl">fullscreen</i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Modal for Alerts/Previews -->
    <div id="customModal" class="fixed inset-0 bg-black/50  items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full m-4">
            <div class="flex justify-between items-center mb-4">
                <h3 id="customModalLabel" class="text-xl font-semibold text-gray-800">Modal Title</h3>
                <button id="closeCustomModal"
                    class="text-gray-400 hover:text-gray-600 text-2xl font-bold">&times;</button>
            </div>
            <div id="customModalBody" class="text-gray-700 mb-4 overflow-y-auto max-h-[70vh]">
                <!-- Modal content goes here -->
            </div>
            <div id="customModalFooter" class="flex justify-end gap-2">
                <!-- Modal footer buttons go here -->
            </div>
        </div>
    </div>

    <!-- Custom Variable Modal -->
    <div id="variableModal" class="fixed inset-0 bg-gray-900 bg-opacity-75  items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full m-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-800">Add Custom Variable</h3>
                <button id="closeVariableModal"
                    class="text-gray-400 hover:text-gray-600 text-2xl font-bold">&times;</button>
            </div>
            <div class="text-gray-700 mb-4">
                <div class="mb-4">
                    <label for="variable-name" class="block text-sm font-medium text-gray-700">Variable Name:</label>
                    <input type="text" id="variable-name"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., eventName">
                </div>
                <div class="mb-4">
                    <label for="variable-type" class="block text-sm font-medium text-gray-700">Variable Type:</label>
                    <select id="variable-type"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="variable-default" class="block text-sm font-medium text-gray-700">Default Value
                        (Optional):</label>
                    <input type="text" id="variable-default"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Annual Conference">
                </div>
            </div>
            <div class="flex justify-end gap-2">
                <button id="save-variable-btn"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">Save
                    Variable</button>
            </div>
        </div>
    </div>

    <!-- Custom Toast Notification -->
    <div id="custom-toast"
        class="fixed bottom-10 left-1/2 -translate-x-1/2 bg-gray-800 text-white px-4 py-2 text-xs rounded-md shadow-lg opacity-0 transition-opacity duration-300 z-50">
        <span id="toast-body"></span>
    </div>

    <!-- Global Loading Overlay -->
    <div id="global-loader-overlay"
        class="fixed inset-0 bg-gray-900 bg-opacity-75  items-center justify-center z-[9999] hidden">
        <div class="spinner-border text-white" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>

    <script src="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.contextMenu.min.js"></script>
    <script src="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.ui.position.min.js"></script>


    <!-- Microsoft Clarity Code -->
    <script type="text/javascript">
        (function (c, l, a, r, i, t, y) {
            c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
            t = l.createElement(r); t.async = 1; t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "s4x38v3eal");
    </script>

    <script>
        // Global variables for canvas dimensions and utility functions
        let canvas;
        let originalCanvasWidth;
        let originalCanvasHeight;
        let currentZoomLevel = 1.0;
        let currentCropRect = null;
        let croppedImage = null;
        let originalClipPath = null;

        let croppingRect = null;
        let selectedImage = null;
        let isCropModeActive = false;
        ;
        /**
         * Shows a custom toast notification.
         * @param {string} message - The message to display.
         * @param {number} [duration=3000] - How long the toast should be visible in milliseconds.
         */
        function showToast(message, duration = 3000) {
            const toast = $('#custom-toast');
            $('#toast-body').text(message);
            toast.removeClass('opacity-0 hidden').addClass('opacity-100');
            setTimeout(() => {
                toast.removeClass('opacity-100').addClass('opacity-0 hidden');
            }, duration);
        }

        // This is essential to ensure only one is visible at a time.
        function hideAllInputPanels() {
            $('#opacity-range').addClass('hidden'); // This will now correctly target the div
            $('#textAlignDropdown').addClass('hidden');
            $('#textPropertiesDropdown').addClass('hidden');
        }

        // Toggle visibility for the main opacity button
        $('#toogleButton').on('click', function (event) {
            event.stopPropagation(); // Prevents document click from immediately closing

            // Check if inputContainer is currently visible AND showing the opacity panel
            if (!$('#inputContainer').hasClass('hidden') && !$('#opacity-range').hasClass('hidden')) {
                // If true, it means we clicked the same button to close it
                $('#inputContainer').addClass('hidden');
                hideAllInputPanels(); // Hide all inner panels when closing main container
            } else {
                // Otherwise, show inputContainer and the opacity panel
                $('#inputContainer').removeClass('hidden'); // Show the main dropdown container
                hideAllInputPanels(); // IMPORTANT: Hide any previously visible panel first
                $('#opacity-range').removeClass('hidden'); // Then, show ONLY the opacity panel
            }
        });

        // Toggle visibility for the Text Alignment button
        $('#textAlignToggle').on('click', function (event) {
            event.stopPropagation(); // Prevents document click from immediately closing

            // Check if inputContainer is currently visible AND showing the text alignment panel
            if (!$('#inputContainer').hasClass('hidden') && !$('#textAlignDropdown').hasClass('hidden')) {
                // If true, it means we clicked the same button to close it
                $('#inputContainer').addClass('hidden');
                hideAllInputPanels(); // Hide all inner panels when closing main container
            } else {
                // Otherwise, show inputContainer and the text alignment panel
                $('#inputContainer').removeClass('hidden'); // Show the main dropdown container
                hideAllInputPanels(); // IMPORTANT: Hide any previously visible panel first
                $('#textAlignDropdown').removeClass('hidden'); // Then, show ONLY the text alignment panel
            }
        });

        // Toggle visibility for the Text Properties button
        $('#textPropertiesToggle').on('click', function (event) {
            event.stopPropagation(); // Prevents document click from immediately closing

            // Check if inputContainer is currently visible AND showing the text properties panel
            if (!$('#inputContainer').hasClass('hidden') && !$('#textPropertiesDropdown').hasClass('hidden')) {
                // If true, it means we clicked the same button to close it
                $('#inputContainer').addClass('hidden');
                hideAllInputPanels(); // Hide all inner panels when closing main container
            } else {
                // Otherwise, show inputContainer and the text properties panel
                $('#inputContainer').removeClass('hidden'); // Show the main dropdown container
                hideAllInputPanels(); // IMPORTANT: Hide any previously visible panel first
                $('#textPropertiesDropdown').removeClass('hidden'); // Then, show ONLY the text properties panel
            }
        });

        // Close inputContainer when clicking anywhere else on the document
        $(document).on('click', function (event) {
            // Check if the click target is not within inputContainer, nor any of the toggle buttons
            if (!$(event.target).closest('#inputContainer, #toogleButton, #textAlignToggle, #textPropertiesToggle').length) {
                $('#inputContainer').addClass('hidden'); // Hide the main container
                hideAllInputPanels(); // Ensure all inner panels are hidden when the main container closes
            }
        });

        // Prevent clicks inside the inputContainer itself from propagating to the document and closing it
        $('#inputContainer').on('click', function (event) {
            event.stopPropagation();
        });


        $('#inputContainer').addClass('hidden');
        hideAllInputPanels(); // Ensures all inner children are hidden too

        /**
         * Shows the global loading overlay.
         */
        function showGlobalLoader() {
            $('#global-loader-overlay').removeClass('hidden');
        }

        // drop down for text controls 


        /**
         * Hides the global loading overlay.
         */
        function hideGlobalLoader() {
            $('#global-loader-overlay').addClass('hidden');
        }

        /**
         * Shows a custom modal with dynamic content.
         * @param {string} title - The title of the modal.
         * @param {string} bodyHtml - The HTML content for the modal body.
         * @param {string} [footerHtml=''] - The HTML content for the modal footer.
         */
        function showCustomModal(title, bodyHtml, footerHtml = '') {
            $('#customModalLabel').text(title);
            $('#customModalBody').html(bodyHtml);
            $('#customModalFooter').html(footerHtml);
            $('#customModal').removeClass('hidden').addClass('flex');
        }

        /**
         * Updates the main object toolbar based on the active object's type.
         * Made global for accessibility.
         * @param {fabric.Object} object - The currently active Fabric.js object.
         */
        /**
 * Updates the main object toolbar based on the active object's type.
 * Made global for accessibility.
 * @param {fabric.Object} object - The currently active Fabric.js object.
 */function updateObjectToolbar(object) {
            // Hide all specific toolbars first
            $('#text-formatting').addClass('hidden');
            $('#element-colors').addClass('hidden');
            $('#image-controls').addClass('hidden');
            $('#crop-actions').addClass('hidden'); // Hide crop actions by default
            $('#general-element-actions').addClass('hidden');

            // Always show global controls (background color)
            $('#global-controls').removeClass('hidden');

            if (!object) {
                $('#object-toolbar').addClass('center-global');
                return;
            }

            // Show general actions (opacity, lock/unlock) if an object is selected
            $('#general-element-actions').removeClass('hidden');
            $('#opacity').val(object.opacity);
            $('#object-toolbar').removeClass('center-global');

            // Determine the target object for toolbar controls (handle groups)
            let targetForToolbar = object;
            if (object.type === 'group') {
                const objectsInGroup = object.getObjects();
                const imageInGroup = objectsInGroup.find(obj => obj.type === 'image');
                const textInGroup = objectsInGroup.find(obj => obj.type === 'i-text' || obj.type === 'text');
                const shapeInGroup = objectsInGroup.find(obj => ['rect', 'circle', 'triangle', 'ellipse', 'line', 'polygon'].includes(obj.type));

                if (imageInGroup) {
                    targetForToolbar = imageInGroup;
                } else if (textInGroup) {
                    targetForToolbar = textInGroup;
                } else if (shapeInGroup) {
                    targetForToolbar = shapeInGroup;
                } else {
                    return; // No recognized object type in group
                }
            }

            // Show specific toolbars based on object type
            if (targetForToolbar.type === 'i-text' || targetForToolbar.type === 'text') {
                $('#text-formatting').removeClass('hidden');
                $('#fontFamily').val(targetForToolbar.fontFamily);
                $('#fontSizeInput').val(targetForToolbar.fontSize);
                $('#fontColorPicker').spectrum("set", targetForToolbar.fill);
                $('#boldBtn').toggleClass('active', targetForToolbar.fontWeight === 'bold');
                $('#italicBtn').toggleClass('active', targetForToolbar.fontStyle === 'italic');
                $('#underlineBtn').toggleClass('active', targetForToolbar.underline);
                $('#strikethroughBtn').toggleClass('active', targetForToolbar.linethrough);
                $('#textAlignLeft').toggleClass('active', targetForToolbar.textAlign === 'left');
                $('#textAlignCenter').toggleClass('active', targetForToolbar.textAlign === 'center');
                $('#textAlignRight').toggleClass('active', targetForToolbar.textAlign === 'right');
                $('#textAlignJustify').toggleClass('active', targetForToolbar.textAlign === 'justify');

            } else if (['rect', 'circle', 'triangle', 'ellipse', 'line', 'polygon'].includes(targetForToolbar.type)) {
                $('#element-colors').removeClass('hidden');
                $('#elementFillPicker').spectrum("set", targetForToolbar.fill || '#cccccc');
                $('#borderColorPicker').spectrum("set", targetForToolbar.stroke || '#000000');
                $('#borderThickness').val(targetForToolbar.strokeWidth || 0);
                if (targetForToolbar.type === 'rect' || targetForToolbar.type === 'ellipse') {
                    $('#elementBorderRadius').val(targetForToolbar.rx || 0);
                } else {
                    $('#elementBorderRadius').val(0);
                }
                $('#borderType').val(targetForToolbar.strokeDashArray && targetForToolbar.strokeDashArray.length > 0 ? 'dashed' : 'solid');
            } else if (targetForToolbar.type === 'image') {
                $('#image-controls').removeClass('hidden');
                $('#imageBorderColor').spectrum("set", targetForToolbar.stroke || '#000000');
                $('#imageBorderThickness').val(targetForToolbar.strokeWidth || 0);
                const currentRadius = (targetForToolbar.clipPath && targetForToolbar.clipPath.type === 'rect') ? targetForToolbar.clipPath.rx : 0;
                $('#imageBorderRadius').val(currentRadius);
                $('#flipXBtn').toggleClass('active', targetForToolbar.flipX);
                $('#flipYBtn').toggleClass('active', targetForToolbar.flipY);

                // Show crop actions if an image is selected AND we are in cropping mode
                if (currentCropRect && croppedImage === targetForToolbar) {
                    $('#crop-actions').removeClass('hidden');
                    $('#image-controls').addClass('hidden');
                }
            }
            if (!targetForToolbar.isIcon && currentCropRect && croppedImage === targetForToolbar) {
                $('#crop-actions').removeClass('hidden');
                $('#image-controls').addClass('hidden');
            } else if (targetForToolbar.isIcon) {
                // If it's an icon, hide the crop button specifically
                $('#cropImageBtn').addClass('hidden');
            } else {
                $('#cropImageBtn').removeClass('hidden'); // Ensure it's visible for regular images
            }
        }




        // Font Weight Slider
        $('#fontWeightSlider').on('input', function () {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                const fontWeight = $(this).val();
                activeObject.set('fontWeight', fontWeight);
                $('#fontWeightValue').text(fontWeight);
                canvas.renderAll();
            }
        });

        // Line Height Slider
        $('#lineHeightSlider').on('input', function () {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                const lineHeight = parseFloat($(this).val());
                activeObject.set('lineHeight', lineHeight);
                $('#lineHeightValue').text(lineHeight.toFixed(1));
                canvas.renderAll();
            }
        });

        // Letter Spacing Slider
        $('#charSpacingSlider').on('input', function () {
            const activeObject = canvas.getActiveObject();
            if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                const charSpacing = parseInt($(this).val());
                activeObject.set('charSpacing', charSpacing);
                $('#charSpacingValue').text(charSpacing);
                canvas.renderAll();
            }
        });

        // Close dropdowns when clicking anywhere else on the document
        $(document).on('click', function (event) {
            // Check if the click was outside the alignment toggle/dropdown
            if (!$(event.target).closest('#textAlignToggle').length && !$(event.target).closest('#textAlignDropdown').length) {
                $('#textAlignDropdown').addClass('hidden');
            }
            // Check if the click was outside the properties toggle/dropdown
            if (!$(event.target).closest('#textPropertiesToggle').length && !$(event.target).closest('#textPropertiesDropdown').length) {
                $('#textPropertiesDropdown').addClass('hidden');
            }
        });

        function updateFloatingControls(object) {
            const floatingControls = $('#floating-object-controls');
            const canvasContainer = $('#canvas-editor'); // Assuming your canvas element's parent container
            const mainContentArea = $('#main-content-area');

            // 1. **PRIMARY FIX: Hide if crop mode is active**
            // This is the most important part for your cropping issue.
            if (isCropModeActive) { // Use the flag directly
                floatingControls.removeClass('show').addClass('hidden'); // Ensure hidden class is applied
                return; // Exit the function immediately
            }

            // 2. **Refined Logic for displaying controls when NOT in crop mode**
            if (object) {
                // Get object's actual bounding rectangle relative to the viewport
                // This accounts for its position, scale, and rotation directly.
                const objectOffset = object.getBoundingRect(true); // true for includeTransform, which includes scale/rotation
                const zoom = canvas.getZoom();

                // Get offsets of parent containers for accurate positioning relative to the viewport
                const canvasContainerOffset = canvasContainer.offset();
                const mainContentAreaOffset = mainContentArea.offset();

                // Calculate toolbar dimensions
                const toolbarHeight = floatingControls.outerHeight();
                const toolbarWidth = floatingControls.outerWidth();

                // Calculate desired position: typically centered above the object
                let controlsLeft = canvasContainerOffset.left - mainContentAreaOffset.left + (objectOffset.left * zoom) + (objectOffset.width * zoom / 2) - (toolbarWidth / 2);
                let controlsTop = canvasContainerOffset.top - mainContentAreaOffset.top + (objectOffset.top * zoom) - toolbarHeight - 50;

                // **3. Clamping logic: Prevent going off-canvas (or out of visible area)**
                // Get the visible canvas area dimensions (relative to main-content-area)
                const canvasEditorWidth = canvasContainer.width();
                const canvasEditorHeight = canvasContainer.height();
                const canvasEditorLeft = canvasContainerOffset.left - mainContentAreaOffset.left;
                const canvasEditorTop = canvasContainerOffset.top - mainContentAreaOffset.top;

                // Clamp Left position
                controlsLeft = Math.max(controlsLeft, canvasEditorLeft); // Don't go left of canvas
                controlsLeft = Math.min(controlsLeft, canvasEditorLeft + canvasEditorWidth - toolbarWidth); // Don't go right of canvas

                // Clamp Top position
                // If toolbar would go above canvas, place it at the top of the object
                if (controlsTop < canvasEditorTop) {
                    controlsTop = canvasContainerOffset.top - mainContentAreaOffset.top + (objectOffset.top * zoom); // Place at object's top edge
                }


                // Apply calculated positions
                floatingControls.css({
                    left: controlsLeft + 'px',
                    top: controlsTop + 'px',
                }).removeClass('hidden').addClass('show'); // Add 'show' class for opacity transition

                // Update lock/unlock icon as before
                const lockUnlockIcon = $('#lockUnlockFloatingIcon');
                if (object.lockMovementX || object.lockMovementY || object.lockScalingX || object.lockScalingY || object.lockRotation) {
                    lockUnlockIcon.removeClass('fa-unlock').addClass('fa-lock');
                } else {
                    lockUnlockIcon.removeClass('fa-lock').addClass('fa-unlock');
                }
            } else {
                // If no object is selected, hide the controls
                floatingControls.removeClass('show').addClass('hidden');
            }
        }


        $(document).ready(function () {
            /**
             * Extracts query parameters from the URL.
             * @returns {object} An object containing width, height, and name from the URL.
             */
            function getQueryParams() {
                const params = new URLSearchParams(window.location.search);
                const width = parseInt(params.get('width'));
                const height = parseInt(params.get('height'));
                const name = params.get('name');
                return { width, height, name };
            }

            // Get canvas dimensions and design name from the URL
            const urlParams = getQueryParams();

            // Determine initial canvas dimensions based on screen width or URL parameters
            let initialCanvasWidth = urlParams.width || (window.innerWidth <= 500 ? 305 : 800);
            let initialCanvasHeight = urlParams.height || (window.innerWidth <= 500 ? 400 : 600);

            // Initialize Fabric.js canvas
            canvas = new fabric.Canvas('canvas-editor', {
                width: initialCanvasWidth,
                height: initialCanvasHeight,
                backgroundColor: '#ffffff',
                selection: true,
                renderOnAddRemove: true
            });

            // Store the original dimensions of the design. These will be updated if a design is loaded.
            originalCanvasWidth = initialCanvasWidth;
            originalCanvasHeight = initialCanvasHeight;

            // Canvas history for Undo/Redo
            let canvasStates = [];
            let currentStateIndex = -1;

            /**
             * Saves the current state of the canvas to the history.
             */
            function saveCanvasState() {
                if (currentStateIndex < canvasStates.length - 1) {
                    canvasStates = canvasStates.slice(0, currentStateIndex + 1);
                }
                canvasStates.push(JSON.stringify(canvas.toJSON()));
                currentStateIndex = canvasStates.length - 1;
            }

            /**
             * Undoes the last canvas action.
             */
            function undo() {
                if (currentStateIndex > 0) {
                    currentStateIndex--;
                    canvas.loadFromJSON(canvasStates[currentStateIndex], function () {
                        canvas.renderAll();
                        showToast('Undo successful!');
                    });
                } else {
                    showToast('Nothing to undo!', 2000);
                }
            }

            /**
             * Redoes the last undone canvas action.
             */
            function redo() {
                if (currentStateIndex < canvasStates.length - 1) {
                    currentStateIndex++;
                    canvas.loadFromJSON(canvasStates[currentStateIndex], function () {
                        canvas.renderAll();
                        showToast('Redo successful!');
                    });
                } else {
                    showToast('Nothing to redo!', 2000);
                }
            }

            // Event listeners for undo/redo buttons
            $('#undo-button').on('click', undo);
            $('#redo-button').on('click', redo);

            // Save state after any canvas modification
            canvas.on('object:added', saveCanvasState);
            canvas.on('object:modified', saveCanvasState);
            canvas.on('object:removed', saveCanvasState);

            // Initialize Spectrum color pickers
            $("#bgColorPicker, #fontColorPicker, #elementFillPicker, #borderColorPicker, #imageBorderColor").spectrum({
                showInput: true,
                preferredFormat: "hex",
                showPalette: true,
                showButtons: false,
                palette: [
                    ["#000", "#444", "#666", "#999", "#ccc", "#eee", "#f3f3f3", "#fff"],
                    ["#f00", "#f90", "#ff0", "#0f0", "#0ff", "#00f", "#90f", "#f0f"],
                    ["#ea9999", "#f9cb9c", "#ffe599", "#b6d7a8", "#a2c4c9", "#9fc5e8", "#b4a7d6", "#d5a6bd"],
                    ["#e06666", "#f6b26b", "#ffd966", "#93c47d", "#76a5af", "#6fa8dc", "#8e7cc3", "#c27ba0"],
                    ["#cc0000", "#e69138", "#f1c232", "#6aa84f", "#45818e", "#3d85c6", "#674ea7", "#a64d79"],
                    ["#990000", "#b45f06", "#bf9000", "#38761d", "#134f5c", "#0b5394", "#351c75", "#741b47"],
                    ["#660000", "#783f04", "#7f6000", "#274e13", "#0c343d", "#073763", "#20124d", "#4c1130"]
                ]
            });

            // Handle close custom modal button
            $('#closeCustomModal').on('click', function () {
                $('#customModal').removeClass('flex').addClass('hidden');
            });

            // Handle sidebar item clicks to show/hide content
            $('.editor-sidebar-items').on('click', function () {
                const targetId = $(this).data('target');
                $('.sidebar-content').addClass('hidden');
                $('.editor-sidebar-items').removeClass('active');
                $(targetId).removeClass('hidden');
                $(this).addClass('active');
                $('#sidebar-content-area').removeClass('hidden').addClass('sidebar-open');

                if ($(window).width() < 768) {
                    $('#sidebar-content-area').css('transform', 'translateY(0%)');
                } else {

                    $('#sidebar-content-area').css('transform', 'translateX(0%)');
                }
            });



            // Handle close sidebar content button
            $('#close-sidebar-content').on('click', function () {
                if ($(window).width() < 768) {
                    $('#sidebar-content-area').css('transform', 'translateY(100%)');
                } else {
                    $('#sidebar-content-area').css('transform', 'translateX(-100%)');
                }
                setTimeout(() => {
                    $('#sidebar-content-area').addClass('hidden').removeClass('sidebar-open');
                    $('.editor-sidebar-items').removeClass('active');
                }, 300);
            });

            /**
             * Toggles the lock state of the active object.
             * @param {fabric.Object} activeObject - The active Fabric.js object.
             */
            function toggleObjectLock(activeObject) {
                const isLocked = activeObject.lockMovementX;
                activeObject.set({
                    lockMovementX: !isLocked,
                    lockMovementY: !isLocked,
                    lockRotation: !isLocked,
                    lockScalingX: !isLocked,
                    lockScalingY: !isLocked,
                    hasControls: isLocked,
                    hasBorders: isLocked,
                });
                canvas.renderAll();
                updateFloatingControls(activeObject);
                showToast(`Object ${isLocked ? 'unlocked!' : 'locked!'}`);
            }

            /**
             * Duplicates the active object.
             * @param {fabric.Object} activeObject - The active Fabric.js object.
             */
            function duplicateObject(activeObject) {
                activeObject.clone(function (clonedObj) {
                    clonedObj.set({
                        left: activeObject.left + 10,
                        top: activeObject.top + 10,
                        evented: true,
                    });
                    if (clonedObj.type === 'activeSelection') {
                        clonedObj.canvas = canvas;
                        clonedObj.forEachObject(function (obj) {
                            canvas.add(obj);
                        });
                    } else {
                        canvas.add(clonedObj);
                    }
                    canvas.setActiveObject(clonedObj);
                    canvas.renderAll();
                    showToast('Object duplicated!');
                });
            }

            /**
             * Deletes the active object.
             * @param {fabric.Object} activeObject - The active Fabric.js object.
             */
            function deleteObject(activeObject) {
                canvas.remove(activeObject);
                canvas.discardActiveObject();
                canvas.renderAll();
                showToast('Object deleted!');
            }

            // Event listeners for object selection and modification.
            canvas.on({
                'selection:created': function (e) {
                    const activeObj = e.target || canvas.getActiveObject();
                    if (activeObj) {
                        updateObjectToolbar(activeObj);
                        updateFloatingControls(activeObj);
                    } else {
                        updateObjectToolbar(null);
                        updateFloatingControls(null);
                    }
                },
                'selection:updated': function (e) {
                    const activeObj = e.target || canvas.getActiveObject();
                    if (activeObj) {
                        updateObjectToolbar(activeObj);
                        updateFloatingControls(activeObj);
                    } else {
                        updateObjectToolbar(null);
                        updateFloatingControls(null);
                    }
                },
                'selection:cleared': function (e) {
                    updateObjectToolbar(null);
                    updateFloatingControls(null);
                    // If cropping was active and selection cleared, cancel it
                    if (currentCropRect) {
                        cancelCrop();
                    }
                },
                'object:moving': function (e) {
                    if (e.target) {
                        updateFloatingControls(e.target);
                        // If the moving object is the crop rect, update the image's clipPath
                        if (e.target === currentCropRect && croppedImage) {
                            updateClipPathFromCropRect();
                        }
                    }
                },
                'object:scaling': function (e) {
                    if (e.target) {
                        updateFloatingControls(e.target);
                        const activeObject = e.target;

                        const updateImageClipPath = (img) => {
                            if (img.clipPath && img.clipPath.type === 'rect') {
                                // Ensure clipPath scales and moves with the image
                                img.clipPath.set({
                                    width: img.width,
                                    height: img.height,
                                    left: 0,
                                    top: 0,
                                    originX: 'center',
                                    originY: 'center'
                                });
                                img.clipPath.set('dirty', true);
                                img.set('dirty', true);
                            }
                        };

                        if (activeObject.type === 'image') {
                            updateImageClipPath(activeObject);
                        } else if (activeObject.type === 'group') {
                            activeObject.getObjects().forEach(obj => {
                                if (obj.type === 'image') {
                                    updateImageClipPath(obj);
                                }
                            });
                            activeObject.set('dirty', true);
                        }

                        // If the scaling object is the crop rect, update the image's clipPath
                        if (e.target === currentCropRect && croppedImage) {
                            updateClipPathFromCropRect();
                        }
                        canvas.renderAll();
                    }
                },
                'object:rotating': function (e) {
                    if (e.target) {
                        updateFloatingControls(e.target);
                    }
                }

            });
            canvas.on('object:scaling', function (e) {

                const target = e.target;

                if (target.type === 'i-text' || target.type === 'text') {

                    const transform = canvas._currentTransform;

                    if (transform && (transform.corner === 'ml' || transform.corner === 'mr')) {

                        const originalWidth = target.width * (target.scaleX || 1);

                        target.set('scaleX', 1);



                        const newWidth = originalWidth + (e.pointer.x - transform.lastX) * 2;



                        const currentTextWidthWithoutSpacing = target.getScaledWidth() - (target.text.length - 1) * (target.charSpacing || 0);

                        const newCharSpacing = (newWidth - currentTextWidthWithoutSpacing) / (target.text.length - 1);



                        target.set('charSpacing', Math.max(0, newCharSpacing));



                        canvas.renderAll();

                        target.setCoords();

                    }

                }

            });

            // Initial toolbar state.
            updateObjectToolbar(null);
            updateFloatingControls(null);

            // Adds a shape to the canvas based on button data.
            $('[id^="addRectangle"], [id^="addCircle"], [id^="addTriangle"], [id^="addEllipse"], [id^="addLine"], [id^="addPolygon"]').on('click', function () {
                const type = $(this).data('type');
                const fill = $(this).data('fill') || '#cccccc';
                const stroke = $(this).data('stroke');
                const strokeWidth = $(this).data('stroke-width');
                let shape;

                const commonOptions = {
                    left: 100,
                    top: 100,
                    opacity: 0.8,
                    cornerColor: '#1d89ff',
                    transparentCorners: false,
                    hasControls: true,
                    hasBorders: true,
                };

                switch (type) {
                    case 'rect':
                        shape = new fabric.Rect($.extend(true, {}, commonOptions, { width: 100, height: 100, fill: fill, stroke: stroke, strokeWidth: strokeWidth, rx: 5, ry: 5 }));
                        break;
                    case 'circle':
                        shape = new fabric.Circle($.extend(true, {}, commonOptions, { radius: 50, fill: fill, stroke: stroke, strokeWidth: strokeWidth }));
                        break;
                    case 'triangle':
                        shape = new fabric.Triangle($.extend(true, {}, commonOptions, { width: 100, height: 100, fill: fill, stroke: stroke, strokeWidth: strokeWidth }));
                        break;
                    case 'ellipse':
                        shape = new fabric.Ellipse($.extend(true, {}, commonOptions, { rx: 60, ry: 40, fill: fill, stroke: stroke, strokeWidth: strokeWidth }));
                        break;
                    case 'line':
                        shape = new fabric.Line([50, 50, 150, 50], $.extend(true, {}, commonOptions, { stroke: stroke, strokeWidth: strokeWidth, fill: 'transparent' }));
                        break;
                    case 'polygon':
                        shape = new fabric.Polygon([{ x: 0, y: 0 }, { x: 100, y: 0 }, { x: 100, y: 100 }, { x: 0, y: 100 }], $.extend(true, {}, commonOptions, { fill: fill, stroke: stroke, strokeWidth: strokeWidth }));
                        break;
                }

                if (shape) {
                    canvas.add(shape);
                    canvas.setActiveObject(shape);
                    canvas.renderAll();
                    showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} added!`);
                }
            });

            // Adds text to the canvas with predefined styles.
            $('[id^="addH"], #addpara').on('click', function () {
                const defaultText = $(this).data('text');
                const headerType = $(this).data('header');
                let fontSize = 24;

                switch (headerType) {
                    case 'h1': fontSize = 30; break;
                    case 'h2': fontSize = 25; break;
                    case 'h3': fontSize = 22; break;
                    case 'h4': fontSize = 20; break;
                    case 'h5': fontSize = 18; break;
                    case 'p': fontSize = 16; break;
                }

                const textObject = new fabric.IText(defaultText, {
                    left: canvas.width / 2,
                    top: 100,
                    fontFamily: 'Roboto',
                    fontSize: fontSize,
                    fontWeight: 'normal',
                    cornerColor: '#1d89ff',
                    splitByGrapheme: true,
                    originX: 'center',
                    
                });

                canvas.add(textObject);
                canvas.setActiveObject(textObject);
                canvas.renderAll();
                showToast(`Text added: "${defaultText}"`);
            });

            // Text formatting toolbar actions.
            $('#fontFamily').on('change', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('fontFamily', $(this).val());
                    canvas.renderAll();
                }
            });

            $('#fontSizeInput').on('change', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('fontSize', parseInt($(this).val()));
                    canvas.renderAll();
                }
            });

            $('#fontColorPicker').on('move.spectrum', function (e, tinycolor) {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('fill', tinycolor.toHexString());
                    canvas.renderAll();
                }
            });

            $('#boldBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    const isBold = activeObject.fontWeight === 'bold';
                    activeObject.set('fontWeight', isBold ? 'normal' : 'bold');
                    $(this).toggleClass('active', !isBold);
                    canvas.renderAll();
                }
            });

            $('#italicBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    const isItalic = activeObject.fontStyle === 'italic';
                    activeObject.set('fontStyle', isItalic ? 'normal' : 'italic');
                    $(this).toggleClass('active', !isItalic);
                    canvas.renderAll();
                }
            });

            $('#underlineBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('underline', !activeObject.underline);
                    $(this).toggleClass('active', activeObject.underline);
                    canvas.renderAll();
                }
            });

            $('#strikethroughBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('linethrough', !activeObject.linethrough);
                    $(this).toggleClass('active', activeObject.linethrough);
                    canvas.renderAll();
                }
            });

            // Text alignment handlers.
            $('#textAlignLeft').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('textAlign', 'left');
                    $(this).addClass('active').siblings().removeClass('active');
                    canvas.renderAll();
                }
            });

            $('#textAlignCenter').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('textAlign', 'center');
                    $(this).addClass('active').siblings().removeClass('active');
                    canvas.renderAll();
                }
            });

            $('#textAlignRight').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('textAlign', 'right');
                    $(this).addClass('active').siblings().removeClass('active');
                    canvas.renderAll();
                }
            });

            $('#textAlignJustify').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && (activeObject.type === 'i-text' || activeObject.type === 'text')) {
                    activeObject.set('textAlign', 'justify');
                    $(this).addClass('active').siblings().removeClass('active');
                    canvas.renderAll();
                }
            });

            // Element color and border toolbar actions.
            $('#elementFillPicker').on('move.spectrum', function (e, tinycolor) {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('fill', tinycolor.toHexString());
                    canvas.renderAll();
                }
            });

            $('#borderColorPicker').on('move.spectrum', function (e, tinycolor) {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('stroke', tinycolor.toHexString());
                    canvas.renderAll();
                }
            });

            $('#borderThickness').on('change', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('strokeWidth', parseInt($(this).val()));
                    canvas.renderAll();
                }
            });

            $('#borderType').on('change', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    const type = $(this).val();
                    if (type === 'dashed') {
                        activeObject.set('strokeDashArray', [5, 5]);
                    } else if (type === 'dotted') {
                        activeObject.set('strokeDashArray', [1, 3]);
                    } else {
                        activeObject.set('strokeDashArray', []);
                    }
                    canvas.renderAll();
                }
            });

            $('#elementBorderRadius').on('input', function () {
                const activeObject = canvas.getActiveObject();
                const radius = parseInt($(this).val());

                if (activeObject) {
                    if (activeObject.type === 'rect') {
                        activeObject.set({ rx: radius, ry: radius });
                        canvas.renderAll();
                        showToast(`Rectangle radius set to ${radius}!`);
                    } else if (activeObject.type === 'ellipse') {
                        activeObject.set({ rx: radius, ry: radius });
                        canvas.renderAll();
                        showToast(`Ellipse radii set to ${radius} (making it more circular)!`);
                    } else {
                        showToast('Border radius only applies to rectangles and ellipses.', 3000);
                    }
                } else {
                    showToast('No object selected to apply radius.', 3000);
                }
            });

            // Image controls actions.
            $('#imageOpacity').on('input', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject && activeObject.type === 'image') {
                    activeObject.set('opacity', parseFloat($(this).val()));
                    canvas.renderAll();
                }
            });

            $('#imageBorderColor').on('move.spectrum', function (e, tinycolor) {
                let activeObject = canvas.getActiveObject();
                if (!activeObject) return;
                let targetImage = activeObject.type === 'group' ? activeObject.getObjects().find(obj => obj.type === 'image') : activeObject;
                if (targetImage && targetImage.type === 'image') {
                    targetImage.set('stroke', tinycolor.toHexString());
                    canvas.renderAll();
                }
            });

            $('#imageBorderThickness').on('change', function () {
                let activeObject = canvas.getActiveObject();
                if (!activeObject) return;
                let targetImage = activeObject.type === 'group' ? activeObject.getObjects().find(obj => obj.type === 'image') : activeObject;
                if (targetImage && targetImage.type === 'image') {
                    targetImage.set('strokeWidth', parseInt($(this).val()));
                    canvas.renderAll();
                }
            });

            $('#imageBorderRadius').on('input', function () {
                let activeObject = canvas.getActiveObject();
                if (!activeObject) return;

                let targetImage = activeObject.type === 'group' ? activeObject.getObjects().find(obj => obj.type === 'image') : activeObject;
                if (!targetImage || targetImage.type !== 'image') return;

                const radius = parseInt($(this).val()) || 0;

                if (radius < 0 || radius > 100) return;

                if (radius > 0) {
                    if (!targetImage.clipPath || targetImage.clipPath.type !== 'rect') {
                        targetImage.clipPath = new fabric.Rect({
                            width: targetImage.width,
                            height: targetImage.height,
                            originX: 'center',
                            originY: 'center',
                            left: 0,
                            top: 0,
                            fill: 'transparent',
                            selectable: false,
                            evented: false
                        });
                    }
                    targetImage.clipPath.set({ rx: radius, ry: radius });
                    targetImage.clipPath.set('dirty', true);
                } else {
                    targetImage.clipPath = null;
                }

                targetImage.set('dirty', true);
                canvas.renderAll();
            });

            $('#flipXBtn').on('click', function () {
                let activeObject = canvas.getActiveObject();
                let targetImage = activeObject.type === 'group' ? activeObject.getObjects().find(obj => obj.type === 'image') : activeObject;
                if (targetImage && targetImage.type === 'image') {
                    targetImage.set('flipX', !targetImage.flipX);
                    $(this).toggleClass('active', targetImage.flipX);
                    canvas.renderAll();
                    showToast(`Image flipped ${targetImage.flipX ? 'horizontally' : 'back'}!`);
                }
            });

            $('#flipYBtn').on('click', function () {
                let activeObject = canvas.getActiveObject();
                let targetImage = activeObject.type === 'group' ? activeObject.getObjects().find(obj => obj.type === 'image') : activeObject;
                if (targetImage && targetImage.type === 'image') {
                    targetImage.set('flipY', !targetImage.flipY);
                    $(this).toggleClass('active', targetImage.flipY);
                    canvas.renderAll();
                    showToast(`Image flipped ${targetImage.flipY ? 'vertically' : 'back'}!`);
                }
            });

            // Placeholder for updateClipPathFromCropRect - used for non-destructive preview
            function updateClipPathFromCropRect() {
                if (selectedImage && croppingRect) {
                    // Create a rectangle for the clipPath relative to the image's own coordinate system
                    const clipRect = new fabric.Rect({
                        left: (croppingRect.left - selectedImage.left) / selectedImage.scaleX,
                        top: (croppingRect.top - selectedImage.top) / selectedImage.scaleY,
                        width: croppingRect.width / selectedImage.scaleX,
                        height: croppingRect.height / selectedImage.scaleY,
                        originX: 'left',
                        originY: 'top',
                        absolutePositioned: true // Important for clipPath to be relative to canvas
                    });

                    selectedImage.set({
                        clipPath: clipRect,
                        dirty: true // Mark as dirty to force redraw
                    });
                    canvas.renderAll();
                }
            }

            function enableCropMode() {
                const activeObject = canvas.getActiveObject();

                if (activeObject && activeObject.type === 'image') {
                    selectedImage = activeObject; // The original image to be cropped
                    isCropModeActive = true; // Activate crop mode

                    // Hide regular image controls and floating object toolbar
                    $('#image-controls').addClass('hidden');
                    $('#floating-object-controls').removeClass('show').addClass('hidden'); // Ensure hidden

                    // Create a cropping rectangle overlay on top of the selected image
                    croppingRect = new fabric.Rect({
                        left: selectedImage.left,
                        top: selectedImage.top,
                        width: selectedImage.width * selectedImage.scaleX,
                        height: selectedImage.height * selectedImage.scaleY,
                        fill: 'rgba(0,0,0,0.1)', // semi-transparent overlay
                        stroke: '#1d89ff', // Blue stroke for clarity
                        strokeWidth: 2,
                        strokeDashArray: [5, 5],
                        originX: 'left',
                        originY: 'top',
                        selectable: true,
                        hasBorders: true,
                        hasControls: true,
                        lockRotation: true, // Cropping rect should not rotate
                        cornerColor: '#1d89ff',
                        transparentCorners: false,
                        padding: 0,
                        // Ensure the cropping rect stays within the bounds of the original image
                        // This is a basic clamp, more advanced might use object:moving/scaling events
                        minWidth: 10,
                        minHeight: 10
                    });

                    canvas.add(croppingRect);
                    canvas.setActiveObject(croppingRect);
                    canvas.renderAll();

                    // Show crop actions
                    $('#crop-actions').removeClass('hidden');
                    showToast("Drag and resize the box to crop. Click 'Apply' when done.");

                    // Set the selectedImage as the one to be clipped for live preview
                    croppedImage = selectedImage;
                    updateClipPathFromCropRect(); // Apply initial clipPath for preview
                    selectedImage.set({ selectable: false, evented: false }); // Make original image non-interactive
                } else {
                    showToast("Please select an image to crop.");
                }
            }

            /**
             * Applies the crop to the image using a temporary canvas.
             */
            function applyCrop() {
                if (croppingRect && selectedImage) {
                    // Remove the clipPath from the original image before rendering to temporary canvas
                    selectedImage.set({ clipPath: null, dirty: true, selectable: true, evented: true });
                    canvas.remove(croppingRect); // Remove the cropping rectangle

                    // Calculate crop area relative to the original image's unscaled, unrotated dimensions
                    const imageOriginalWidth = selectedImage.getOriginalSize().width;
                    const imageOriginalHeight = selectedImage.getOriginalSize().height;

                    // Get the coordinates of the cropping rectangle relative to the canvas
                    const cropRectCoords = croppingRect.getBoundingRect();


                    const objectsToRemove = canvas.getObjects().filter(obj => obj !== selectedImage);
                    objectsToRemove.forEach(obj => canvas.remove(obj));
                    canvas.renderAll(); // Re-render without other objects

                    const croppedDataUrl = canvas.toDataURL({
                        left: cropRectCoords.left,
                        top: cropRectCoords.top,
                        width: cropRectCoords.width,
                        height: cropRectCoords.height,
                        format: 'png', // or 'jpeg'
                        quality: 1 // Max quality
                    });

                    // Restore other objects to canvas
                    objectsToRemove.forEach(obj => canvas.add(obj));

                    fabric.Image.fromURL(croppedDataUrl, function (newCroppedImg) {
                        newCroppedImg.set({
                            left: cropRectCoords.left, // Position the new image where the crop rect was
                            top: cropRectCoords.top,
                            scaleX: 1,
                            scaleY: 1,
                            originX: 'left', // New image should be positioned from its top-left
                            originY: 'top',
                            cornerColor: '#1d89ff',
                            transparentCorners: false,
                            hasControls: true,
                            hasBorders: true,
                        });

                        canvas.remove(selectedImage); // Remove the original image
                        canvas.add(newCroppedImg);
                        canvas.setActiveObject(newCroppedImg);
                        canvas.renderAll();
                        saveCanvasState();
                        showToast('Image cropped successfully!');

                        // Reset cropping state variables
                        croppingRect = null;
                        selectedImage = null;
                        croppedImage = null;
                        isCropModeActive = false; // Deactivate crop mode

                        $('#crop-actions').addClass('hidden');
                        updateObjectToolbar(canvas.getActiveObject()); // Show toolbars for the new image
                        updateFloatingControls(canvas.getActiveObject());
                    }, {
                        crossOrigin: 'anonymous',
                        onError: function (img, err) {
                            console.error('Fabric.js Image loading error for cropped image:', err);
                            showToast('Failed to load cropped image. This might be due to a broken link or CORS issues.', 5000);
                            // Revert state if error
                            cancelCrop();
                        }
                    });
                }
            }

            function cancelCrop() {
                if (croppingRect) {
                    canvas.remove(croppingRect);
                    croppingRect = null;
                }
                // Remove clipPath from the original image and make it interactive again
                if (selectedImage) {
                    selectedImage.set({ clipPath: null, dirty: true, selectable: true, evented: true });
                    canvas.setActiveObject(selectedImage); // Re-select the original image
                } else {
                    canvas.discardActiveObject();
                }
                canvas.renderAll();

                // Reset cropping state variables
                selectedImage = null;
                croppedImage = null;
                isCropModeActive = false; // Deactivate crop mode

                $('#crop-actions').addClass('hidden');
                updateObjectToolbar(canvas.getActiveObject()); // Show toolbars if an object is active
                updateFloatingControls(canvas.getActiveObject()); // Ensure floating controls reappear if object is selected
                showToast('Cropping cancelled.');
            }


            $(document).ready(function () {
                // Event listeners for crop buttons
                $('#cropImageBtn').on('click', enableCropMode);
                $('#applyCropBtn').on('click', applyCrop);
                $('#cancelCropBtn').on('click', cancelCrop);
            })

            // Handles opacity changes for the active object.
            $('#opacity').on('input', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    activeObject.set('opacity', parseFloat($(this).val()));
                    canvas.renderAll();
                }
            });

            // Floating controls event handlers.
            $('#lockUnlockFloatingBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    toggleObjectLock(activeObject);
                }
            });

            $('#duplicateFloatingBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    duplicateObject(activeObject);
                }
            });

            $('#deleteFloatingBtn').on('click', function () {
                const activeObject = canvas.getActiveObject();
                if (activeObject) {
                    deleteObject(activeObject);
                } else {
                    showToast('No object selected to delete.', 2000);
                }
            });

            // Global canvas controls.
            $('#bgColorPicker').on('move.spectrum', function (e, tinycolor) {
                canvas.setBackgroundColor(tinycolor.toHexString(), canvas.renderAll.bind(canvas));
            });

            // Image upload functionality.
            $('#browse-files-button').on('click', function () {
                $('#file-upload-input').click();
            });

            $('#file-upload-input').on('change', function (e) {
                const files = e.target.files;
                if (files.length > 0) {
                    handleFileUpload(files);
                }
            });

            /**
             * Handles file upload to the server.
             */
            function handleFileUpload(files) {
                const $imageContainer = $('#uploadedImages');
                $('#image-loader').removeClass('hidden');

                Array.from(files).forEach(file => {
                    const formData = new FormData();
                    formData.append('file', file);

                    $.ajax({
                        url: '/api/files/upload/',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            if (response.file && response.file.url) {
                                showToast('Image uploaded successfully!');
                                appendImageToContainer($imageContainer, response.file);
                            } else {
                                showToast('File upload failed. No URL returned.', 3000);
                            }
                        },
                        error: function (xhr, status, error) {
                            showToast(`Image upload failed: ${error}`, 3000);
                        },
                        complete: function () {
                            $('#image-loader').addClass('hidden');
                        }
                    });
                });
            }

            /**
             * Appends an image to the uploaded images container.
             */
            function appendImageToContainer($container, file) {

                const $imgWrapper = $('<div>', { class: 'imageWrapper cursor-pointer', style: 'position: relative; display: inline-block; margin: 5px;' });
                const $imgElement = $('<img>', {
                    class: "my-single-image",
                    src: file.url,
                    style: 'max-width: 150px;'
                });

                $imgElement.on('dragstart', function (e) {
                    e.originalEvent.dataTransfer.setData('text/plain', $(this).attr('src'));
                    e.originalEvent.dataTransfer.setData('text/x-type', 'uploaded-image');
                    e.originalEvent.dataTransfer.effectAllowed = 'copy';
                });
                const $downloadBtn = $('<button>', {
                    class: 'btn-icon-2 absolute top-2 cursor-pointer right-10 bg-black/50 text-white rounded-md opacity-0  transition-opacity duration-200',
                    html: '<i class="fas fa-download icon-sm-2"></i>',

                }).on('click', function () {
                    const $a = $('<a>', { href: file.url, download: file.fileName });
                    $a[0].click();
                });
                const $deleteBtn = $('<button>', {
                    class: 'btn-icon-2 absolute top-2 cursor-pointer right-10 bg-black/50 text-white rounded-md opacity-0  transition-opacity duration-200',
                    html: '<i class="fas fa-trash-alt icon-sm-2"></i>',

                }).on('click', function () {
                    showCustomModal(
                        'Confirm Delete',
                        'Are you sure you want to delete this file?',
                        `<button id="confirmDeleteFileBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200">Delete</button>
                         <button id="cancelDeleteFileBtn" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors duration-200">Cancel</button>`
                    );
                    $('#confirmDeleteFileBtn').on('click', function () {
                        $.ajax({
                            url: `/api/files/${file._id}`,
                            method: 'DELETE',
                            success: function () {
                                showToast('File deleted successfully!');
                                fetchUploadedImages();
                                $('#customModal').removeClass('flex').addClass('hidden');
                            },
                            error: function (xhr, status, error) {
                                showToast('Failed to delete file. Please try again.', 3000);
                                $('#customModal').removeClass('flex').addClass('hidden');
                            }
                        });
                    });
                    $('#cancelDeleteFileBtn').on('click', function () {
                        $('#customModal').removeClass('flex').addClass('hidden');
                    });
                });

                $imgWrapper.append($imgElement, $downloadBtn, $deleteBtn);
                $container.append($imgWrapper);

                $imgWrapper.hover(
                    function () { $downloadBtn.show(); $deleteBtn.show(); },
                    function () { $downloadBtn.hide(); $deleteBtn.hide(); }
                );

                $imgElement.on('click', function () {
                    var imgSrc = $(this).attr('src');
                    var imgElement = new Image();
                    imgElement.crossOrigin = 'anonymous';
                    imgElement.src = imgSrc;

                    imgElement.onload = function () {
                        var tempCanvas = document.createElement('canvas');
                        var tempCtx = tempCanvas.getContext('2d');
                        tempCanvas.width = imgElement.width;
                        tempCanvas.height = imgElement.height;
                        tempCtx.drawImage(imgElement, 0, 0);
                        var base64Image = tempCanvas.toDataURL('image/png');

                        fabric.Image.fromURL(base64Image, function (img) {
                            if (img) {
                                const canvasAspect = originalCanvasWidth / originalCanvasHeight;
                                const imgAspect = img.width / img.height;

                                let scaleFactor;
                                if (canvasAspect >= imgAspect) {
                                    // Canvas is wider or same aspect as image, scale by height
                                    scaleFactor = originalCanvasHeight / img.height;
                                } else {
                                    // Image is wider than canvas, scale by width
                                    scaleFactor = originalCanvasWidth / img.width;
                                }

                                // Calculate new dimensions after scaling
                                const scaledWidth = img.width * scaleFactor;
                                const scaledHeight = img.height * scaleFactor;

                                // Calculate position to center the image
                                const left = (originalCanvasWidth - scaledWidth) / 2;
                                const top = (originalCanvasHeight - scaledHeight) / 2;

                                canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                    scaleX: scaleFactor,
                                    scaleY: scaleFactor,
                                    left: left,
                                    top: top,
                                    originX: 'left', // Set origin to top-left for positioning
                                    originY: 'top',
                                    crossOrigin: 'anonymous'
                                });
                                showToast('Background applied!');
                            } else {
                                showToast('Failed to load background image. Please check the image source.', 3000);
                            }
                        }, {
                            crossOrigin: 'anonymous',
                            onError: function (img, err) {
                                console.error('Fabric.js Image loading error for background:', err, 'Image URL:', base64Image);
                                showToast('Failed to load background image for canvas. This might be due to a broken link or CORS issues.', 5000);
                            }
                        });

                        // fabric.Image.fromURL(base64Image, function (img) {
                        //     if (img) {
                        //         img.scaleToWidth(150);
                        //         img.scaleToHeight(150);
                        //         img.set({
                        //             left: canvas.width / 2 - img.getScaledWidth() / 2,
                        //             top: canvas.height / 2 - img.getScaledHeight() / 2
                        //         });
                        //         canvas.add(img);
                        //         canvas.renderAll();
                        //         showToast('Image added to canvas!');
                        //     } else {
                        //         showToast('Failed to add image to canvas. Please check the image source.', 3000);
                        //     }
                        // }, {
                        //     crossOrigin: 'anonymous',
                        //     onError: function (img, err) {
                        //         console.error('Fabric.js Image loading error for uploaded image:', err, 'Image URL:', base64Image);
                        //         showToast('Failed to load image for canvas. This might be due to a broken link or CORS issues.', 5000);
                        //     }
                        // });
                    };
                    imgElement.onerror = function () {
                        showToast('Failed to load image. Please check the image source.', 3000);
                    };
                });
            }


            // Drag and drop file upload for #drop-area
            const dropArea = document.getElementById('drop-area');

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropArea.classList.add('highlight');
            }

            function unhighlight() {
                dropArea.classList.remove('highlight');
            }

            dropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFileUpload(files);
            }

            // Drag and drop target for the canvas
            const canvasContainer = document.getElementById('main-content-area');

            canvasContainer.addEventListener('dragover', function (e) {
                e.preventDefault();
            });

            canvasContainer.addEventListener('drop', function (e) {
                e.preventDefault();
                const data = e.dataTransfer.getData('text/plain');
                const dropType = e.dataTransfer.getData('text/x-type');
                const pointer = canvas.getPointer(e);

                if (dropType === 'text-heading' || dropType === 'text-body') {
                    const defaultText = e.dataTransfer.getData('text/x-text-content');
                    const headerType = e.dataTransfer.getData('text/x-header-type');
                    let fontSize = 24;

                    switch (headerType) {
                        case 'h1': fontSize = 30; break;
                        case 'h2': fontSize = 25; break;
                        case 'h3': fontSize = 22; break;
                        case 'h4': fontSize = 20; break;
                        case 'h5': fontSize = 18; break;
                        case 'p': fontSize = 16; break;
                    }

                    const textObject = new fabric.IText(defaultText, {
                        left: pointer.x,
                        top: pointer.y,
                        fontFamily: 'Roboto',
                        fontSize: fontSize,
                        fill: '#000000',
                        fontWeight: 'normal',
                        cornerColor: '#1d89ff',
                        transparentCorners: false,
                        hasControls: true,
                        hasBorders: true,
                    });
                    canvas.add(textObject);
                    canvas.setActiveObject(textObject);
                    canvas.renderAll();
                    showToast(`Text added: "${defaultText}"`);

                } else if (dropType === 'variable') {

                    const variableTag = e.dataTransfer.getData('text/x-variable-tag');
                    const variableName = e.dataTransfer.getData('text/x-variable-name');
                    addVariableToCanvasAtCoords(variableTag, variableName, pointer.x, pointer.y);
                    showToast(`Variable "${variableName}" dropped onto canvas!`);
                }
                else if (dropType === 'shape') {
                    const shapeData = JSON.parse(data);
                    let shape;
                    const commonOptions = {
                        left: pointer.x,
                        top: pointer.y,
                        opacity: 0.8,
                        cornerColor: '#1d89ff',
                        transparentCorners: false,
                        hasControls: true,
                        hasBorders: true,
                        originX: 'center',
                        originY: 'center'
                    };

                    switch (shapeData.type) {
                        case 'rect':
                            shape = new fabric.Rect($.extend(true, {}, commonOptions, { width: 100, height: 100, fill: shapeData.fill, stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth, rx: 5, ry: 5 }));
                            break;
                        case 'circle':
                            shape = new fabric.Circle($.extend(true, {}, commonOptions, { radius: 50, fill: shapeData.fill, stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth }));
                            break;
                        case 'triangle':
                            shape = new fabric.Triangle($.extend(true, {}, commonOptions, { width: 100, height: 100, fill: shapeData.fill, stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth }));
                            break;
                        case 'ellipse':
                            shape = new fabric.Ellipse($.extend(true, {}, commonOptions, { rx: 60, ry: 40, fill: shapeData.fill, stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth }));
                            break;
                        case 'line':
                            shape = new fabric.Line([0, 0, 100, 0], $.extend(true, {}, commonOptions, { stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth, fill: 'transparent' }));
                            break;
                        case 'polygon':
                            shape = new fabric.Polygon([{ x: 0, y: 0 }, { x: 100, y: 0 }, { x: 100, y: 100 }, { x: 0, y: 100 }], $.extend(true, {}, commonOptions, { fill: shapeData.fill, stroke: shapeData.stroke, strokeWidth: shapeData.strokeWidth }));
                            break;
                    }

                    if (shape) {
                        canvas.add(shape);
                        canvas.setActiveObject(shape);
                        canvas.renderAll();
                        showToast(`${shapeData.type.charAt(0).toUpperCase() + shapeData.type.slice(1)} added!`);
                    }
                } else if (dropType === 'icon') {
                    addIconToCanvasAtCoords(data, pointer.x, pointer.y);
                    showToast('Icon dropped onto canvas!');

                } else if (dropType === 'uploaded-image') {

                    addUploadedImageToCanvas(data, pointer.x, pointer.y);
                } else if (dropType === 'pixabay-image') {
                    addImageToCanvas(data, pointer.x, pointer.y);
                } else if (dropType === 'design-background') { // New condition for design backgrounds
                    fabric.Image.fromURL(data, function (img) {
                        if (img) {
                            // Calculate scale factors to make the image cover the entire canvas
                            const scaleX = canvas.width / img.width;
                            const scaleY = canvas.height / img.height;
                            const scale = Math.max(scaleX, scaleY); // Use Math.max to ensure it covers the canvas

                            // Calculate position to center the scaled image
                            const left = (canvas.width - img.width * scale) / 2;
                            const top = (canvas.height - img.height * scale) / 2;

                            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                scaleX: scale,
                                scaleY: scale,
                                originX: 'left', // Keep origin at left/top for easier positioning
                                originY: 'top',
                                left: left, // Position to center
                                top: top,   // Position to center
                                crossOrigin: 'anonymous'
                            });
                            showToast('Design background applied!');
                        } else {
                            showToast('Failed to load design background. Please check the image source.', 3000);
                        }
                    }, {
                        crossOrigin: 'anonymous',
                        onError: function (img, err) {
                            console.error('Fabric.js Image loading error for dropped design background:', err, 'Image URL:', data);
                            showToast('Failed to load design background. This might be due to a broken link or CORS issues.', 5000);
                        }
                    });
                }
                else if (data) {
                    fabric.Image.fromURL(data, function (img) {

                        if (img) {
                            img.set({
                                left: pointer.x,
                                top: pointer.y,
                                originX: 'center',
                                originY: 'center',
                                cornerColor: '#1d89ff',
                                transparentCorners: false,
                                hasControls: true,
                                hasBorders: true
                            });

                            canvas.add(img);
                            canvas.setActiveObject(img);
                            canvas.renderAll();
                            showToast('External image dropped onto canvas!');
                        } else {
                            showToast('Failed to add image to canvas. Please check the URL.', 3000);
                        }
                    }, {
                        crossOrigin: 'anonymous',
                        onError: function (img, err) {
                            console.error('Fabric.js Image loading error for dragged image:', err, 'Image URL:', data);
                            showToast('Failed to load image for canvas. This might be due to a broken link or CORS issues.', 5000);
                        }
                    });
                }
            });


            // Dragstart for text elements
            $('#text-content button').on('dragstart', function (e) {
                const defaultText = $(this).data('text');
                const headerType = $(this).data('header');
                e.originalEvent.dataTransfer.setData('text/plain', defaultText);
                e.originalEvent.dataTransfer.setData('text/x-type', $(this).data('type')); // This is the key line
                e.originalEvent.dataTransfer.setData('text/x-text-content', defaultText);
                e.originalEvent.dataTransfer.setData('text/x-header-type', headerType);
            });

            // Dragstart for shape elements
            $('#elements-content button').on('dragstart', function (e) {
                const shapeType = $(this).data('type');
                const fill = $(this).data('fill');
                const stroke = $(this).data('stroke');
                const strokeWidth = $(this).data('stroke-width');
                const shapeData = { type: shapeType, fill: fill, stroke: stroke, strokeWidth: strokeWidth };
                e.originalEvent.dataTransfer.setData('text/plain', JSON.stringify(shapeData));
                e.originalEvent.dataTransfer.setData('text/x-type', 'shape');
            });



            // Drag Start Event on icon items


            $('.variable-draggable-item').on('dragstart', function (e) { // <-- Changed selector
                const originalEvent = e.originalEvent;
                const variableTag = $(this).data('variable-tag');
                const variableName = $(this).text();

                originalEvent.dataTransfer.setData('text/plain', variableTag);
                originalEvent.dataTransfer.setData('text/x-type', 'variable'); // This will now only apply to variables
                originalEvent.dataTransfer.setData('text/x-variable-tag', variableTag);
                originalEvent.dataTransfer.setData('text/x-variable-name', variableName);
                originalEvent.dataTransfer.effectAllowed = 'copy';
            });
            /**
             * Applies the current zoom level and centers the canvas.
             */
            function applyCanvasTransformations() {
                const parent = document.getElementById("main-content-area");
                if (!parent) return;

                const containerWidth = parent.clientWidth - 40;
                const containerHeight = parent.clientHeight - 40;

                const fitZoom = Math.min(containerWidth / originalCanvasWidth, containerHeight / originalCanvasHeight);

                const effectiveZoom = fitZoom * currentZoomLevel;

                canvas.setDimensions({
                    width: originalCanvasWidth * effectiveZoom,
                    height: originalCanvasHeight * effectiveZoom
                }, { cssOnly: false });
                canvas.setZoom(effectiveZoom);

                canvas.wrapperEl.style.position = 'absolute';
                canvas.wrapperEl.style.left = '50%';
                canvas.wrapperEl.style.top = '50%';
                canvas.wrapperEl.style.transform = 'translate(-50%, -50%)';

                canvas.renderAll();
            }


            //  * Adds an uploaded image to the canvas with proportional scaling to a max dimension.

            function addUploadedImageToCanvas(imageUrl, x, y) {
                fabric.Image.fromURL(imageUrl, function (img) {
                    if (img) {
                        let scaleFactor = 1;
                        // This block calculates the scale factor to fit within MAX_UPLOADED_IMAGE_DIMENSION
                        if (img.width > MAX_UPLOADED_IMAGE_DIMENSION || img.height > MAX_UPLOADED_IMAGE_DIMENSION) {
                            scaleFactor = Math.min(MAX_UPLOADED_IMAGE_DIMENSION / img.width, MAX_UPLOADED_IMAGE_DIMENSION / img.height);
                        }

                        img.set({
                            // ... other properties ...
                            scaleX: scaleFactor, // Apply the calculated scale
                            scaleY: scaleFactor, // Apply the calculated scale
                            // ... other properties ...
                        });
                        canvas.add(img);
                        canvas.setActiveObject(img);
                        canvas.renderAll();
                        showToast('Uploaded image added to canvas!');
                    }
                    // ... error handling ...
                });
            }

            /**
             * Adjusts canvas size to fit its parent container while maintaining aspect ratio.
             */
            function resizeCanvas() {
                const container = $('#main-content-area');
                const containerWidth = container.width();
                const containerHeight = container.height();

                const fitZoom = Math.min(
                    (containerWidth * 0.9) / originalCanvasWidth,
                    (containerHeight * 0.9) / originalCanvasHeight
                );

                currentZoomLevel = 1.0;
                $('#zoomSlider').val(currentZoomLevel.toFixed(1));
                $('#zoomValue').text(currentZoomLevel.toFixed(1) + 'x');

                applyCanvasTransformations();
            }

            /**
             * Helper function to download canvas as image.
             */
            function downloadCanvas(format, dpi = 96) {
                if (!canvas) {
                    showToast('Canvas not ready!', 2000);
                    return;
                }
                const scale = dpi / 96;

                canvas.setDimensions({
                    width: originalCanvasWidth * scale,
                    height: originalCanvasHeight * scale
                });
                canvas.setZoom(scale);
                canvas.renderAll();

                const dataURL = canvas.toDataURL({
                    format: format,
                    quality: 1.0,
                    multiplier: 1,
                });

                applyCanvasTransformations();

                const link = document.createElement('a');
                link.download = `mixcertificate-design.${format}`;
                link.href = dataURL;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                showToast(`Design downloaded as ${format.toUpperCase()}!`);
            }

            // Download PNG.
            $('#downloadPngBtn, #downloadPngBtn-mobile').on('click', function (e) {
                e.preventDefault();
                downloadCanvas('png');
            });

            // Download JPEG.
            $('#downloadJpegBtn, #downloadJpegBtn-mobile').on('click', function (e) {
                e.preventDefault();
                downloadCanvas('jpeg');
            });

            // Download PNG for print (300 DPI).
            $('#printDownloadPngBtn, #printDownloadPngBtn-mobile').on('click', function (e) {
                e.preventDefault();
                downloadCanvas('png', 300);
            });

            // Download JPEG for print (300 DPI).
            $('#printDownloadJpegBtn, #printDownloadJpegBtn-mobile').on('click', function (e) {
                e.preventDefault();
                downloadCanvas('jpeg', 300);
            });

            // Download PDF.
            $('#downloadPdfBtn, #downloadPdfBtn-mobile').on('click', function (e) {
                e.preventDefault();
                if (!window.jspdf || !window.jspdf.jsPDF) {
                    showToast('jsPDF library not loaded.', 2000);
                    return;
                }

                const scale = 300 / 72;

                canvas.setDimensions({
                    width: originalCanvasWidth * scale,
                    height: originalCanvasHeight * scale
                });
                canvas.setZoom(scale);
                canvas.renderAll();

                const imgData = canvas.toDataURL('image/jpeg', 1.0);

                applyCanvasTransformations();

                const doc = new window.jspdf.jsPDF({
                    orientation: originalCanvasWidth > originalCanvasHeight ? 'landscape' : 'portrait',
                    unit: 'px',
                    format: [originalCanvasWidth, originalCanvasHeight]
                });

                doc.addImage(imgData, 'JPEG', 0, 0, originalCanvasWidth, originalCanvasHeight);
                doc.save('mixcertificate-design.pdf');
                showToast('Design downloaded as PDF!');
            });

            // Preview button functionality.
            $('#previewBtn, #previewBtn-mobile').on('click', function () {
                const dataURL = canvas.toDataURL({
                    format: 'png',
                    quality: 1.0,
                    multiplier: 1.5
                });
                showCustomModal('Canvas Preview', `<div class="flex justify-center items-center"><img src="${dataURL}" class="max-w-full h-auto rounded-lg shadow-xl border border-gray-700"/></div>`);
            });

            // Zoom functionality.
            $('#zoomSlider').on('input', function () {
                currentZoomLevel = parseFloat($(this).val());
                $("#zoomValue").text(currentZoomLevel.toFixed(1) + "x");
                applyCanvasTransformations();
            });

            // Initial resize and on window resize.
            $(window).on('resize', resizeCanvas);
            resizeCanvas();

            $.contextMenu({
                selector: 'canvas',
                callback: function (key, options) {
                    var activeObject = canvas.getActiveObject();

                    if (!activeObject && key !== 'paste') {
                        return;
                    }

                    switch (key) {
                        case 'copy':
                            if (activeObject) {
                                copiedObject = fabric.util.object.clone(activeObject);
                            }
                            break;
                        case 'copyStyle':
                            if (activeObject) {
                                copiedObjectStyle = {
                                    fill: activeObject.fill,
                                    stroke: activeObject.stroke,
                                    strokeWidth: activeObject.strokeWidth,
                                    fontFamily: activeObject.fontFamily,
                                    fontSize: activeObject.fontSize,
                                    fontWeight: activeObject.fontWeight,
                                    fontStyle: activeObject.fontStyle,
                                    textDecoration: activeObject.textDecoration,
                                };
                            }
                            break;
                        case 'paste':
                            if (copiedObject) {
                                copiedObject.clone(function (cloned) {
                                    cloned.set({
                                        left: cloned.left + 20,
                                        top: cloned.top + 20,
                                        evented: true,
                                        selectable: true
                                    });
                                    canvas.add(cloned);
                                    canvas.setActiveObject(cloned);
                                    canvas.renderAll();
                                });
                            }
                            break;
                        case 'pasteStyle':
                            if (activeObject && copiedObjectStyle) {
                                activeObject.set(copiedObjectStyle);
                                canvas.renderAll();
                            }
                            break;
                        case 'duplicate':
                            if (activeObject) {
                                activeObject.clone(function (cloned) {
                                    cloned.set({
                                        left: cloned.left + 20,
                                        top: cloned.top + 20,
                                        evented: true,
                                        selectable: true
                                    });
                                    canvas.add(cloned);
                                    canvas.setActiveObject(cloned);
                                    canvas.renderAll();
                                });
                            }
                            break;
                        case 'delete':
                            if (activeObject) {
                                canvas.remove(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            }
                            break;
                        case 'lock':
                            if (activeObject) {
                                activeObject.set({
                                    selectable: false,
                                    evented: false,
                                    hasControls: false,
                                    hasBorders: false,
                                    lockMovementX: true,
                                    lockMovementY: true,
                                    lockRotation: true,
                                    lockScalingX: true,
                                    lockScalingY: true
                                });
                                canvas.renderAll();
                            }
                            break;
                        case 'unlock':
                            if (activeObject) {
                                activeObject.set({
                                    selectable: true,
                                    evented: true,
                                    hasControls: true,
                                    hasBorders: true,
                                    lockMovementX: false,
                                    lockMovementY: false,
                                    lockRotation: false,
                                    lockScalingX: false,
                                    lockScalingY: false
                                });
                                canvas.renderAll();
                            }
                            break;
                        case 'alignToPage':
                            if (activeObject) {
                                var scaledWidth = activeObject.getScaledWidth();
                                var scaledHeight = activeObject.getScaledHeight();

                                activeObject.set({
                                    left: (canvas.width / 2) - (scaledWidth / 2),
                                    top: (canvas.height / 2) - (scaledHeight / 2)
                                });
                                canvas.renderAll();
                            }
                            break;
                        case 'setAsBackground':
                            if (activeObject && activeObject.type === 'image') {
                                canvas.setBackgroundImage(activeObject.toDataURL(), canvas.renderAll.bind(canvas), {
                                    scaleX: canvas.width / activeObject.width,
                                    scaleY: canvas.height / activeObject.height,
                                    top: 0,
                                    left: 0,
                                    originX: 'left',
                                    originY: 'top'
                                });
                                canvas.remove(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            } else if (activeObject) {
                                alert('Only image objects can be set as background.');
                            }
                            break;
                        case 'bringForward':
                            if (activeObject) {
                                canvas.bringForward(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            }
                            break;
                        case 'sendBackwards':
                            if (activeObject) {
                                canvas.sendBackwards(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            }
                            break;
                        case 'bringToFront':
                            if (activeObject) {
                                canvas.bringToFront(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            }
                            break;
                        case 'sendToBack':
                            if (activeObject) {
                                canvas.sendToBack(activeObject);
                                canvas.discardActiveObject();
                                canvas.renderAll();
                            }
                            break;
                        case 'link':
                            if (activeObject) {
                                var link = prompt("Enter a link for the object (e.g., https://example.com):");
                                if (link) {
                                    activeObject.set({ link: link });
                                }
                            }
                            break;
                    }
                },
                items: {
                    "copy": { name: 'Copy ' },
                    "paste": { name: 'Paste ' },
                    "duplicate": { name: 'Duplicate' },
                    "copyStyle": { name: 'Copy Style' },
                    "pasteStyle": { name: 'Paste Style' },
                    "delete": { name: 'Delete' },
                    "sep1": "---------",
                    "bringForward": { name: 'Bring Forward' },
                    "sendBackwards": { name: 'Send Backwards' },
                    "bringToFront": { name: 'Bring To Front' },
                    "sendToBack": { name: 'Send To Back' },
                    "sep2": "---------",
                    "lock": { name: 'Lock' },
                    "unlock": { name: 'Unlock' },
                    "alignToPage": { name: 'Align to Page' },
                    "link": { name: 'Add Link' },
                    "setAsBackground": { name: 'Set Image as Background' }
                }
            });

            var noteTooltip = $('<div>', {
                id: 'noteTooltip',
                class: 'd-none bg-light p-2 border rounded shadow',
                text: 'Add a note'
            }).appendTo('body');

            $(document).on('mousemove', function (e) {
                noteTooltip.css({
                    left: e.pageX + 10 + 'px',
                    top: e.pageY + 10 + 'px'
                });
            });

            canvas.on('mouse:down', function (opt) {
                var evt = opt.e;
                if (evt.button === 3) {
                    evt.preventDefault();
                    var pointer = canvas.getPointer(evt);
                    canvas.setActiveObject(canvas.findTarget(pointer));
                    canvas.renderAll();
                }
            });

            // Mobile menu toggle.
            $('#mobile-menu-button').on('click', function () {
                $('#mobile-menu-dropdown').slideToggle();
            });

            // General dropdown toggle logic for mobile.
            $('#downloadDropdown-mobile').on('click', function () {
                $(this).next('ul').slideToggle();
            });
            $('#dropdownMenuButton-mobile').on('click', function () {
                $(this).next('ul').slideToggle();
            });

            // Fullscreen toggle.
            $('#fullscreenToggle').on('click', function () {
                const mainContentArea = document.getElementById('main-content-area');
                if (!document.fullscreenElement) {
                    if (mainContentArea.requestFullscreen) {
                        mainContentArea.requestFullscreen().then(() => {
                            resizeCanvas();
                        }).catch(err => {
                            showToast(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`, 4000);
                        });
                    }
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen().then(() => {
                            resizeCanvas();
                        }).catch(err => {
                            showToast(`Error attempting to exit full-screen mode: ${err.message} (${err.name})`, 4000);
                        });
                    }
                }
            });

            // Listens for fullscreen change events to update the button icon and canvas size.
            document.addEventListener('fullscreenchange', () => {
                const fullscreenIcon = $('#fullscreenToggle i');
                if (document.fullscreenElement) {
                    fullscreenIcon.text('fullscreen_exit');
                } else {
                    fullscreenIcon.text('fullscreen');
                }
                resizeCanvas();
            });

            // Activate Design menu by default on load
            $('#sidebar button[data-target="#design-content"]').trigger('click');

            /**
             * Uploads canvas image.
             */
            const uploadImage = (dataURL) => {
                return new Promise((resolve, reject) => {
                    const byteString = atob(dataURL.split(',')[1]);
                    const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
                    const arrayBuffer = new ArrayBuffer(byteString.length);
                    const intArray = new Uint8Array(arrayBuffer);

                    for (let i = 0; i < byteString.length; i++) {
                        intArray[i] = byteString.charCodeAt(i);
                    }

                    const blob = new Blob([arrayBuffer], { type: mimeString });
                    const formData = new FormData();
                    formData.append('file', blob, 'design.jpg');

                    $.ajax({
                        url: '/api/files/upload/designs',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function () {
                            showGlobalLoader(); // Show loader for image upload
                        },
                        success: function (response) {
                            if (response.file && response.file.url) {
                                resolve(response.file.url);
                            } else {
                                reject('File upload failed. No URL returned.');
                            }
                        },
                        error: function (xhr, status, error) {
                            reject(`Image upload failed: ${error}`);
                        },
                        complete: function () {
                            hideGlobalLoader(); // Hide loader after image upload
                        }
                    });
                });
            };

            /**
             * Uploads the current canvas content as an image.
             */
            const uploadCanvasImage = () => {
                return new Promise(async (resolve, reject) => {
                    try {
                        const dataURL = canvas.toDataURL({ format: 'jpeg', quality: 0.8 });
                        const fileUrl = await uploadImage(dataURL);
                        resolve(fileUrl);
                    } catch (error) {
                        reject(`Error uploading canvas image: ${error}`);
                    }
                });
            };

            /**
             * Loads design data if designId exists in the URL.
             */
            function loadDesignIfExists() {
                var currentUrl = window.location.href;
                var designIdMatch = currentUrl.match(/\/design\/(\w+)/);
                var designId = designIdMatch ? designId[1] : null;

                if (designId) {
                    $.ajax({
                        url: `/api/design/${designId}`,
                        type: 'GET',
                        beforeSend: function () {
                            showGlobalLoader(); // Show loader when fetching design
                        },
                        success: function (response) {
                            if (response.canvas) {
                                var canvasData = response.canvas;

                                if (canvasData.width && canvasData.height) {
                                    originalCanvasWidth = canvasData.width;
                                    originalCanvasHeight = canvasData.height;
                                }

                                canvas.loadFromJSON(canvasData, function () {
                                    canvas.renderAll();
                                    resizeCanvas();
                                    saveCanvasState();
                                });

                                $("#designName").val(response.designName);
                            }
                        },
                        error: function (xhr, status, error) {
                            showToast('Error loading design. Please try again.', 3000);
                        },
                        complete: function () {
                            hideGlobalLoader(); // Hide loader after fetching design
                        }
                    });
                } else {
                    saveCanvasState();
                }
            }

            // Call this function to check for designId and load data
            loadDesignIfExists();

            /**
             * Saves or updates the canvas data.
             */
            function saveCanvas() {
                var $saveButton = $('#saveCanvas');

                $saveButton.prop('disabled', true);
                $saveButton.html(`
                    <span class="flex items-center">
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Saving
                    </span>
                `);

                var currentUrl = window.location.href;
                var designIdMatch = currentUrl.match(/\/design\/(\w+)/);
                var designId = designIdMatch ? designId[1] : null;

                var canvasData = canvas.toJSON();
                canvasData.width = originalCanvasWidth;
                canvasData.height = originalCanvasHeight;

                uploadCanvasImage()
                    .then((imageUrl) => {
                        var requestData = JSON.stringify({
                            designName: $("#designName").val(),
                            imageURL: imageUrl,
                            canvas: canvasData
                        });

                        if (designId) {
                            $.ajax({
                                url: `/api/design/${designId}`,
                                type: 'PUT',
                                contentType: 'application/json',
                                data: requestData,
                                success: function (response) {
                                    $saveButton.text('Saved');
                                    $saveButton.prop('disabled', false);
                                    showToast('Design saved successfully!');
                                    setTimeout(function () {
                                        $saveButton.html(`<i class="fas fa-save me-2"></i>
                                            Save
                                        `);
                                    }, 4000);
                                },
                                error: function (xhr, status, error) {
                                    showToast('Error saving design. Please try again.', 3000);
                                    $saveButton.text('Save Failed');
                                    $saveButton.prop('disabled', false);
                                    setTimeout(function () {
                                        $saveButton.html(`<i class="fas fa-save me-2"></i>
                                            Save
                                        `);
                                    }, 4000);
                                }
                            });
                        } else {
                            $.ajax({
                                url: '/api/design/',
                                type: 'POST',
                                contentType: 'application/json',
                                data: requestData,
                                success: function (response) {
                                    var newTemplateId = response._id;
                                    var newUrl = `${window.location.origin}/design/${newTemplateId}`;
                                    window.history.pushState({ path: newUrl }, '', newUrl);
                                    $saveButton.text('Saved');
                                    $saveButton.prop('disabled', false);
                                    showToast('Design created and saved successfully!');
                                    setTimeout(function () {
                                        $saveButton.html(`<i class="fas fa-save me-2"></i>
                                            Save
                                        `);
                                    }, 4000);

                                },
                                error: function (xhr, status, error) {
                                    showToast('Error creating design. Please try again.', 3000);
                                    $saveButton.text('Save Failed');
                                    $saveButton.prop('disabled', false);
                                    setTimeout(function () {
                                        $saveButton.html(`<i class="fas fa-save me-2"></i>
                                            Save
                                        `);
                                    }, 4000);
                                }
                            });
                        }
                    })
                    .catch((error) => {
                        showToast('Error preparing design for save. Please try again.', 3000);
                        $saveButton.text('Save Failed');
                        $saveButton.prop('disabled', false);
                        setTimeout(function () {
                            $saveButton.html(`<i class="fas fa-save me-2"></i>
                                Save
                            `);
                        }, 4000);
                    });
            }

            document.getElementById("saveCanvas").addEventListener("click", saveCanvas);
        });

        /**
         * Opens the editor with specified canvas dimensions.
         */
        function openEditor(type) {
            let width, height;
            switch (type) {
                case 'horizontal':
                    width = 800; height = 600;
                    break;
                case 'vertical':
                    width = 600; height = 800;
                    break;
                case 'badge':
                    width = 300; height = 300;
                    break;
            }
            window.open(`/design/?width=${width}&height=${height}`, '_blank');
        }

        /**
         * Creates a custom design with user-defined width and height.
         */
        function createCustomDesign() {
            const width = document.getElementById('customWidth').value;
            const height = document.getElementById('customHeight').value;
            if (width && height) {
                window.open(`/design/?width=${width}&height=${height}`, '_blank');
            } else {
                showToast('Please enter both width and height!', 2000);
            }
        }

        var iconPage = 1; // Current page number for icons
        var iconQuery = ''; // Current search query for icons
        var iconLoading = false; // To prevent multiple simultaneous requests

        /**
         * Search Iconfinder function.
         */
        function searchIconfinder(query, page) {
            if (iconLoading) return;
            iconLoading = true;

            $('#iconLoading').removeClass('hidden'); // Show loading spinner

            $.ajax({
                url: `/api/iconfinder?query=${query}&limit=60&page=${page}`,
                method: 'GET',
                success: function (data) {
                    displayIconResults(data.icons);
                    iconLoading = false;
                    $('#iconLoading').addClass('hidden'); // Hide loading spinner
                },
                error: function (err) {
                    console.error('Error fetching Iconfinder icons:', err);
                    iconLoading = false;
                    $('#iconLoading').addClass('hidden'); // Hide loading spinner
                }
            });
        }


        /**
         * Function to get the best preview URL (smaller image for preview).
         */
        function getBestPreviewUrl(rasterSizes) {
            var bestSize = rasterSizes.find(size => size.size === 64); // Prefer 64x64 for preview
            if (!bestSize) {
                bestSize = rasterSizes[rasterSizes.length - 1]; // Fallback to largest available size
            }
            if (bestSize && bestSize.formats && bestSize.formats.length > 0) {
                return bestSize.formats[0].preview_url;
            }
            return null;
        }

        /**
         * Function to get the best download URL (larger image for canvas).
         */
        function getBestDownloadUrl(rasterSizes) {
            var bestSize = rasterSizes.find(size => size.size >= 512); // Prefer 512x512 or larger for high quality
            if (!bestSize) {
                bestSize = rasterSizes[rasterSizes.length - 1]; // Fallback to largest available size
            }
            if (bestSize && bestSize.formats && bestSize.formats.length > 0) {
                return bestSize.formats[0].download_url;
            }
            return null;
        }

        /**
         * Adds an SVG icon to Fabric.js canvas at specific coordinates (for drag-and-drop).
         */
        // Updated addIconToCanvasAtCoords function
        function addIconToCanvasAtCoords(iconUrl, x, y) {


            fabric.loadSVGFromURL(iconUrl, function (objects, options) {
                if (!objects || objects.length === 0) {
                    console.error("No SVG objects loaded from URL: ", iconUrl);
                    return;
                }

                let svgObject;
                if (objects.length > 1) {
                    svgObject = fabric.util.groupSVGElements(objects, options);
                } else {
                    svgObject = objects[0];
                    if (options) {
                        svgObject.set(options);
                    }
                }

                if (!svgObject) {
                    console.error("SVG Object failed to be created from URL: ", iconUrl);
                    return;
                }

                // const targetSize = 100;
                // const imgWidth = svgObject.width || targetSize;
                // const imgHeight = svgObject.height || targetSize;
                // const scaleFactor = Math.min(targetSize / imgWidth, targetSize / imgHeight);
                // svgObject.scale(scaleFactor);

                // --- START OF UPDATED LOGIC ---
                // Set the object's left and top directly to the drop coordinates (x, y).
                // This is because we will set its origin to 'left' and 'top',
                // meaning the top-left corner of the object will be placed at (x, y).
                svgObject.set({
                    left: x,
                    top: y,
                    originX: 'left',
                    originY: 'top',
                    hasControls: true,
                    hasBorders: true,
                    selectable: true,
                    evented: true,
                    isIcon: true
                });

                // Apply the same origin setting approach as in addIconToCanvas.
                // Note: Fabric.js 5.x.x typically uses object.set({ originX: 'left', originY: 'top' }).
                // This try/catch block is replicated from your existing addIconToCanvas function.
                try {
                    svgObject.setOriginX('left');
                    svgObject.setOriginY('top');
                } catch (e) {
                    console.error("Error setting origin for svgObject:", svgObject, e);
                }
                // --- END OF UPDATED LOGIC ---

                // The rest of the properties for interactivity remain the same
                svgObject.set({
                    hasControls: true,
                    hasBorders: true,
                    selectable: true,
                    evented: true
                });

                canvas.add(svgObject);
                canvas.bringToFront(svgObject);
                canvas.renderAll();

            }, function (xhr) {
                console.error("Failed to load SVG (drag-and-drop) from URL: ", iconUrl, xhr);
            }, {
                crossOrigin: 'anonymous'
            });
        }


        /**
         * Adds an icon (SVG or raster image like PNG/JPG) to the Fabric.js canvas,
         * centering it and making it interactive.
         */
        function addIconToCanvas(iconUrl) {


            if (iconUrl.endsWith('.svg')) {

                fabric.loadSVGFromURL(iconUrl, function (objects, options) {
                    if (!objects || objects.length === 0) {
                        console.error("No SVG objects loaded from URL (SVG path): ", iconUrl);
                        return;
                    }
                    let svgObject;
                    if (objects.length > 1) {
                        svgObject = fabric.util.groupSVGElements(objects, options);
                    } else {
                        svgObject = objects[0];
                        if (options) {
                            svgObject.set(options);
                        }
                    }

                    if (!svgObject) {
                        console.error("SVG Object failed to be created from URL (SVG path): ", iconUrl);
                        return;
                    }

                    const targetSize = 100;
                    const imgWidth = svgObject.width || targetSize;
                    const imgHeight = svgObject.height || targetSize;
                    const scaleFactor = Math.min(targetSize / imgWidth, targetSize / imgHeight);
                    svgObject.scale(scaleFactor);

                    svgObject.set({
                        left: canvas.getWidth() / 2,
                        top: canvas.getHeight() / 2,
                        originX: 'center',
                        originY: 'center',
                        hasControls: true,
                        hasBorders: true,
                        selectable: true,
                        evented: true
                    });

                    canvas.add(svgObject);
                    canvas.bringToFront(svgObject);
                    canvas.renderAll();

                }, function (xhr) {
                    console.error("Failed to load SVG from URL (SVG path XHR error): ", iconUrl, xhr);
                }, {
                    crossOrigin: 'anonymous'
                });
            } else {

                fabric.Image.fromURL(iconUrl, function (img) {
                    if (!img) {
                        console.error("Fabric.js did not create an image object from URL:", iconUrl);
                        return;
                    }



                    const targetSize = 100;
                    const imgWidth = img.width;
                    const imgHeight = img.height;

                    if (imgWidth === 0 || imgHeight === 0) {
                        console.error("Image has zero dimensions, cannot scale:", iconUrl);
                        return;
                    }

                    const scaleFactor = Math.min(targetSize / imgWidth, targetSize / imgHeight);
                    img.scale(scaleFactor);



                    img.set({
                        left: canvas.getWidth() / 2,
                        top: canvas.getHeight() / 2,
                        originX: 'center',
                        originY: 'center',
                        hasControls: true,
                        hasBorders: true,
                        selectable: true,
                        evented: true
                    });

                    canvas.add(img);
                    canvas.bringToFront(img);
                    canvas.renderAll();


                }, {
                    crossOrigin: 'anonymous',
                    error: function (error) {
                        console.error("Fabric.js Image loading failed for URL:", iconUrl, error);
                    }
                });
            }
        }

        // Event delegation for dynamically added image elements
        $(document).on('click', '.icon-item', function () {
            // Retrieve the high-resolution download URL from the data-full attribute
            var fullUrl = $(this).find('img').attr('data-full');

            // If data-full isn't available, fall back to the preview URL (though not ideal for canvas quality)
            if (!fullUrl) {
                fullUrl = $(this).find('img').attr('src');
                console.warn("Using preview URL for canvas, consider enabling data-full for high-res icons.");
            }

            addIconToCanvas(fullUrl); // Call the function to add image to canvas
        });





        function displayIconResults(icons) {
            var resultsContainer = $('#iconResults');
            // If it's the first page, clear existing results. Otherwise, append.
            if (iconPage === 1) {
                resultsContainer.empty();
            }

            if (icons.length === 0 && iconPage === 1) {
                resultsContainer.html('<p class="text-gray-500 text-center">No icons found for your search.</p>');
                return;
            }

            icons.forEach(function (icon) {
                var previewUrl = getBestPreviewUrl(icon.raster_sizes);
                // --- START OF FIX ---
                // Prioritize icon.svg_url if available, otherwise use the previewUrl.
                // Avoid using getBestDownloadUrl().formats[0].download_url directly for client-side loading
                // as it often requires authentication that Fabric.js cannot provide.
                var canvasLoadUrl = icon.svg_url || previewUrl;
                // --- END OF FIX ---

                if (previewUrl && canvasLoadUrl) {
                    var imgElement = $('<img>')
                        .attr('src', previewUrl) // Use previewUrl for the visible image in the palette
                        .attr('alt', 'Iconfinder Icon')
                        .css({ 'width': '50px', 'height': '50px', 'object-fit': ' contain' });

                    var divWrapper = $('<div>')
                        .attr('class', 'icon-item cursor-pointer bg-white rounded-md p-1 flex items-center justify-center')
                        .attr('draggable', 'true')
                        .data('icon-url', canvasLoadUrl); // Store the best publicly accessible URL for Fabric.js to load

                    divWrapper.append(imgElement);
                    resultsContainer.append(divWrapper);
                }
            });
        }

        // Search button click event for Icons
        $('#searchIconBtn').on('click', function () {
            iconQuery = $('#icons-search-input').val().trim();
            iconPage = 1;
            $('#iconResults').empty();
            if (iconQuery) {
                searchIconfinder(iconQuery, iconPage);
            } else {
                showToast('Please enter a search term for icons.', 2000);
            }
        });

        // Infinite scroll event for icons
        $('#sidebar-content-area').on('scroll', function () {
            const $this = $(this);
            if ($this.scrollTop() + $this.innerHeight() >= $this[0].scrollHeight - 50) {
                if (!iconLoading && iconQuery && $('#shapes-elements-content').is(':visible')) {
                    iconPage++;
                    searchIconfinder(iconQuery, iconPage);
                }
            }
        });

        // Initialize icon search on page load if input has a value
        $(document).ready(function () {
            if ($('#icons-search-input').val().trim()) {
                iconQuery = $('#icons-search-input').val().trim();
                searchIconfinder(iconQuery, iconPage);
            }
        });

        // --- Pixabay (Photos) API Integration ---
        photoPage = 1;
        photoQuery = '';
        photoLoading = false;

        /**
         * Searches for photos using the Pixabay API.
         */
        function searchPixabay(query, page) {
            if (photoLoading) return;
            photoLoading = true;

            $('#photoLoading').removeClass('hidden');

            $.ajax({
                url: `/api/pixabay?q=${query}&type=photo&per_page=20&hits=18&page=${page}`,
                method: 'GET',
                success: function (data) {
                    displayPhotoResults(data.hits);
                    photoLoading = false;
                    $('#photoLoading').addClass('hidden');
                },
                error: function (err) {
                    photoLoading = false;
                    $('#photoLoading').addClass('hidden');
                    showToast('Error fetching photos. Please try again.', 3000);
                }
            });
        }

        /**
         * Displays photo search results.
         */
        function displayPhotoResults(images) {
            var resultsContainer = $('#photoSearchResults');
            if (photoPage === 1) { // Clear only for the first page of results
                resultsContainer.empty();
            }
            images.forEach(function (image) {
                var imgElement = $('<img>')
                    .attr('src', image.previewURL)
                    .attr('class', 'photo-item cursor-pointer rounded-md shadow-md hover:shadow-lg transition-shadow duration-200')
                    .attr('data-full', image.largeImageURL)
                    .attr('alt', 'Pixabay Image')
                    .attr('crossorigin', 'anonymous')
                    .css({ 'width': '100%', 'height': 'auto' });

                imgElement.on('click', function () {
                    addImageToCanvas($(this).data('full'));
                });

                resultsContainer.append($('<div class="w-full"></div>').append(imgElement));
            });
        }

        // Search button click event for Photos
        $('#photoSearchButton').on('click', function () {
            photoQuery = $('#photosSearchQuery').val().trim();
            photoPage = 1;
            $('#photoSearchResults').empty();
            if (photoQuery) {
                searchPixabay(photoQuery, photoPage);
            } else {
                showToast('Please enter a search term for photos.', 2000);
            }
        });

        // Infinite scroll event for photos
        $('#sidebar-content-area').on('scroll', function () {
            const $this = $(this);
            if ($this.scrollTop() + $this.innerHeight() >= $this[0].scrollHeight - 50) {
                if (!photoLoading && photoQuery && $('#photos-content').is(':visible')) {
                    photoPage++;
                    searchPixabay(photoQuery, photoPage);
                }
            }
        });

        // Initialize photo search on page load if input has a value
        $(document).ready(function () {
            if ($('#photosSearchQuery').val().trim()) { // Corrected ID
                photoQuery = $('#photosSearchQuery').val().trim();
                searchPixabay(photoQuery, photoPage); // Perform search on page load if input has a value
            }
        });
        // --- End Pixabay (Photos) API Integration ---

        function addImageToCanvas(imageUrl) {
            fabric.Image.fromURL(imageUrl, function (img) {
                if (img) {
                    img.scaleToWidth(150);
                    img.scaleToHeight(150);
                    img.set({
                        left: canvas.width / 2 - img.getScaledWidth() / 2,
                        top: canvas.height / 2 - img.getScaledHeight() / 2,
                        cornerColor: '#1d89ff',
                        transparentCorners: false,
                        hasControls: true,
                        hasBorders: true,
                        isIcon: false // Explicitly mark as not an icon
                    });
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    canvas.renderAll();
                    showToast('Image added to canvas!');
                } else {
                    showToast('Failed to add image to canvas. Please check the image source.', 3000);
                }
            }, {
                crossOrigin: 'anonymous',
                onError: function (img, err) {
                    console.error('Fabric.js Image loading error:', err, 'Image URL:', imageUrl);
                    showToast('Failed to load image for canvas. This might be due to a broken link or CORS issues.', 5000);
                }
            });
        }
        /**
         * Generates and displays a QR code.
         */
        $(document).ready(function () {
            $('#generateQrCodeBtn').on('click', function () {
                var url = $('#qrCodeUrlInput').val().trim();

                if (!url) {
                    showToast('Please enter a URL for the QR Code.', 2000);
                    return;
                }

                $('#qrCodeDisplay').empty(); // Clear previous QR code
                $('#qrCodeLoading').removeClass('hidden'); // Show QR code loader

                $.ajax({
                    url: '/api/qr/basic',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ url: url }),
                    success: function (response) {
                        if (response.qrCodeUrl) {
                            var qrImg = $('<img>')
                                .attr('src', response.qrCodeUrl)
                                .attr('alt', 'QR Code')
                                .addClass('w-32 h-32 cursor-pointer rounded-md shadow-md hover:shadow-lg transition-shadow duration-200');

                            qrImg.on('click', function () {
                                fabric.Image.fromURL(response.qrCodeUrl, function (img) {
                                    if (img) { // Check if image loaded successfully
                                        img.scaleToWidth(150);
                                        img.scaleToHeight(150);

                                        var canvasCenterX = canvas.width / 2;
                                        var canvasCenterY = canvas.height / 2;

                                        img.set({
                                            left: canvasCenterX,
                                            top: canvasCenterY,
                                            originX: 'center',
                                            originY: 'center',
                                            cornerColor: '#1d89ff',
                                            transparentCorners: false,
                                            hasControls: true,
                                            hasBorders: true
                                        });

                                        canvas.add(img);
                                        canvas.renderAll();
                                        showToast('QR Code added to canvas!');
                                    } else {
                                        showToast('Failed to add QR code to canvas. Please check the URL.', 3000);
                                    }
                                }, {
                                    crossOrigin: 'anonymous',
                                    onError: function (img, err) {
                                        console.error('Fabric.js Image loading error for QR code:', err, 'Image URL:', response.qrCodeUrl);
                                        showToast('Failed to load QR code for canvas. This might be due to a broken link or CORS issues.', 5000);
                                    }
                                });
                            });

                            $('#qrCodeDisplay').append(qrImg);
                        } else {
                            showToast('Error generating QR Code.', 3000);
                        }
                    },
                    error: function (xhr, status, error) {
                        showToast('An error occurred while generating the QR Code.', 3000);
                    },
                    complete: function () {
                        $('#qrCodeLoading').addClass('hidden'); // Hide QR code loader
                    }
                });
            });
        });

        /**
         * Fetches and displays background templates.
         */
        function fetchTemplateBackgrounds() {
            var designQuery = $("#designSearchQuery").val();

            if (!designQuery) {
                designQuery = 'background';
            }

            const $templateContainer = $('#designSearchResults');
            $templateContainer.empty(); // Clear existing templates
            $('#designLoading').removeClass('hidden'); // Show loading spinner

            $.ajax({
                url: `/api/files/search?tags=${designQuery},background`,
                method: 'GET',
                success: function (data) {
                    data.forEach(file => {
                        const $templateWrapper = $('<div>', { class: 'template-wrapper relative flex m-1 group' });

                        const $templateImage = $('<img>', {
                            class: "my-template-image h-auto rounded-md shadow-md cursor-pointer",
                            src: file.url,
                            alt: file.fileName,
                             "data-type": "design-background" 
                        });

                        $templateImage.on('dragstart', function(e) {
                            e.originalEvent.dataTransfer.setData('text/plain', $(this).attr('src'));
                            e.originalEvent.dataTransfer.setData('text/x-type', 'design-background'); // This is the key to detection
                            e.originalEvent.dataTransfer.effectAllowed = 'copy';
                        });

                        const $downloadBtn = $('<button>', {
                            class: 'absolute top-2 right-8 bg-black/50 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 btn-icon',
                            html: '<i class="fas fa-download icon-sm"></i>',
                        }).on('click', function (e) {
                            e.stopPropagation();
                            const $a = $('<a>', {
                                href: file.url,
                                download: file.fileName
                            });
                            $a[0].click();
                            showToast(`Downloading ${file.fileName}...`);
                        });

                        const $deleteBtn = $('<button>', {
                            class: 'absolute top-2 right-2 bg-black/50  text-white  rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 btn-icon',
                            html: '<i class="fas fa-trash-alt icon-sm"></i>',
                        }).on('click', function (e) {
                            e.stopPropagation();
                            showCustomModal(
                                'Confirm Delete',
                                'Are you sure you want to delete this template?',
                                `<button id="confirmDeleteBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200">Delete</button>
                                 <button id="cancelDeleteBtn" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors duration-200">Cancel</button>`
                            );

                            $('#confirmDeleteBtn').on('click', function () {
                                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...'); // Show spinner on delete button
                                $(this).prop('disabled', true);

                                $.ajax({
                                    url: `/api/files/${file._id}`,
                                    method: 'DELETE',
                                    success: function () {
                                        showToast('Template deleted successfully!');
                                        fetchTemplateBackgrounds();
                                        $('#customModal').removeClass('flex').addClass('hidden');
                                    },
                                    error: function (xhr, status, error) {
                                        showToast('Failed to delete template. Please try again.', 3000);
                                        $('#customModal').removeClass('flex').addClass('hidden');
                                    },
                                    complete: function () {
                                        $('#confirmDeleteBtn').html('Delete'); // Reset button text
                                        $('#confirmDeleteBtn').prop('disabled', false);
                                    }
                                });
                            });
                            $('#cancelDeleteBtn').on('click', function () {
                                $('#customModal').removeClass('flex').addClass('hidden');
                            });
                        });

                        $templateWrapper.append($templateImage, $downloadBtn, $deleteBtn);
                        $templateContainer.append($templateWrapper);
                    });
                },
                error: function (xhr, status, error) {
                    showToast('Failed to load templates. Please try again.', 3000);
                },
                complete: function () {
                    $('#designLoading').addClass('hidden'); // Hide loading spinner
                }
            });
        }

        // Event Delegation for adding background to canvas
        $('#designSearchResults').on('click', '.my-template-image', function () {
            var imgSrc = $(this).attr('src');

            var imgElement = new Image();
            imgElement.crossOrigin = 'anonymous';
            imgElement.src = imgSrc;

            imgElement.onload = function () {
                var tempCanvas = document.createElement('canvas');
                var tempCtx = tempCanvas.getContext('2d');

                tempCanvas.width = imgElement.width;
                tempCanvas.height = imgElement.height;

                tempCtx.drawImage(imgElement, 0, 0);

                var base64Image = tempCanvas.toDataURL('image/png');

                fabric.Image.fromURL(base64Image, function (img) {
                    if (img) { // Check if image loaded successfully
                        const canvasAspect = originalCanvasWidth / originalCanvasHeight;
                        const imgAspect = img.width / img.height;

                        if (canvasAspect >= imgAspect) {
                            img.scaleToWidth(originalCanvasWidth);
                        } else {
                            img.scaleToHeight(originalCanvasHeight);
                        }

                        canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                            originX: 'left',
                            originY: 'top',
                            crossOrigin: 'anonymous',
                            scaleX: originalCanvasWidth / img.width,
                            scaleY: originalCanvasHeight / img.height
                        });
                        showToast('Background applied!');
                    } else {
                        showToast('Failed to load background image. Please check the image source.', 3000);
                    }
                }, {
                    crossOrigin: 'anonymous',
                    onError: function (img, err) {
                        console.error('Fabric.js Image loading error for background:', err, 'Image URL:', base64Image);
                        showToast('Failed to load background image for canvas. This might be due to a broken link or CORS issues.', 5000);
                    }
                });
            };

            imgElement.onerror = function () {
                showToast('Failed to load image for background. Please check the image source.', 3000);
            };
        });

        // Initial Call and Search Button Listener
        $(document).ready(function () {
            fetchTemplateBackgrounds();

            $("#designSearchButton").on("click", () => {
                fetchTemplateBackgrounds();
            });
        });

        $(document).ready(function () {
            $(document).on('keydown', function (e) {
                const activeObjects = canvas.getActiveObjects();

                if (activeObjects.length === 0) {
                    return;
                }

                let step = 5;

                // Prevent default for arrow keys to stop page scrolling
                if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                    e.preventDefault();
                }

                switch (e.key) {
                    case 'ArrowUp':
                        activeObjects.forEach(function (obj) {
                            obj.top = (obj.top || 0) - step;
                            obj.setCoords();
                        });
                        break;
                    case 'ArrowDown':
                        activeObjects.forEach(function (obj) {
                            obj.top = (obj.top || 0) + step;
                            obj.setCoords();
                        });
                        break;
                    case 'ArrowLeft':
                        activeObjects.forEach(function (obj) {
                            obj.left = (obj.left || 0) - step;
                            obj.setCoords();
                        });
                        break;
                    case 'ArrowRight':
                        activeObjects.forEach(function (obj) {
                            obj.left = (obj.left || 0) + step;
                            obj.setCoords();
                        });
                        break;
                    case 'Delete':
                    case 'Backspace': // Also handle Backspace for deletion
                        if (activeObjects.length > 0) {
                            const activeObject = activeObjects[0]; 

                            // Check if the active object is an IText or Text object AND is currently in editing mode
                            if ((activeObject.type === 'i-text' || activeObject.type === 'text') && activeObject.isEditing) {
                                
                                return; // Or break;
                            } else {
                                // If not in editing mode (just selected), proceed with full object deletion
                                activeObjects.forEach(obj => canvas.remove(obj));
                                canvas.discardActiveObject();
                                canvas.renderAll();
                                showToast('Object(s) deleted!');
                            }
                        }
                        break;
                    case 'c':
                        if ((e.ctrlKey || e.metaKey) && activeObjects.length > 0) { // Ctrl+C or Cmd+C
                            e.preventDefault();
                            activeObjects[0].clone(function (clonedObj) {
                                copiedObject = clonedObj;
                                showToast('Object copied!');
                            });
                        }
                        break;
                    case 'v':
                        if ((e.ctrlKey || e.metaKey) && copiedObject) { // Ctrl+V or Cmd+V
                            e.preventDefault();
                            copiedObject.clone(function (cloned) {
                                cloned.set({
                                    left: cloned.left + 10,
                                    top: cloned.top + 10,
                                    evented: true
                                });
                                canvas.add(cloned);
                                canvas.setActiveObject(cloned);
                                canvas.renderAll();
                                showToast('Object pasted!');
                            });
                        }
                        break;
                    case 'd':
                        if ((e.ctrlKey || e.metaKey) && activeObjects.length > 0) { // Ctrl+D or Cmd+D
                            e.preventDefault();
                            duplicateObject(activeObjects[0]); // Reuse existing duplicate function
                        }
                        break;
                    case 'z':
                        if ((e.ctrlKey || e.metaKey)) { // Ctrl+Z or Cmd+Z
                            e.preventDefault();
                            if (e.shiftKey) { // Ctrl+Shift+Z or Cmd+Shift+Z for Redo
                                redo();
                            } else { // Ctrl+Z or Cmd+Z for Undo
                                undo();
                            }
                        }
                        break;
                    case 'y':
                        if ((e.ctrlKey || e.metaKey)) { // Ctrl+Y or Cmd+Y for Redo
                            e.preventDefault();
                            redo();
                        }
                        break;
                }

                canvas.renderAll();
            });
        });

        /**
         * Fetches and displays uploaded images.
         */
        function fetchUploadedImages() {
            const $imageContainer = $('#uploadedImages');
            $imageContainer.empty(); // Clear existing images
            $('#uploadedImagesLoading').removeClass('hidden'); // Show loading spinner

            $.ajax({
                url: '/api/files/images',
                method: 'GET',
                success: function (data) {
                    data.forEach(file => {
                        // Create image wrapper
                        const $imgWrapper = $('<div>', { class: 'imageWrapper cursor-pointer w-full flex', style: 'position: relative;  ' });

                        // Create image element
                        const $imgElement = $('<img>', {
                            class: "my-single-image  w-full",
                            src: file.url,


                        });

                        // Create download button
                        const $downloadBtn = $('<button>', {
                            class: 'btn-icon-2 absolute top-2  right-1 bg-black/50 text-white rounded-md opacity-0  transition-opacity duration-200',
                            html: '<i class="fas fa-download icon-sm-2"></i>',

                        }).on('click', function () {

                            const $a = $('<a>', {
                                href: file.url,
                                download: file.fileName
                            });
                            $a[0].click(); // Programmatically trigger the download
                        });

                        // Create delete button
                        const $deleteBtn = $('<button>', {
                            class: 'btn-icon-2 absolute top-2 right-8 bg-black/50 text-white rounded-md opacity-0 transition-opacity duration-200',
                            html: '<i class="fas fa-trash-alt icon-sm-2"></i>',
                        }).on('click', function () {
                            // Assuming 'file' is defined in the scope where this button is created,
                            // containing the _id of the file to be deleted.
                            const fileId = file._id;

                            showCustomModal(
                                'Confirm Delete',
                                'Are you sure you want to delete this file?',
                                `<button id="confirmDeleteFileBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200">Delete</button>
         <button id="cancelDeleteFileBtn" class="px-4 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors duration-200">Cancel</button>`
                            );

                            $('#confirmDeleteFileBtn').on('click', function () {
                                const $confirmButton = $(this);
                                $confirmButton.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...'); // Show spinner
                                $confirmButton.prop('disabled', true); // Disable button

                                $.ajax({
                                    url: `/api/files/${fileId}`, // Use the fileId obtained earlier
                                    method: 'DELETE',
                                    success: function () {
                                        showToast('File deleted successfully!');
                                        fetchUploadedImages(); // Refresh image list after delete
                                        $('#customModal').removeClass('flex').addClass('hidden'); // Hide the modal
                                    },
                                    error: function (err) {
                                        console.error('Error deleting file:', err); // Log the error for debugging
                                        showToast('Failed to delete file. Please try again.', 3000);
                                        $('#customModal').removeClass('flex').addClass('hidden'); // Hide the modal even on error
                                    },
                                    complete: function () {
                                        $confirmButton.html('Delete'); // Reset button text
                                        $confirmButton.prop('disabled', false); // Re-enable button
                                    }
                                });
                            });

                            $('#cancelDeleteFileBtn').on('click', function () {
                                $('#customModal').removeClass('flex').addClass('hidden'); // Hide the modal on cancel
                            });
                        });

                        // Append image and buttons to wrapper
                        $imgWrapper.append($imgElement, $downloadBtn, $deleteBtn);
                        $imageContainer.append($imgWrapper);

                        // Show buttons on hover
                        $imgWrapper.hover(
                            function () { $downloadBtn.show(); $deleteBtn.show(); },
                            function () { $downloadBtn.hide(); $deleteBtn.hide(); }
                        );




                    });



                    // Add to canvas on click starts
                    $(".my-single-image").on('click', function () {

                        var imgSrc = $(this).attr('src');

                        // Create an image element to load the image
                        var imgElement = new Image();
                        imgElement.crossOrigin = 'anonymous';
                        imgElement.src = imgSrc;

                        // When the image is loaded
                        imgElement.onload = function () {
                            // Create a temporary canvas to convert the image to base64
                            var tempCanvas = document.createElement('canvas');
                            var tempCtx = tempCanvas.getContext('2d');

                            // Set the canvas size to the image size
                            tempCanvas.width = imgElement.width;
                            tempCanvas.height = imgElement.height;

                            // Draw the image on the temporary canvas
                            tempCtx.drawImage(imgElement, 0, 0);

                            // Convert to base64
                            var base64Image = tempCanvas.toDataURL('image/png');


                            fabric.Image.fromURL(base64Image, function (img) {
                                img.scaleToWidth(150);  // Adjust size if necessary
                                img.scaleToHeight(150);

                                // Position the image at the center of the canvas
                                img.set({
                                    left: canvas.width / 2 - img.getScaledWidth() / 2,
                                    top: canvas.height / 2 - img.getScaledHeight() / 2
                                });

                                // Add the image to the canvas and render
                                canvas.add(img);
                                canvas.renderAll();


                            }, {
                                crossOrigin: 'anonymous',
                                onError: function (img, err) {
                                    console.error('Fabric.js Image loading error for uploaded image (base64):', err, 'Image URL:', base64Image);
                                    showToast('Failed to load image for canvas. This might be due to a broken link or CORS issues.', 5000);
                                }
                            });
                        };
                        imgElement.onerror = function () {
                            console.error('Image element loading error for uploaded image:', imgSrc);
                            showToast('Failed to load image. Please check the image source.', 3000);
                        };
                    });
                    // Add to canvas on click ends


                },
                error: function (err) {
                    showToast('Error fetching uploaded images. Please try again.', 3000);
                },
                complete: function () {
                    $('#uploadedImagesLoading').addClass('hidden');
                }
            });
        }

        // Call the function on page load
        $(document).ready(function () {
            fetchUploadedImages();
        });



        // --- Pixabay (Photos) API Integration - Corrected Initialization ---
        var photoPage = 1; // Current page number
        var photoQuery = ''; // Current search query
        var photoIsLoading = false; // To prevent multiple simultaneous requests

        /**
         * Searches for photos using the Pixabay API.
        
         */
        function searchPixabay(query, page) {
            if (photoIsLoading) return;
            photoIsLoading = true;

            $('#photoLoading').removeClass('hidden'); // Show loading spinner

            $.ajax({
                url: `/api/pixabay?q=${query}&type=photo&per_page=20&hits=18&page=${page}`,
                method: 'GET',
                success: function (data) {
                    displayPhotoResults(data.hits); // Use displayPhotoResults
                    photoIsLoading = false;
                    $('#photoLoading').addClass('hidden'); // Hide loading spinner
                },
                error: function (xhr, status, error) {
                    photoIsLoading = false;
                    $('#photoLoading').addClass('hidden'); // Hide loading spinner
                    showToast('Error fetching photos. Please try again.', 3000);
                }
            });
        }
        // Updated addIconToCanvas function to center icons on click


        /**
         * Displays photo search results.
         */
        function displayPhotoResults(images) {
            var resultsContainer = $('#photoSearchResults');
            if (photoPage === 1) { // Clear only for the first page of results
                resultsContainer.empty();
            }
            images.forEach(function (image) {
                var imgElement = $('<img>')
                    .attr('src', image.previewURL)
                    .attr('class', 'photo-item cursor-pointer rounded-md shadow-md hover:shadow-lg transition-shadow duration-200')
                    .attr('data-full', image.largeImageURL)
                    .attr('alt', 'Pixabay Image')
                    .attr('crossorigin', 'anonymous')
                    .css({ 'width': '100%', 'height': 'auto' });

                imgElement.on('click', function () {
                    addImageToCanvas($(this).data('full'));
                });

                resultsContainer.append($('<div class="w-full flex"></div>').append(imgElement));
            });
        }

        // Search button click event for Photos
        $('#photoSearchButton').on('click', function () {
            photoQuery = $('#photosSearchQuery').val().trim();
            photoPage = 1;
            $('#photoSearchResults').empty();
            if (photoQuery) {
                searchPixabay(photoQuery, photoPage);
            } else {
                showToast('Please enter a search term for photos.', 2000);
            }
        });

        // Infinite scroll event for photos
        $('#sidebar-content-area').on('scroll', function () {
            const $this = $(this);
            if ($this.scrollTop() + $this.innerHeight() >= $this[0].scrollHeight - 50) {
                if (!photoIsLoading && photoQuery && $('#photos-content').is(':visible')) { // Use photoIsLoading
                    photoPage++;
                    searchPixabay(photoQuery, photoPage);
                }
            }
        });

        // Initialize by using the current value in the search box, if present
        $(document).ready(function () {
            if ($('#photosSearchQuery').val().trim()) { // Corrected ID
                photoQuery = $('#photosSearchQuery').val().trim();
                searchPixabay(photoQuery, photoPage); // Perform search on page load if input has a value
            }
        });
        // --- End Pixabay (Photos) API Integration ---


        // --- Variable Script Starts ---

        let customVariables = [];

        // Renders a single variable into the sidebar list.
        function renderComponent(variable) {
            const item = $(`<div class="variable-draggable-item bg-zinc-600 hover:bg-zinc-700 text-white p-2 rounded-md text-sm cursor-grab">${variable.name}</div>`);
            item.data('tag', variable.tag);
            item.data('variable-tag', variable.tag);
            item.data('variable-name', variable.name);
            item.attr('draggable', 'true');
            item.on('click', function () {
                addVariableToCanvas($(this).data('tag'), variable.name);
            });
            $('#components-list').append(item);
        }

        // Fetches custom variables from the backend API and renders them in the sidebar.
        function fetchVariables() {
            const $componentsList = $('#components-list');
            $componentsList.empty();
            $('#variablesLoading').removeClass('hidden');

            // Define default contact schema variables
            const defaultContactVariables = [
                { name: "First Name", tag: "{{firstName}}" },
                { name: "Last Name", tag: "{{lastName}}" },
                { name: "Full Name", tag: "{{fullName}}" },
                { name: "Email", tag: "{{email}}" },
                { name: "Phone", tag: "{{phone}}" },
                { name: "Company", tag: "{{company}}" },
                { name: "Address", tag: "{{address}}" },
                { name: "City", tag: "{{city}}" },
                { name: "State", tag: "{{state}}" },
                { name: "Zip Code", tag: "{{zipCode}}" },
                { name: "Country", tag: "{{country}}" },
                { name: "Date", tag: "{{date}}" },
                { name: "Certificate ID", tag: "{{certificateId}}" },
                { name: "Course Name", tag: "{{courseName}}" },
                { name: "Completion Date", tag: "{{completionDate}}" },
                { name: "Issue Date", tag: "{{issueDate}}" },
                { name: "Expiration Date", tag: "{{expirationDate}}" },
                { name: "Score", tag: "{{score}}" },
                { name: "Grade", tag: "{{grade}}" },
                { name: "Instructor Name", tag: "{{instructorName}}" },
                { name: "Event Name", tag: "{{eventName}}" },
                { name: "Event Date", tag: "{{eventDate}}" },
                { name: "Award Name", tag: "{{awardName}}" },
                { name: "Awarding Body", tag: "{{awardingBody}}" },
                { name: "Signature 1 Name", tag: "{{signature1Name}}" },
                { name: "Signature 1 Title", tag: "{{signature1Title}}" },
                { name: "Signature 2 Name", tag: "{{signature2Name}}" },
                { name: "Signature 2 Title", tag: "{{signature2Title}}" },
                { name: "Signature 3 Name", tag: "{{signature3Name}}" },
                { name: "Signature 3 Title", tag: "{{signature3Title}}" },
                { name: "QR Code URL", tag: "{{qrCodeUrl}}" }
            ];

            // Render default variables first
            defaultContactVariables.forEach(variable => {
                renderComponent(variable);
            });

            // Then fetch custom variables
            $.ajax({
                url: '/api/custom-variable/get',
                method: 'GET',
                success: function (response) {


                    if (response && response.contactCustomVariable && typeof response.contactCustomVariable === 'object') {
                        const fetchedVariablesArray = Object.values(response.contactCustomVariable);
                        customVariables = fetchedVariablesArray;

                        if (customVariables.length > 0) {
                            customVariables.forEach(variable => {
                                if (!variable.tag) {
                                    variable.tag = `{{${variable.name.replace(/\s+/g, '_').toLowerCase()}}}`;
                                }
                                renderComponent(variable);
                            });

                        } else {
                            // If no custom variables, the default ones will still be shown
                            console.warn("No custom variables found for this user.");
                        }
                    } else if (response && response.error === "No Custom Variables Found") {
                        console.warn("No custom variables found for this user, as per backend response.");
                    } else {
                        console.warn("API response for custom variables was not in the expected format:", response);
                        showToast('Received unexpected data format for variables. Please check console.', 3000);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching custom variables:', error, status, xhr);
                    showToast('Error fetching custom variables. Please try again.', 3000);
                    $componentsList.append('<p class="text-red-400 text-center py-4">Failed to load custom variables. Server error.</p>');
                },
                complete: function () {
                    $('#variablesLoading').addClass('hidden');

                    $componentsList.append(`
                
            `);
                }
            });
        }

        $(document).ready(function () {
            fetchVariables();

            // Event delegation for dragstart on variable items
            // This ensures that dragstart works for elements added dynamically
            $('#components-list').on('dragstart', '.variable-draggable-item', function (e) {
                const originalEvent = e.originalEvent;
                const variableTag = $(this).data('variable-tag');
                const variableName = $(this).text();

                originalEvent.dataTransfer.setData('text/plain', variableTag);
                originalEvent.dataTransfer.setData('text/x-type', 'variable');
                originalEvent.dataTransfer.setData('text/x-variable-tag', variableTag);
                originalEvent.dataTransfer.setData('text/x-variable-name', variableName);
                originalEvent.dataTransfer.effectAllowed = 'copy'; // Explicitly allow copy operation
            });
        });

        // Adds a variable to the canvas at specified coordinates.
        function addVariableToCanvasAtCoords(tag, name, x, y, isCustom = false) {

            const textObj = new fabric.IText(tag, {
                left: x,
                top: y,
                fill: '#000',
                fontSize: 20,
                originX: 'center',
                originY: 'center',
                variableTag: tag,
                variableName: name,
                isCustomVariable: isCustom,
                fontFamily: 'Roboto',
                cornerColor: '#1d89ff',
                transparentCorners: false,
                hasControls: true,
                hasBorders: true,
            });
            canvas.add(textObj);
            canvas.setActiveObject(textObj);
            canvas.renderAll();
            showToast(`Variable "${name}" added to canvas!`);
        }

        // Adds a variable to the center of the canvas.
        function addVariableToCanvas(tag, name, isCustom = false) {
            const textObj = new fabric.IText(tag, {
                left: canvas.width / 2,
                top: canvas.height / 2,
                fill: '#000',
                fontSize: 20,
                originX: 'center',
                originY: 'center',
                variableTag: tag,
                variableName: name,
                isCustomVariable: isCustom,
                fontFamily: 'Roboto',
                cornerColor: '#1d89ff',
                transparentCorners: false,
                hasControls: true,
                hasBorders: true,
            });
            canvas.add(textObj);
            canvas.setActiveObject(textObj);
            canvas.renderAll();
            showToast(`Variable "${name}" added to canvas!`);
        }

        // Retrieves all variables currently on the canvas.
        function getCanvasVariables() {
            return canvas.getObjects()
                .filter(obj => obj.variableTag)
                .map(obj => ({
                    tag: obj.variableTag,
                    name: obj.variableName,
                    isCustom: obj.isCustomVariable || false
                }));
        }

        // Opens the modal for adding a custom variable.
        $('#add-variable-btn').on('click', function () {
            $('#variable-name').val('');
            $('#variable-type').val('text');
            $('#variable-default').val('');
            $('#variableModal').removeClass('hidden').addClass('flex');
        });

        // Closes the custom variable modal.
        $('#closeVariableModal').on('click', function () {
            $('#variableModal').removeClass('flex').addClass('hidden');
        });

        // Applies font family styles to dropdown options.
        $('#fontFamily option').each(function () {
            const fontName = $(this).val();
            $(this).css('font-family', fontName);
        });

        // Saves a custom variable from the modal.
        $('#save-variable-btn').on('click', function () {
            const name = $('#variable-name').val().trim();
            const type = $('#variable-type').val();
            const defaultValue = $('#variable-default').val().trim();

            if (name) {
                const tag = `{{${name.replace(/\s+/g, '_').toLowerCase()}}}`;
                const newCustomVar = { name: name, type: type, defaultValue: defaultValue, tag: tag };
                customVariables.push(newCustomVar);
                renderComponent(newCustomVar);
                addVariableToCanvas(tag, name, true);
                $('#variableModal').removeClass('flex').addClass('hidden');
                showToast(`Custom variable "${name}" added!`);
            } else {
                showToast('Please provide a variable name.', 2000);
            }
        });
        // --- Variable Script Ends ---

        // Manual Ai Template Generator

        const automaticTabBtn = $('#automatic-tab-btn');
        const manualTabBtn = $('#manual-tab-btn');
        const automaticTabContent = $('#automatic-tab-content');
        const manualTabContent = $('#manual-tab-content');
        const generateButton = $('#generate-template-btn');
        const generatedDesignPreview = $('#ai-generated-design-preview');
        const generatedDesignImg = $('#generated-design-img');
        const generatedDesignMessage = $('#generated-design-message');

        // Manual tab specific elements
        const manualPromptInput = $('#manual-prompt-input');
        const manualUploadArea = $('#manual-upload-area');
        const manualFileUploadInput = $('#manual-file-upload-input');
        const manualBrowseFilesButton = $('#manual-browse-files-button');
        const manualImagePreviewContainer = $('#manual-image-preview-container');
        const manualImagePreview = $('#manual-image-preview');
        const manualImageFilename = $('#manual-image-filename');
        const manualImageResolution = $('#manual-image-resolution');
        const manualRemoveImageBtn = $('#manual-remove-image-btn');
        const manualBrandColorPicker = $('#manual-brand-color-picker');
        const manualSmartModeToggle = $('#manual-smart-mode-toggle');

        let uploadedFileUrl = null;
        let uploadedFileResolution = null;
        let manualUploadedFile = null; // Stores the actual File object for FormData

        // Initialize Spectrum color picker for manual mode
        manualBrandColorPicker.spectrum({
            showInput: true,
            preferredFormat: "hex",
            showPalette: true,
            showButtons: false,
            palette: [
                ["#000", "#444", "#666", "#999", "#ccc", "#eee", "#f3f3f3", "#fff"],
                ["#f00", "#f90", "#ff0", "#0f0", "#0ff", "#00f", "#90f", "#f0f"],
                ["#ea9999", "#f9cb9c", "#ffe599", "#b6d7a8", "#a2c4c9", "#9fc5e8", "#b4a7d6", "#d5a6bd"],
                ["#e06666", "#f6b26b", "#ffd966", "#93c47d", "#76a5af", "#6fa8dc", "#8e7cc3", "#c27ba0"],
                ["#cc0000", "#e69138", "#f1c232", "#6aa84f", "#45818e", "#3d85c6", "#674ea7", "#a64d79"],
                ["#990000", "#b45f06", "#bf9000", "#38761d", "#134f5c", "#0b5394", "#351c75", "#741b47"],
                ["#660000", "#783f04", "#7f6000", "#274e13", "#0c343d", "#073763", "#20124d", "#4c1130"]
            ]
        });

        // Function to switch tabs
        function switchAITab(tabId) {
            // Deactivate all tab buttons and hide all tab content
            automaticTabBtn.removeClass('border-b-2 border-blue-500 text-white').addClass('text-gray-400');
            manualTabBtn.removeClass('border-b-2 border-blue-500 text-white').addClass('text-gray-400');
            $('.tab-content').addClass('hidden');

            // Activate the selected tab button and show its content
            if (tabId === 'automatic') {
                automaticTabBtn.addClass('border-b-2 border-blue-500 text-white').removeClass('text-gray-400');
                automaticTabContent.removeClass('hidden');
            } else if (tabId === 'manual') {
                manualTabBtn.addClass('border-b-2 border-blue-500 text-white').removeClass('text-gray-400');
                manualTabContent.removeClass('hidden');
            }
            // Hide the generated design preview when switching tabs
            generatedDesignPreview.addClass('hidden');
            generatedDesignImg.attr('src', '');
            generatedDesignMessage.addClass('hidden');
        }

        // Event listeners for tab buttons
        automaticTabBtn.on('click', function () {
            switchAITab('automatic');
        });

        manualTabBtn.on('click', function () {
            switchAITab('manual');
        });

        // Set 'Automatic' tab as default on page load
        switchAITab('automatic');

        // File upload logic for Manual tab
        manualBrowseFilesButton.on('click', function () {
            manualFileUploadInput.click();
        });

        manualFileUploadInput.on('change', function (e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleManualFileUpload(files[0]);
            }
        });

        // Drag and drop for manual upload area
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            manualUploadArea.on(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            manualUploadArea.on(eventName, function () { $(this).addClass('border-blue-500'); }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            manualUploadArea.on(eventName, function () { $(this).removeClass('border-blue-500'); }, false);
        });

        manualUploadArea.on('drop', function (e) {
            const dt = e.originalEvent.dataTransfer;
            const files = dt.files;
            if (files.length > 0) {
                handleManualFileUpload(files[0]);
            }
        });

        manualRemoveImageBtn.on('click', function () {
            uploadedFileUrl = null;
            uploadedFileResolution = null;
            manualUploadedFile = null;
            manualImagePreview.attr('src', '');
            manualImageFilename.text('');
            manualImageResolution.text('');
            manualImagePreviewContainer.addClass('hidden');
            manualUploadArea.removeClass('hidden');
            showToast('Reference image removed.');
        });

        //Automatic Ai Template Generation

        // --- Selectors for Automatic Tab Inputs ---
        const $certificateTypeDropdown = $('#certificate-type-dropdown');
        const $certificatePurposeInput = $('#certificate-purpose-input');
        const $eventNameInput = $('#event-name-input');
        const $logoImageUrlInput = $('#logo-image-url-input');
        const $numSignaturesInput = $('#num-signatures-input');
        const $signaturesContainer = $('#signatures-container');
        const $stylePills = $('.ai-style-pill');
        const $brandColorPicker = $('#brand-color-picker');
        const $smartModeToggle = $('#smart-mode-toggle');
        const $positionPreferenceRadios = $('input[name="position-preference"]');

        // --- Selectors for Automatic Generation Buttons & Feedback ---
        const $generateAutomaticTemplateBtn = $('#generate-automatic-template-btn');
        const $aiLoadingSpinnerAutomatic = $('#ai-loading-spinner-automatic');
        const $regenerateAutomaticTemplateBtn = $('#regenerate-automatic-template-btn');
        const $reviseAutomaticTemplateBtn = $('#revise-automatic-template-btn');

        // --- Shared Design Preview Elements ---
        const $generatedDesignPreview = $('#ai-generated-design-preview');
        const $generatedDesignImg = $('#generated-design-img'); 
        const $generatedDesignMessage = $('#generated-design-message');

        let selectedTheme = 'professional'; // Default theme

        // Define a global constant for max uploaded image dimension if not already defined
        const MAX_UPLOADED_IMAGE_DIMENSION = 500; // Example value, adjust as needed

        // Initialize selection for the default theme ('professional') on page load
        $('.ai-style-pill[data-style="professional"]').addClass('border-blue-500 border-2');

        // Handle theme selection clicks
        $stylePills.on('click', function () {
            $stylePills.removeClass('border-blue-500 border-2');
            $(this).addClass('border-blue-500 border-2');
            selectedTheme = $(this).data('style');
            console.log("Selected theme:", selectedTheme); // Debug: Log selected theme
        });

        // Generates dynamic signature input fields
        function generateSignatureInputs(count) {
            $signaturesContainer.empty();
            for (let i = 0; i < count; i++) {
                const signatureHtml = `
        <div class="mb-4 p-3 rounded-md bg-zinc-800">
            <h4 class="text-md font-semibold text-white mb-2">Signature ${i + 1}</h4>
            <input type="url" class="signature-image-url w-full p-2 mb-2 rounded-md bg-zinc-700 text-white border border-gray-600" placeholder="Signature Image URL (e.g., https://...)">
            <input type="text" class="signature-name w-full p-2 mb-2 rounded-md bg-zinc-700 text-white border border-gray-600" placeholder="Signatory Name">
            <input type="text" class="signature-designation w-full p-2 rounded-md bg-zinc-700 text-white border border-gray-600" placeholder="Signatory Designation">
        </div>
    `;
                $signaturesContainer.append(signatureHtml);
            }
            console.log(`Generated ${count} signature input fields.`); // Debug: Log signature field generation
        }

        // Initial generation of signature inputs based on the default value
        generateSignatureInputs(parseInt($numSignaturesInput.val()));

        // Listen for changes in the number of signatures input
        $numSignaturesInput.on('change', function () {
            const count = parseInt($(this).val());
            if (!isNaN(count) && count >= 0 && count <= 5) {
                generateSignatureInputs(count);
            } else {
                $(this).val(1); // Reset to 1 if invalid
                generateSignatureInputs(1);
                console.warn("Invalid number of signatures entered. Reset to 1."); // Debug: Warn on invalid input
            }
        });

        // Handles the generation of the automatic template
        $generateAutomaticTemplateBtn.on('click', async function () {
            // Gather all input values from the Automatic tab
            const certificateType = $certificateTypeDropdown.val();
            const purpose = $certificatePurposeInput.val();
            const eventName = $eventNameInput.val();
            const logoImage = $logoImageUrlInput.val();
            const numSignatures = parseInt($numSignaturesInput.val());
            const brandColor = $brandColorPicker.val();
            const smartMode = $smartModeToggle.prop('checked');
            const positionPreference = $positionPreferenceRadios.filter(':checked').val();

            // Collect signature details
            const signatures = [];
            $('.signature-image-url').each(function (index) {
                const sigImage = $(this).val();
                const sigName = $('.signature-name').eq(index).val();
                const sigDesignation = $('.signature-designation').eq(index).val();
                signatures.push({
                    image: sigImage,
                    name: sigName,
                    designation: sigDesignation
                });
            });

            // Construct the payload for the API request
            const payload = {
                certificate_type: certificateType,
                purpose: purpose,
                event_name: eventName,
                theme: selectedTheme,
                logo_image: logoImage,
                num_signatures: numSignatures,
                signatures: signatures,
                brand_color: brandColor,
                smart_mode: smartMode,
                position_preference: positionPreference
            };

            console.log("Sending payload to backend:", payload);

            // UI Feedback: Show loading spinner and hide previous results/buttons
            $aiLoadingSpinnerAutomatic.removeClass('hidden');
            $generatedDesignPreview.addClass('hidden');
            $generatedDesignMessage.addClass('hidden').removeClass('text-red-500 text-green-400');
            $regenerateAutomaticTemplateBtn.addClass('hidden');
            $reviseAutomaticTemplateBtn.addClass('hidden');

            try {
                // Make the AJAX call to your backend API
                const response = await $.ajax({
                    url: 'api/template-generator',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(payload)
                });

                console.log("Received response from backend:", response);

                // On success:
                $aiLoadingSpinnerAutomatic.addClass('hidden');
                $generatedDesignPreview.removeClass('hidden');
                $generatedDesignMessage.removeClass('hidden').text("Template generated successfully!").addClass('text-green-400');

                // Clear existing canvas content
                canvas.clear();

                // Load the background image first
                if (response.background && response.background.source) {
                    fabric.Image.fromURL(response.background.source, function (img) {
                        if (img) {
                            // Scale background to fit canvas
                            const scaleX = canvas.width / img.width;
                            const scaleY = canvas.height / img.height;
                            const scale = Math.max(scaleX, scaleY); // Use max to ensure it covers the canvas

                            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                scaleX: scale,
                                scaleY: scale,
                                originX: 'left',
                                originY: 'top',
                                left: 0,
                                top: 0,
                                crossOrigin: 'anonymous'
                            });
                        } else {
                            console.warn("Failed to load background image from URL:", response.background.source);
                        }
                    }, { crossOrigin: 'anonymous' });
                }

                // Load the rest of the Fabric.js JSON objects (logo, content, signatures)
                if (response.objects && typeof fabric !== 'undefined') {
                    canvas.loadFromJSON({ objects: response.objects }, function () {
                        canvas.renderAll();
                        console.log("Fabric.js canvas objects loaded from JSON successfully.");
                    });
                } else {
                    console.warn("No valid Fabric.js objects found in response. Canvas might be empty.");
                }

                $regenerateAutomaticTemplateBtn.removeClass('hidden'); 
                $reviseAutomaticTemplateBtn.removeClass('hidden');    

            } catch (xhr) {
                // On error:
                $aiLoadingSpinnerAutomatic.addClass('hidden'); 
                $generatedDesignPreview.addClass('hidden'); 
                const errorMessage = xhr.responseText ? JSON.parse(xhr.responseText).error : 'Failed to generate automatic template. Please try again.';
                $generatedDesignMessage.removeClass('hidden').text(`Error: ${errorMessage}`).addClass('text-red-500');
                console.error("AJAX Error:", xhr.status, xhr.responseText);
                $regenerateAutomaticTemplateBtn.removeClass('hidden');
            }
        });

        // Regenerate and Revise Logic for Automatic Tab
        $regenerateAutomaticTemplateBtn.on('click', function () {
            console.log("Regenerating automatic template...");
            $generateAutomaticTemplateBtn.click();
        });

        $reviseAutomaticTemplateBtn.on('click', function () {
            alert('Revise Automatic Design functionality would typically involve adjusting prompt/inputs and re-generating.');
            console.log("Revise automatic design initiated (placeholder)."); // Debug: Log revise action
        });


    </script>
</body>

</html>