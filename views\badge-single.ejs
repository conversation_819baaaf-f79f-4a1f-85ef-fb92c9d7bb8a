<%- contentFor('HeaderCss') %> <%-include("partials/title-meta",{"title":"Single Badge" }) %>
<!-- DataTables CSS -->
<link
  href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css"
  rel="stylesheet"
/>
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<%- contentFor('body') %> <%-include("partials/page-title", {"title": "Single Badge" , "pagetitle": "All" }) %>

<div class="row align-items-center">
  <div class="col-md-6">
    <div class="mb-3">
      <h5 class="card-title">
        Total :<span class="text-muted fw-normal ms-2"
          >(<span id="totalContactCount">0</span>)</span
        >
      </h5>
    </div>
  </div>

  <div class="col-md-6">
    <div
      class="d-flex flex-wrap align-items-center justify-content-end gap-2 mb-3"
    >
      <div>
        <button
          type="button"
          class="btn btn-light"
          data-bs-toggle="modal"
          data-bs-target="#contactModal"
        >
          <i class="bx bx-plus me-1"></i> Add New Contact
        </button>
      </div>

      <div>
        <button
          type="button"
          class="btn btn-light"
          data-bs-toggle="modal"
          data-bs-target="#bigContactModal"
        >
          <i class="bx bx-plus me-1"></i> Add from Contacts
        </button>
      </div>
    </div>
  </div>
</div>

<hr class="py-1" />

<div class="mt-4">
  <div class="table-responsive">
    <table
      id="single-badge-list"
      class="table table-striped"
      style="width: 100%"
    >
      <thead>
        <tr>
          <th scope="col">No</th>
          <th scope="col">Full Name</th>
          <th scope="col">Business Email</th>
          <th scope="col">First Appeared</th>
          <th scope="col">Updated On</th>
          <th scope="col">Certificates</th>
          <th scope="col">Badges</th>
          <th scope="col">Actions</th>
        </tr>
      </thead>
      <tbody id="badgeTableBody">
        <!-- Contacts will be appended here -->
      </tbody>
    </table>
  </div>
</div>

<!-- Modal starts for Adding Contact to Contact List -->
<div
  class="modal fade"
  id="contactModal"
  tabindex="-1"
  aria-labelledby="contactModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contactModalLabel">
          Add Contact to Contact List
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="fullName" class="form-label">Full Name</label>
          <input type="text" class="form-control" id="fullName" required />
          <label for="businessEmail" class="form-label">Business Email</label>
          <input
            type="email"
            class="form-control"
            id="businessEmail"
            required
          />
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="saveContact">
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal ends for Adding Contact to Contact List -->

<!-- Big Modal for Adding Existing Contact -->
<div
  class="modal fade"
  id="bigContactModal"
  tabindex="-1"
  aria-labelledby="bigContactModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <!-- Search Email Input -->
        <div class="mb-3">
          <label for="email" class="form-label" title="Type Email or Domain"
            >Search</label
          >
          <input
            type="email"
            class="form-control"
            id="email"
            placeholder="Enter name or email"
            required
          />
          <div id="searchResults" class="list-group mt-2"></div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Big Modal ends for Adding Existing Contact -->

<%- contentFor('FooterJs') %>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<script>
   let nameSpaceCode = "";
  $(document).ready(function () {
    const url = window.location.href;
    const badgeId = url.substring(url.lastIndexOf("/") + 1);

      $.ajax({
    url: "/api/users/profile",
    method: "GET",
    success: function (response) {
      if (response && response.businessId) {
        fetchNameSpaceFromBusinessData(response.businessId);
      }
    },
    error: function (error) {
      console.log(error);
    },
  });
  function fetchNameSpaceFromBusinessData(businessId) {
    $.ajax({
      url: "/api/business/" + businessId,
      method: "GET",
      success: function (response) {
        if (response && response.nameSpaceCode) {
          nameSpaceCode = response.nameSpaceCode;
           loadContacts();
          
        }
      },
      error: function (error) {
        console.log(error);
      },
    });
  }

    function loadContacts() {
      $("#single-badge-list").DataTable({
        responsive: true,
        searching: true,
        ordering: true,
        pageLength: 50,
        destroy: true,
        lengthMenu: [
          [10, 50, 100, 500, 1000],
          [10, 50, 100, 500, 1000],
        ],
        columnDefs: [
          { orderable: false, targets: 0 }, // Disable sorting on the first column (No)
          { orderable: false, targets: -1 }, // Disable sorting on the last column (Actions)
        ],

        ajax: {
          url: `/api/badges/${badgeId}`,
          method: "GET",
          dataSrc: function (json) {
            $("#page-heading").text(json.title || "untitled");
            $("#totalContactCount").text(json.contactId.length);

            return json.contactId || []; // Return the contactId array
          },
        },
        columns: [
          {
            data: null,
            render: function (data, type, row, meta) {
              return meta.row + meta.settings._iDisplayStart + 1;
            },
          },
          { data: "fullName" },
          { data: "businessEmail" },
          {
            data: "createdAt",
            render: function (data) {
              return new Date(data).toLocaleDateString();
            },
          },
          {
            data: "updatedAt",
            render: function (data) {
              return new Date(data).toLocaleDateString();
            },
          },
          {
            data: "certificates",
            render: function (data) {
              return data.length || 0;
            },
          }, // Handle empty data
          {
            data: "badges",
            render: function (data) {
              return data.length || 0;
            },
          },
          {
            data: null,
            render: function (data, type, row) {
              return `<div class="d-flex align-items-center gap-1">
      <a href="/${nameSpaceCode}/profile/${data._id}" target="_blank" class="btn btn-sm btn-primary">View</a>
      <button class="btn btn-sm btn-primary remove-contact mt-1" data-contact-id="${data._id}">
        Remove
      </button>
    </div>`;
            },
          },
        ],
      });
    }

   

    $(document).on("click", ".remove-contact", function () {
      if (confirm("Are you sure you want to remove this contact?")) {
        const contactId = $(this).data("contact-id");
        $.ajax({
          url: `/api/badges/${badgeId}/${contactId}`,
          method: "PUT",
          success: function (data) {
            console.log(data);
            showToast(
              data.message || "Contact removed successfully from the list.",
              "primary"
            );
            loadContacts();
          },
          error: function (err) {
            console.log(err);
          },
        });
      }
    });

    $(document).on("click", "#saveContact", function () {
      const fullName = $("#fullName").val();
      const businessEmail = $("#businessEmail").val();

      if (!fullName || !businessEmail) {
        showToast("Please enter the details", "danger");
        return;
      }

      $.ajax({
        url: `/api/badges/${badgeId}`,
        method: "POST",
        data: {
          fullName,
          businessEmail,
        },
        success: function (data) {
          console.log(data);
          showToast(
            data.message || "Contact added successfully to the list.",
            "success"
          );
          $("#contactModal").modal("hide");
          $("#fullName").val("");
          $("#businessEmail").val("");
          loadContacts();
        },
        error: function (err) {
          console.log(err);
          showToast(
            err.responseJSON.message || "Something went wrong!",
            "danger"
          );
        },
      });
    });

    $("#email").on("input", function () {
      const value = $(this).val();
      if (value.length > 0) {
        $.ajax({
          url: `/api/contacts/search?email=${value}&name=${value}`,
          method: "GET",
          success: function (data) {
            $("#searchResults").empty();
            if (data && data.contacts.length > 0) {
              data.contacts.forEach((contact) => {
                $("#searchResults").append(`
                    <span class="list-group-item list-group-item-action" data-contact='${JSON.stringify(
                      contact
                    )}'>
                      <i class="add-search-contact fa fa-plus pr-3"></i> ${
                        contact.fullName
                      } - ${contact.businessEmail}
                    </span>
                  `);
              });
            } else {
              $("#searchResults").append(
                '<div class="list-group-item">No contact found</div>'
              );

              // Add a border to the end of the "Add" item
              $("#searchResults").append(`
                  <div class="list-group-item" style="border: 1px solid grey; margin-top:5px; border-radius: 0.25rem; text-align: center; cursor: pointer;">
                    <i class="fa fa-plus pr-2"></i> Add New Contact
                  </div>
                `);
            }
          },
          error: function (err) {
            console.error("Error fetching contacts:", err);
          },
        });
      } else {
        $("#searchResults").empty();
      }
    });

    // Handle search result click
    $(document).on("click", ".list-group-item", function (e) {
      const contact = $(this).data("contact");

      // first check if the contact already exists
       console.log(contact,'contact');
      $.ajax({
        url: `/api/badges/contact/exists/${contact._id}/${badgeId}`,
        method: "GET",

        success: function (data) {
          console.log(data);
          if (data.exists) {
            showToast("Contact already exists", "danger");
            return;
          }
          $.ajax({
            url: `/api/badges/${badgeId}`,
            method: "POST",
            data: {
              contactId: contact._id,
            },
            success: function (data) {
              showToast(
                data.message || "Contact added successfully to the list.",
                "primary"
              );
              $("#bigContactModal").modal("hide");
              $("#email").val("");
              $("#searchResults").empty();
              loadContacts();
            },
            error: function (err) {
              console.log(err);
              showToast(
                err.responseJSON.message || "Something went wrong!",
                "danger"
              );
            },
          });
        },
        error: function (err) {
          console.log(err);
          showToast(
            err.responseJSON.message || "Something went wrong!",
            "danger"
          );
        },
      });
    });
  });
</script>
