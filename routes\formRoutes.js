const express = require("express");
const router = express.Router();
const formController = require("../controllers/formController");
const { checkPermissions } = require("../middleware/middleware");

// CRUD Routes
router
  .route("/")
  .post(checkPermissions("Form", ["create"]), formController.createForm)
  .get(checkPermissions("Form", ["read"]), formController.getAllForms);

router
  .route("/:id")
  .get(checkPermissions("Form", ["read"]), formController.getFormById)
  .put(checkPermissions("Form", ["update"]), formController.updateForm)
  .delete(checkPermissions("Form", ["delete"]), formController.deleteForm);

// Enable/Disable Form
router.patch(
  "/:id/enable-disable",
  checkPermissions("Form", ["update"]),
  formController.enableDisableForm
);

// Toggle Default
router.patch(
  "/:id/toggle-default",
  checkPermissions("Form", ["update"]),
  formController.toggleDefaultForm
);


router.post("/public/submit", formController.submitForm);

module.exports = router;
