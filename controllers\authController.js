const passport = require("passport");
const crypto = require("crypto");
const bcrypt = require("bcryptjs");
const User = require("../models/User");
const jwt = require("jsonwebtoken");
const sendEmail = require("../utils/email");
const sanitizer = require("sanitizer");
const { createLog } = require("../controllers/logController");
// Check if app is running
const appstatus = async (req, res) => {
  return res.status(200).send("App is running");
};

// Registration
const signup = async (req, res) => {
  try {
    // Sanitize inputs
    const sanitizedInputs = {
      firstName: sanitizer.sanitize(req.body.firstName || ""),
      lastName: sanitizer.sanitize(req.body.lastName || ""),
      email: sanitizer.sanitize(req.body.email || ""),
      password: sanitizer.sanitize(req.body.password || ""),
      displayName: sanitizer.sanitize(req.body.displayName || "Mixer"),
    };

    // Check if user exists
    const existsUser = await User.findOne({ email: sanitizedInputs.email });

    if (existsUser) {
      req.flash("error", "Account already exists with email.");
      return res.redirect("/login");
    }

    // Generate verification token
    const verificationToken = jwt.sign(
      { email: sanitizedInputs.email },
      process.env.SECRET,
      {
        expiresIn: "1d",
      }
    );

    // Create user
    const newUser = await User.create({
      ...sanitizedInputs,
      verificationToken,
    });

    req.session.useremail = newUser.email;
    // Send welcome email
    const subject = "Welcome to MixCertificate";
    const message = `Hey ${newUser.firstName} ${newUser.lastName},<br>
            Welcome to MixCertificate. <br>
            Please onboard your business at <a href="https://mixcertificate.mixcommerce.co/onboard/">this link</a>. <br>
            Feel free to reach out to our support for any <NAME_EMAIL>.`;
    await sendEmail({
      email: newUser.email,
      subject,
      message,
    });
    // Send verification email
    const verificationLink = `${req.protocol}://${req.get(
      "host"
    )}/auth-email-verification?token=${verificationToken}`;
    await sendEmail({
      email: newUser.email,
      subject: "Verify Your Email",
      message: `<h3>Click the link below to verify your email:</h3>
             <a href="${verificationLink}">${verificationLink}</a>`,
    });

    return res.redirect(`/auth-email-verification?token=${verificationToken}`);
  } catch (err) {
    req.flash("error", `Registration Failed. Error: ${err.message}`);
    return res.redirect("/register");
  }
};
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) return res.redirect("/login");
    const decoded = jwt.verify(token, process.env.SECRET);
    const user = await User.findOne({ email: decoded.email });

    if (!user) {
      req.flash("error", "Invalid token or email.");
      return res.redirect("/login");
    }
    user.isVerified = true;
    user.verificationToken = null;
    await user.save();
    req.flash("message", "Email verified successfully.");
    return res.redirect("/login");
  } catch (error) {
    req.flash("error", "Email verification failed.");
    return res.redirect("/login");
  }
};

const resendEmail = async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) return res.redirect("/login");
    const decoded = jwt.verify(token, process.env.SECRET);
    const user = await User.findOne({ email: decoded.email });

    if (!user) {
      req.flash("error", "Invalid token or email.");
      return res.redirect("/login");
    }
    const verificationToken = jwt.sign(
      { email: user.email },
      process.env.SECRET,
      {
        expiresIn: "1d",
      }
    );
    user.verificationToken = verificationToken;
    user.isVerified = false;
    await user.save();
    // Send verification email
    const verificationLink = `${req.protocol}://${req.get(
      "host"
    )}/auth-email-verification?token=${verificationToken}`;
    await sendEmail({
      email: user.email,
      subject: "Verify Your Email",
      message: `<h3>Click the link below to verify your email:</h3>
               <a href="${verificationLink}">${verificationLink}</a>`,
    });
    req.flash("message", "Email sended again");
    return res.redirect(`/auth-email-verification?token=${verificationToken}`);
  } catch (error) {
    req.flash("error", "Email verification failed.");
    return res.redirect("/login");
  }
};
// Render login page
const login = (req, res) => {
  if (res.locals.userLogin) {
    return res.redirect("/dashboard");
  }
  return res.render("login");
};

// Validate login using Passport.js
const validate = (req, res, next) => {
  const { redirect_uri } = req.body;
  req.body.email = sanitizer.sanitize(req.body.email || "");
  req.body.password = sanitizer.sanitize(req.body.password || "");

  // Helper: Sanitize and validate redirect_uri
  const getSafeRedirectUrl = (uri) => {
    if (!uri || typeof uri !== "string") return "/";
    if (/^(https?:)?\/\//.test(uri)) {
      console.log("Potential open redirect attempt blocked:", uri);
      return "/";
    }
    return uri.startsWith("/") ? uri : `/${uri}`;
  };

  // Helper: Perform user login logging
  const performLogging = async (user) => {
    try {
      await createLog(
        {
          eventType: "Authentication",
          action: "Login",
          target: user.email,
        },
        {
          user: {
            _id: user._id,
            businessId: user.businessId,
            role: user.role,
          },
          ip: req.ip,
          headers: req.headers["user-agent"] || "Unknown",
        },
        res
      );
    } catch (err) {
      console.error("Log creation failed:", err);
    }
  };

  passport.authenticate("local", (err, user, info) => {
    if (err) {
      req.flash("error", err.message);
      return res.redirect(getSafeRedirectUrl(redirect_uri));
    }

    if (!user) {
      req.flash("error", "Invalid Email or Password.");
      return res.redirect(getSafeRedirectUrl(redirect_uri));
    }

    req.logIn(user, (err) => {
      if (err) {
        req.flash("error", err.message);
        return res.redirect(getSafeRedirectUrl(redirect_uri));
      }

      if (user.isAccountDeleted) {
        req.flash("error", "No account found with this email.");
        return res.redirect(getSafeRedirectUrl(redirect_uri));
      }

      req.session.userid = user._id;
      req.session.username = user.name;
      req.session.useremail = user.email;

      req.session.save(async (saveErr) => {
        if (saveErr) {
          console.error("Session save error:", saveErr);
          req.flash("error", "Something went wrong, please try again.");
          return res.redirect(getSafeRedirectUrl(redirect_uri));
        }

        if (!user.isVerified) {
          req.flash(
            "error",
            "Please check your email for account verification."
          );
          return res.redirect(
            `${req.protocol}://${req.get(
              "host"
            )}/auth-email-verification?token=${user.verificationToken}`
          );
        }

        const isUserOnBoarded = !!user.businessId;
        const safeRedirect = getSafeRedirectUrl(redirect_uri);

        await performLogging(user);

        // Redirect logic
        if (!redirect_uri) {
          return res.redirect(isUserOnBoarded ? "/" : "/onboard");
        }

        return res.redirect(safeRedirect);
      });
    });
  })(req, res, next);
};

// const validateApi = async (req, res) => {
//   const { email, password } = req.body;

//   const user = await User.findOne({ email });
//   if (!user) {
//     return res.status(404).json({ message: "User not found", success: false });
//   }
//   const isMatch = await user.comparePassword(password);
//   if (!isMatch) {
//     return res
//       .status(401)
//       .json({ message: "Invalid credentials", success: false });
//   }
//   const token = jwt.sign({ id: user._id }, process.env.SECRET, {
//     expiresIn: "1d",
//   });
//   return res
//     .status(200)
//     .json({ token, user, message: "Login successfull", success: true });
// };
// Logout the user
const logout = (req, res) => {
  const user = req.user;

  req.logout(async (err) => {
    if (err) {
      console.error("Logout Error:", err);
      req.flash("error", err.message);
      return res.redirect("/login");
    }

   
    if (user) {
      try {
        await createLog(
          {
            eventType: "Authentication",
            action: "Logout",
            target: user.email,
          },
          {
            user: {
              _id: user._id,
              businessId: user.businessId,
              role: user.role,
            },
            ip: req.ip,
            headers: req.headers["user-agent"] || "Unknown",
          },
          res
        );
      } catch (logErr) {
        console.error("Logout log failed:", logErr);
        
      }
    }

    // Destroy session after logging
    req.session.destroy((err) => {
      if (err) {
        console.error("Session Destroy Error:", err);
        req.flash("error", "Failed to end session");
        return res.redirect("/login");
      }

      res.redirect("/login");
    });
  });
};

// Forgot password - send reset link
const forgotpassword = async (req, res) => {
  const userEmail = req.body.email;
  const user = await User.findOne({ email: userEmail });
  console.log("user", user);
  if (!user) {
    req.flash("error", "Please provide registered User's email id.");
    return res.redirect("/forgotpassword");
  }

  //create password token
  const resetToken = user.createPasswordResetToken();

  //add updated reset token to user
  user.resetPasswordToken = resetToken;

  //save user with resetToken
  await user.save({ validateBeforeSave: false });

  //const resetPasswordUrl = `${req.protocol}s://${req.get('host')}/resetpassword?token=${resetToken}`;
  const resetPasswordUrl = `${req.protocol}://${req.get(
    "host"
  )}/resetpswdview/?token=${resetToken}`;
  //const message = `Reset your password with the given link: <a href="${resetPasswordUrl}">${resetPasswordUrl}</a>`;

  //const resetLink = resetPasswordUrl;
  try {
    //console.log("user reset link",resetPasswordUrl)
    await sendEmail({
      email: user.email,
      subject: "Reset Password",
      message: `Reset your password with the given link: <a href="${resetPasswordUrl}">${resetPasswordUrl}</a>`,
    });
    req.flash("message", `Password reset link sent on email ${user.email}`);
  } catch (err) {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save({ validateBeforeSave: false });
    req.flash("error", err.message);
  }

  return res.redirect("/forgotpassword");
};

// Check if reset token is valid
const resetpswdview = async (req, res) => {
  //console.log("resetpswdview req recived in controller")
  const { token } = req.query;

  const user = await User.findOne({
    resetPasswordToken: token,
    resetPasswordExpires: { $gt: Date.now() },
  });

  if (!user) {
    req.flash("error", "Password reset token is invalid or has expired.");
    return res.redirect("/error");
  }
  return res.render("auth/resetpassword", {
    token,
    title: "Change Password",
    layout: "layouts/layout-without-nav",
  });
};

// Change password
const changepassword = async (req, res) => {
  const { token, password } = req.body;

  console.log("token & password received", token, password);

  const user = await User.findOne({
    resetPasswordToken: token,
    resetPasswordExpires: { $gt: Date.now() },
  });
  if (!user) {
    req.flash("error", "Password reset token is invalid or has expired.");
    return res.redirect("/error");
  }

  user.password = password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpires = undefined;
  await user.save();

  const message = `Your password for <a href="https://mixcertificate.mixcommerce.co/">MixCertificate</a> has been changed. If this was a mistake, please ignore this mail.`;
  try {
    await sendEmail({
      email: user.email,
      subject: "MixCertificate - Password changed",
      message,
    });
    req.flash("message", "Password reset successfully.");
  } catch (err) {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save({ validateBeforeSave: false });
    req.flash("error", err.message);
  }

  return res.redirect("/login");
};

module.exports = {
  signup,
  login,
  validate,
  logout,
  forgotpassword,
  resetpswdview,
  changepassword,
  appstatus,
  verifyEmail,
  resendEmail,
  // validateApi,
};
