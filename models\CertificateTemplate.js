const mongoose = require("mongoose");

// Define the schema for the certificate template
const certificateTemplateSchema = new mongoose.Schema({
  certificateName: {
    type: String,
    required: true,
  },
  startDate: {
    type: Date,
    required: false,
  },
  endDate: {
    type: Date,
    required: false,
  },
  location: {
    type: String,
    required: false,
  },
  url: {
    type: String,
    required: false,
  },
  canvas: {
    type: Object,
    required: false,
  },
  html: {
    type: String,
    required: false,
  },
  pdfURL: {
    type: String,
    required: false,
    default: "/assets/images/certificate/sample-certificate-horizontal.pdf",
  },
  imageURL: {
    type: String,
    required: false,
    default: "/assets/images/certificate/sample-certificate-horizontal.jpg",
  },
  variables: {
    type: Map,
    of: String,
    default: {},
  },
  creatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: false,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  businessId: { type: mongoose.Schema.Types.ObjectId, ref: "Business" },
  status: {
    type: String,
    enum: ["draft", "published"],
    default: "draft",
  },
  public: {
    type: Boolean,
    default: true,
  },
  format: {
    type: String,
    required: false,
  },
  style: {
    type: String,
    required: false,
  },
  theme: {
    type: String,
    required: false,
  },
  feature: {
    type: String,
    required: false,
  },
  grade: {
    type: String,
    required: false,
  },
  subject: {
    type: String,
    required: false,
  },
  topic: {
    type: String,
    required: false,
  },
  industries: {
    type: [String],
    default: [],
  },
  metaTitle: {
    type: String,
  },
  metaDescription: {
    type: String,
  },
  Content: {
    type: String,
  },
});

// Create indexes for faster search
certificateTemplateSchema.index({ creatorId: 1 });
certificateTemplateSchema.index({ createdAt: 1 });

module.exports = mongoose.model(
  "CertificateTemplate",
  certificateTemplateSchema
);
