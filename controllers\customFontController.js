const path = require("path");
const fs = require("fs");
const Font = require("../models/CustomFont");



exports.uploadFont = async (req, res) => {
  try {
    const { fileUrl, name } = req.body;

    const font = new Font({
      name,
      fileUrl,
      uploadedBy: req.user._id,
      businessId: req.user.businessId,
    });

    await font.save();

    res.status(201).json({ message: "Font uploaded successfully", font });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Font upload failed", error: error.message });
  }
};

exports.deleteFont = async (req, res) => {
  try {
    const font = await Font.findOneAndDelete({
      _id: req.params.id,
      businessId: req.user.businessId,
    });
    if (!font) return res.status(404).json({ message: "Font not found" });

    const filePath = path.join(__dirname, "../public", font.fileUrl);
    if (fs.existsSync(filePath)) fs.unlinkSync(filePath);

    res.json({ message: "<PERSON>ont deleted successfully" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error deleting font", error: error.message });
  }
};

exports.getAllFonts = async (req, res) => {
  try {
    const fonts = await Font.find({ businessId: req.user.businessId });
    res.json(fonts);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to fetch fonts", error: error.message });
  }
};

exports.getFontById = async (req, res) => {
  try {
    const font = await Font.findOne({
      _id: req.params.id,
      businessId: req.user.businessId,
    });
    if (!font) return res.status(404).json({ message: "Font not found" });
    res.json(font);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error fetching font", error: error.message });
  }
};

