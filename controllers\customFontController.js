const path = require("path");
const fs = require("fs");
const Font = require("../models/CustomFont");
const AdmZip = require("adm-zip");
const { v4: uuidv4 } = require("uuid");

// Upload single font file
exports.uploadFont = async (req, res) => {
  try {
    const { fileUrl, name } = req.body;

    const font = new Font({
      name,
      fontFamily: name,
      fileUrl,
      uploadType: "single",
      uploadedBy: req.user._id,
      businessId: req.user.businessId,
    });

    await font.save();

    res.status(201).json({ message: "Font uploaded successfully", font });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ message: "Font upload failed", error: error.message });
  }
};

// Upload ZIP file with multiple font variants
exports.uploadFontZip = async (req, res) => {
  try {
    if (!req.files || !req.files.zipFile) {
      return res.status(400).json({ message: "No ZIP file uploaded" });
    }

    const zipFile = req.files.zipFile;
    const { fontName } = req.body;

    if (!fontName) {
      return res.status(400).json({ message: "Font name is required" });
    }

    // Validate file type
    if (!zipFile.name.toLowerCase().endsWith(".zip")) {
      return res.status(400).json({ message: "Only ZIP files are allowed" });
    }

    // Create temporary directory for extraction
    const tempDir = path.join(__dirname, "../temp", uuidv4());
    const uploadsDir = path.join(__dirname, "../public/uploads");

    // Ensure directories exist
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Save ZIP file temporarily
    const tempZipPath = path.join(tempDir, zipFile.name);
    await zipFile.mv(tempZipPath);

    // Extract ZIP file
    const zip = new AdmZip(tempZipPath);
    const zipEntries = zip.getEntries();

    const fontVariants = [];
    const supportedExtensions = [".ttf", ".otf", ".woff", ".woff2"];

    for (const entry of zipEntries) {
      if (!entry.isDirectory) {
        const fileName = entry.entryName;
        const fileExt = path.extname(fileName).toLowerCase();

        if (supportedExtensions.includes(fileExt)) {
          // Extract font file
          const fontBuffer = entry.getData();
          const uniqueFileName = `${uuidv4()}_${fileName}`;
          const fontFilePath = path.join(uploadsDir, uniqueFileName);

          fs.writeFileSync(fontFilePath, fontBuffer);

          // Determine font variant from filename
          const variant = determineFontVariant(fileName);
          const { fontWeight, fontStyle } = getFontProperties(variant);

          fontVariants.push({
            variant,
            fileUrl: `/uploads/${uniqueFileName}`,
            fileName: fileName,
            fileSize: fontBuffer.length,
            fontWeight,
            fontStyle,
          });
        }
      }
    }

    if (fontVariants.length === 0) {
      // Clean up
      fs.rmSync(tempDir, { recursive: true, force: true });
      return res
        .status(400)
        .json({ message: "No valid font files found in ZIP" });
    }

    // Save ZIP file to uploads
    const zipFileName = `${uuidv4()}_${zipFile.name}`;
    const zipFilePath = path.join(uploadsDir, zipFileName);
    fs.copyFileSync(tempZipPath, zipFilePath);

    // Create font record
    const font = new Font({
      name: fontName,
      fontFamily: fontName,
      uploadType: "zip",
      zipFileUrl: `/uploads/${zipFileName}`,
      variants: fontVariants,
      uploadedBy: req.user._id,
      businessId: req.user.businessId,
    });

    await font.save();

    // Clean up temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });

    res.status(201).json({
      message: "Font ZIP uploaded successfully",
      font,
      variantsCount: fontVariants.length,
    });
  } catch (error) {
    console.error("ZIP upload error:", error);
    res
      .status(500)
      .json({ message: "Font ZIP upload failed", error: error.message });
  }
};

exports.deleteFont = async (req, res) => {
  try {
    const font = await Font.findOneAndDelete({
      _id: req.params.id,
      businessId: req.user.businessId,
    });
    if (!font) return res.status(404).json({ message: "Font not found" });

    const filePath = path.join(__dirname, "../public", font.fileUrl);
    if (fs.existsSync(filePath)) fs.unlinkSync(filePath);

    res.json({ message: "Font deleted successfully" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error deleting font", error: error.message });
  }
};

exports.getAllFonts = async (req, res) => {
  try {
    const fonts = await Font.find({ businessId: req.user.businessId });
    res.json(fonts);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to fetch fonts", error: error.message });
  }
};

exports.getFontById = async (req, res) => {
  try {
    const font = await Font.findOne({
      _id: req.params.id,
      businessId: req.user.businessId,
    });
    if (!font) return res.status(404).json({ message: "Font not found" });
    res.json(font);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error fetching font", error: error.message });
  }
};

// Helper function to determine font variant from filename
function determineFontVariant(fileName) {
  const name = fileName.toLowerCase();

  if (
    name.includes("bolditalic") ||
    name.includes("bold-italic") ||
    name.includes("bold_italic")
  ) {
    return "Bold Italic";
  } else if (name.includes("bold")) {
    return "Bold";
  } else if (name.includes("italic")) {
    return "Italic";
  } else if (name.includes("light")) {
    return "Light";
  } else if (name.includes("medium")) {
    return "Medium";
  } else if (
    name.includes("semibold") ||
    name.includes("semi-bold") ||
    name.includes("semi_bold")
  ) {
    return "SemiBold";
  } else if (
    name.includes("extrabold") ||
    name.includes("extra-bold") ||
    name.includes("extra_bold")
  ) {
    return "ExtraBold";
  } else if (name.includes("black")) {
    return "Black";
  } else if (name.includes("regular") || name.includes("normal")) {
    return "Regular";
  } else {
    // Default to Regular if no variant detected
    return "Regular";
  }
}

// Helper function to get CSS font properties from variant
function getFontProperties(variant) {
  const properties = {
    Light: { fontWeight: "300", fontStyle: "normal" },
    Regular: { fontWeight: "400", fontStyle: "normal" },
    Medium: { fontWeight: "500", fontStyle: "normal" },
    SemiBold: { fontWeight: "600", fontStyle: "normal" },
    Bold: { fontWeight: "700", fontStyle: "normal" },
    ExtraBold: { fontWeight: "800", fontStyle: "normal" },
    Black: { fontWeight: "900", fontStyle: "normal" },
    Italic: { fontWeight: "400", fontStyle: "italic" },
    "Bold Italic": { fontWeight: "700", fontStyle: "italic" },
  };

  return properties[variant] || { fontWeight: "400", fontStyle: "normal" };
}
