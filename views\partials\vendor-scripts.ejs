<!-- JAVASCRIPT -->
<script src="/assets/libs/jquery/jquery.min.js"></script>
<script src="/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="/assets/libs/metismenu/metisMenu.min.js"></script>
<script src="/assets/libs/simplebar/simplebar.min.js"></script>
<script src="/assets/libs/node-waves/waves.min.js"></script>
<script src="/assets/libs/feather-icons/feather.min.js"></script>
<!-- pace js -->
<script src="/assets/libs/pace-js/pace.min.js"></script>

<!-- Full Screen CSS & Script -->

<!-- black is default bg color, making white to make the screen look accurate -->
<style>
  ::backdrop {
    background-color: white;
  }
</style>

<script>
  document
    .getElementById("fullscreenButton")
    .addEventListener("click", function () {
      var elem = document.getElementById("layout-wrapper");
      if (!document.fullscreenElement) {
        if (elem.requestFullscreen) {
          elem.requestFullscreen();
        } else if (elem.mozRequestFullScreen) {
          // Firefox
          elem.mozRequestFullScreen();
        } else if (elem.webkitRequestFullscreen) {
          // Chrome, Safari and Opera
          elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) {
          // IE/Edge
          elem.msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          // Firefox
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          // Chrome, Safari and Opera
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          // IE/Edge
          document.msExitFullscreen();
        }
      }
    });
</script>
<!-- Full Screen CSS & Script -->

<!-- Quick Button Starts -->
<!-- <button
  id="addMaterBtn"
  class="btn btn-primary rounded-circle"
  style="
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 60px;
    height: 60px;
    font-size: 24px;
  "
>
  +
</button> -->

<!-- <div class="quick-options d-none" style="z-index: 1000">
  <a
    href="/contact-add/"
    class="btn btn-light"
    style="position: fixed; bottom: 19rem; right: 20px"
  >
    <i class="fas fa-user"></i> New Contact
  </a>
  <a
    href="/events/"
    class="btn btn-light"
    style="position: fixed; bottom: 15.5rem; right: 20px"
  >
    <i class="fas fa-handshake"></i> New Event
  </a>
  <a
    href="/design/"
    class="btn btn-light"
    style="position: fixed; bottom: 12rem; right: 20px"
  >
    <i class="fas fa-file-invoice"></i> New Design
  </a>
</div> -->
<!-- Quick Button Ends -->
<!-- Qucik button script starts -->
<script>
  $("#addMaterBtn").click(function () {
    $(".quick-options").toggleClass("d-none");
  });
</script>

<!-- Quick button script ends -->

<!-- notification jquery starts -->

<script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.10.7/dayjs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.10.7/plugin/relativeTime.min.js"></script>

<script>
  let notificationLimit = 30;
  let currentPage = 1;
  let notificationsItems = [];
  let isLoadingNotifications = false;
  let hasMoreNotifications = true;

  $(document).ready(function () {
    // Load the relative time plugin
    dayjs.extend(dayjs_plugin_relativeTime);

    function fetchNotifications(reset = false) {
      if (isLoadingNotifications) return;

      isLoadingNotifications = true;

      if (reset) {
        currentPage = 1;
        notificationsItems = [];
        $("#top-notification-bar").empty();
      }

      $.ajax({
        url: `/api/notifications?page=${currentPage}&limit=${notificationLimit}&status=unread`,
        method: "GET",
        success: function (data) {
          const { notifications, hasMore, unreadCount } = data;

          if (reset) {
            notificationsItems = notifications;
          } else {
            notificationsItems = [...notificationsItems, ...notifications];
          }

          hasMoreNotifications = hasMore;

          // Render new notifications (only unread ones)
          if (notifications.length > 0) {
            notifications.forEach(function (notification) {
              const notificationItem = createNotificationItem(notification);
              $("#top-notification-bar").append(notificationItem);
            });
          } else if (reset) {
            // Show message when no unread notifications
            $("#top-notification-bar").html(
              '<div class="text-center p-3 text-muted">No unread notifications</div>'
            );
          }

          // If after loading, there are still no notifications in the container, show the message
          if (
            $("#top-notification-bar .notification-item").length === 0 &&
            $("#top-notification-bar").text().trim() === ""
          ) {
            $("#top-notification-bar").html(
              '<div class="text-center p-3 text-muted">No unread notifications</div>'
            );
          }

          // Update unread count in bell icon (show only unread count)
          $(".bellCount").text(unreadCount);

          // Show/hide view more button based on hasMore
          if (hasMoreNotifications && notificationsItems.length > 0) {
            $(".viewMoreBtn").removeClass("d-none");
          } else {
            $(".viewMoreBtn").addClass("d-none");
          }

          // Update unread notifications link
          if (unreadCount > 0) {
            $(".unreadNotifications").removeClass("d-none");
            $(".unreadNotifications a").text(`Unread (${unreadCount})`);
          } else {
            $(".unreadNotifications").addClass("d-none");
          }

          isLoadingNotifications = false;
        },
        error: function (err) {
          console.error("Error fetching notifications:", err);
          isLoadingNotifications = false;
        },
      });
    }

    function createNotificationItem(notification) {
      const isUnread = !notification.isRead;
      const timeAgo = dayjs(notification.createdAt).fromNow();
      const unreadClass = isUnread
        ? "notification-unread"
        : "notification-read";

      // Always use bell icon instead of image
      const avatarHtml = `<div class="avatar-sm">
          <span class="avatar-title ${
            isUnread ? "bg-primary" : "bg-secondary"
          } rounded-circle">
            <i class="fas fa-bell" style="color: white;"></i>
          </span>
        </div>`;

      return `
        <div class="notification-item ${unreadClass}" data-id="${notification._id}" data-read="${notification.isRead}">
          <div class="d-flex p-3 position-relative">
            <div class="flex-shrink-0 me-3">
              ${avatarHtml}
            </div>
            <div class="flex-grow-1" style="cursor: pointer;" onclick="handleNotificationClick('${
              notification._id
            }', '${notification.url || ""}')">
              <div class="d-flex align-items-center mb-1">
                <h6 class="mb-0 ${
                  isUnread ? "fw-bold text-dark" : "fw-normal text-muted"
                }">${notification.title}</h6>
                ${
                  isUnread
                    ? '<span class="badge bg-danger ms-2" style="font-size: 0.6rem;">NEW</span>'
                    : ""
                }
              </div>
              <div class="font-size-13 ${
                isUnread ? "text-dark" : "text-muted"
              }">
                <p class="mb-1">${notification.message}</p>
                <p class="mb-0"><i class="mdi mdi-clock-outline"></i> <span>${timeAgo}</span></p>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    // Helper function to check if element is in viewport
    function isElementInViewport($el) {
      if (!$el.length) return false;
      var rect = $el[0].getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <=
          (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <=
          (window.innerWidth || document.documentElement.clientWidth)
      );
    }

    // Throttle function to limit how often scroll events fire
    function throttle(func, limit) {
      let inThrottle;
      return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => (inThrottle = false), limit);
        }
      };
    }

    // Function to check and mark visible notifications as read
    function checkVisibleNotifications() {
      $(".notification-item.notification-unread").each(function () {
        const $notification = $(this);
        const notificationId = $notification.data("id");

        // Check if notification is visible and in viewport
        if (
          $notification.is(":visible") &&
          isElementInViewport($notification)
        ) {
          // Add a small delay to ensure user has seen it
          setTimeout(() => {
            if (
              $notification.hasClass("notification-unread") &&
              isElementInViewport($notification)
            ) {
              markNotificationAsRead(notificationId, $notification);
            }
          }, 5000); // 5 second delay
        }
      });
    }

    // Scroll event handler for notification dropdown
    function setupNotificationScrollHandler() {
      const $notificationContainer = $("#top-notification-bar");

      // Throttled scroll handlers to improve performance
      const throttledCheckVisible = throttle(checkVisibleNotifications, 250);

      // Handle scroll within notification dropdown
      $notificationContainer.on("scroll", throttledCheckVisible);

      // Handle window scroll (in case dropdown is open)
      $(window).on("scroll.notifications", function () {
        if ($("#page-header-notifications-dropdown").hasClass("show")) {
          throttledCheckVisible();
        }
      });

      // Check when dropdown is opened
      $("#page-header-notifications-dropdown").on(
        "shown.bs.dropdown",
        function () {
          console.log(
            "Notification dropdown opened - checking visible notifications"
          );
          // Small delay to ensure dropdown is fully rendered
          setTimeout(() => {
            checkVisibleNotifications();
          }, 300);
        }
      );

      // Clean up when dropdown is closed
      $("#page-header-notifications-dropdown").on(
        "hidden.bs.dropdown",
        function () {
          console.log(
            "Notification dropdown closed - cleaning up scroll handlers"
          );
          $(window).off("scroll.notifications");
        }
      );
    }

    // Global functions for onclick handlers
    window.handleNotificationClick = function (notificationId, url) {
      const $notificationItem = $(
        `.notification-item[data-id="${notificationId}"]`
      );

      // Mark as read if unread
      if ($notificationItem.hasClass("notification-unread")) {
        markNotificationAsRead(notificationId, $notificationItem);
      }

      // Navigate to URL if provided
      if (url && url !== "#" && url !== "") {
        setTimeout(() => {
          window.location.href = url;
        }, 300); // Small delay to show the read state change
      }
    };

    // Removed markSingleAsRead function - notifications are marked as read when viewed

    // View More button click - Fixed selector
    $(document).on("click", ".viewMoreBtn a", function (e) {
      e.preventDefault();
      if (!isLoadingNotifications && hasMoreNotifications) {
        currentPage++;
        fetchNotifications();
      }
    });

    // Mark all as read functionality
    $(document).on("click", ".unreadNotifications a", function (e) {
      e.preventDefault();
      markAllNotificationsAsRead();
    });

    // Track notifications being marked as read to prevent duplicate API calls
    const markingAsRead = new Set();

    function markNotificationAsRead(notificationId, $notificationElement) {
      // Prevent duplicate API calls for the same notification
      if (markingAsRead.has(notificationId)) {
        return;
      }

      // Add to tracking set
      markingAsRead.add(notificationId);

      $.ajax({
        url: `/api/notifications/${notificationId}/read`,
        method: "PUT",
        success: function () {
          // Update the notification appearance to show it's been read
          $notificationElement
            .removeClass("notification-unread")
            .addClass("notification-read");
          $notificationElement.attr("data-read", "true");

          // Update the title styling
          $notificationElement
            .find("h6")
            .removeClass("fw-bold text-dark")
            .addClass("fw-normal text-muted");

          // Update the message styling
          $notificationElement
            .find(".font-size-13")
            .removeClass("text-dark")
            .addClass("text-muted");

          // Remove the "NEW" badge
          $notificationElement.find(".badge").remove();

          // Update unread count
          updateUnreadCount();

          console.log(`Notification ${notificationId} marked as read`);
        },
        error: function (err) {
          console.error("Error marking notification as read:", err);
          // Remove from tracking set on error so it can be retried
          markingAsRead.delete(notificationId);
        },
        complete: function () {
          // Remove from tracking set after completion (success or error)
          setTimeout(() => {
            markingAsRead.delete(notificationId);
          }, 1000); // Small delay to prevent rapid re-marking
        },
      });
    }

    function markAllNotificationsAsRead() {
      $.ajax({
        url: "/api/notifications/mark-all-read",
        method: "PUT",
        success: function (data) {
          // Since we only show unread notifications, clear the dropdown
          $("#top-notification-bar").html(
            '<div class="text-center p-3 text-muted">No unread notifications</div>'
          );

          // Hide view more button and unread notifications link
          $(".viewMoreBtn").addClass("d-none");
          $(".unreadNotifications").addClass("d-none");

          // Update unread count
          $(".bellCount").text("0");

          showToast(
            `${data.modifiedCount} notifications marked as read`,
            "success"
          );
        },
        error: function (err) {
          console.error("Error marking all notifications as read:", err);
          showToast("Failed to mark all notifications as read", "danger");
        },
      });
    }

    function updateUnreadCount() {
      $.ajax({
        url: "/api/notifications/unread-count",
        method: "GET",
        success: function (data) {
          const unreadCount = data.unreadCount;
          $(".bellCount").text(unreadCount);

          if (unreadCount > 0) {
            $(".unreadNotifications").removeClass("d-none");
            $(".unreadNotifications a").text(`Unread (${unreadCount})`);
          } else {
            $(".unreadNotifications").addClass("d-none");
            // If no unread notifications, ensure dropdown shows the message
            if ($("#top-notification-bar .notification-item").length === 0) {
              $("#top-notification-bar").html(
                '<div class="text-center p-3 text-muted">No unread notifications</div>'
              );
            }
          }
        },
        error: function (err) {
          console.error("Error fetching unread count:", err);
        },
      });
    }

    // Auto-refresh notifications every 5 minutes
    setInterval(function () {
      updateUnreadCount();
    }, 5 * 60 * 1000);

    // Initialize scroll handlers
    setupNotificationScrollHandler();

    // Initial load
    fetchNotifications(true);
  });
</script>

<style>
  /* Unread notifications - clean white styling with subtle indicator */
  .notification-item.notification-unread {
    background-color: #ffffff;
    border-left: 3px solid #5156be;
    border-radius: 0 6px 6px 0;
  }

  /* Read notifications - subtle styling */
  .notification-item.notification-read {
    background-color: #ffffff;
    border-left: 3px solid #e9ecef;
    border-radius: 0 6px 6px 0;
  }

  /* Hover effects */
  .notification-item:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .notification-item.notification-unread:hover {
    background-color: #f8f9fa !important;
    border-left-color: #4c51bf;
  }

  /* Avatar styling */
  .avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 16px;
    transition: all 0.3s ease;
  }

  /* Badge styling */
  .notification-item .badge {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Removed Mark as Read button styling - no longer needed */

  /* Notification content styling */
  .notification-item h6 {
    transition: all 0.3s ease;
  }

  .notification-item .font-size-13 {
    transition: all 0.3s ease;
  }

  /* Separator between notifications */
  .notification-item + .notification-item {
    border-top: 1px solid #f1f3f4;
  }

  /* Animation for state changes */
  .notification-item {
    transition: all 0.3s ease;
  }

  /* Loading state for buttons */
  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Custom scrollbar for notification dropdown */
  #top-notification-bar::-webkit-scrollbar {
    width: 6px;
  }

  #top-notification-bar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  #top-notification-bar::-webkit-scrollbar-thumb {
    background: #5156be;
    border-radius: 3px;
  }

  #top-notification-bar::-webkit-scrollbar-thumb:hover {
    background: #4c51bf;
  }
</style>

<!-- notification jquery ends -->

<!-- V1 Internet Connection Checker Script starts-->

<style>
  .connection-status {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 9999;
    padding: 10px;
    color: white;
  }
  .offline {
    background-color: #dc3545;
  }
  .online {
    background-color: #20c997;
  }
</style>

<script>
  $(document).ready(function () {
    function checkConnection() {
      return window.navigator.onLine;
    }

    function showConnectionStatus(isOnline) {
      var statusDiv = $("#connection-status");

      if (isOnline) {
        if (statusDiv.length) {
          statusDiv
            .removeClass("offline")
            .addClass("online")
            .text("You are back online");
          setTimeout(function () {
            statusDiv.fadeOut(500, function () {
              $(this).remove();
            });
          }, 3000);
        }
      } else {
        if (!statusDiv.length) {
          $("body").prepend(
            '<div id="connection-status" class="connection-status offline">You are offline</div>'
          );
        } else {
          statusDiv
            .removeClass("online")
            .addClass("offline")
            .text("You are offline")
            .show();
        }
      }
    }

    setInterval(function () {
      var isOnline = checkConnection();
      showConnectionStatus(isOnline);
    }, 5000); // Check every 5 seconds

    // Initial check
    showConnectionStatus(checkConnection());
  });
</script>
<!-- V1 Internet Connection Checker Script ends-->

<!-- set display name starts -->
<script>
  $(document).ready(function () {
    $.ajax({
      url: "/api/users/profile",
      type: "GET",
      success: function (response) {
        if (response) {
          // Set displayName or fallback to firstName + lastName
          var displayName = response.displayName
            ? response.displayName
            : response.firstName + " " + response.lastName;
          $("#display-name").text(displayName);

          // Set profile picture or fallback to default image
          var profilePicture = response.profilePicture
            ? response.profilePicture
            : "/assets/images/users/avatar-1.jpg";
          $("#profile-picture").attr("src", profilePicture);
        } else {
          console.error("Profile data is missing");
        }
      },
      error: function (xhr, status, error) {
        console.error("Error fetching profile data:", error);
      },
    });
  });
</script>
<!-- set display name ends -->

<!-- universal toast code starts-->

<!-- Main Container -->
<div
  id="toast-container"
  class="position-fixed bottom-0 end-0 p-3"
  style="z-index: 1100"
></div>

<script>
  function showToast(message, type = "primary", autoHide = true, delay = 3000) {
    // Create a unique ID for each toast
    var toastId = "toast-" + Date.now();

    // Create the toast HTML and ensure it aligns left
    var toastHtml = `
      <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0 position-fixed start-0 bottom-0 m-3" style="z-index: 1100;" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;

    // Append the toast to the container
    $("#toast-container").append(toastHtml);

    // Initialize the toast component
    var toastElement = document.getElementById(toastId);
    var toast = new bootstrap.Toast(toastElement, {
      autohide: autoHide,
      delay: delay,
    });

    // Show the toast
    toast.show();

    // Optionally remove the toast from DOM after it's hidden
    $(toastElement).on("hidden.bs.toast", function () {
      $(this).remove();
    });
  }

  $(document).ready(function () {
    // Example usage:
    //showToast('This is a success message', 'success');
    //showToast('This is a success message', 'success');
    // showToast('This is an error message', 'danger', true, 5000);
  });
</script>
<!-- universal toast code ends-->

<script>
  $(document).ready(function () {
    let nameSpaceCode = "";
    $.ajax({
      url: "/api/users/profile",
      method: "GET",
      success: function (response) {
        if (response && response.businessId) {
          fetchNameSpaceFromBusinessData(response.businessId);
        }
      },
      error: function (xhr, status, error) {
        console.error("Error fetching profile data:", error);
      },
    });

    function fetchNameSpaceFromBusinessData(businessId) {
      $.ajax({
        url: "/api/business/" + businessId,
        method: "GET",
        success: function (response) {
         
          if (response && response.nameSpaceCode) {
            nameSpaceCode = response.nameSpaceCode;
          }
        },
        error: function (error) {
          console.log(error);
        },
      });
    }
    document
      .getElementById("publicHomeButton")
      .addEventListener("click", function () {
        window.open(`/${nameSpaceCode}/p/home`, "_blank");
      });
  });
</script>
