const express = require("express");
const router = express.Router();
const webhookController = require("../controllers/webhookController");

// POST /api/webhooks
router.post("/", webhookController.createWebhook);

// GET /api/webhooks/:eventId
router.get("/:eventId", webhookController.getWebhooksByEvent);

// PUT /api/webhooks/:id
router.put("/:id", webhookController.updateWebhook);

// DELETE /api/webhooks/:id
router.delete("/:id", webhookController.deleteWebhook);

router.get("/",webhookController.getAllWebhooks)

module.exports = router;
