const mongoose = require("mongoose");

const apiKeySchema = new mongoose.Schema({
  name: String,
  key: String,
  scopes: [String],
  createdAt: { type: Date, default: Date.now },
  lastUsedAt: Date,
  expiresAt: Date,
  businessId: { type: mongoose.Schema.Types.ObjectId, ref: "Business" },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
});

module.exports = mongoose.model("ApiKey", apiKeySchema);
