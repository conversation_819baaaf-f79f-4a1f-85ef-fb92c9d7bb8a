const mongoose = require("mongoose");

const apiKeySchema = new mongoose.Schema(
  {
    name: String,
    key: String,
    scopes: [String],
    createdAt: { type: Date, default: Date.now },
    lastUsedAt: Date,
    expiresAt: Date,
    businessId: { type: mongoose.Schema.Types.ObjectId, ref: "Business" },
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    status: {
      type: String,
      enum: ["active", "expired", "revoked"],
      default: "active",
    },
    tobeShown: {
      type: Boolean,
      default: false,
    },
    toBeShownDefaultToFalseAgainAfterExpiry: Date,
    otp: String,
    otpExpiresAt: Date,
  },

  {
    timestamps: true,
  }
);

module.exports = mongoose.model("ApiKey", apiKeySchema);
