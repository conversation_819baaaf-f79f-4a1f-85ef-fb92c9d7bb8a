{"name": "mixcertififcate C124", "version": "2.0.0", "description": "Bulk certificate generator with CSV, Excel and API.", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "nodemon app.js"}, "author": "mixcommerceco", "license": "ISC", "devDependencies": {"bcryptjs": "2.4.3", "body-parser": "1.20.2", "browser-sync": "2.27.10", "connect-flash": "0.1.1", "cookie-parser": "1.4.6", "crypto": "^1.0.1", "del": "6.0.0", "dotenv": "16.0.3", "ejs": "3.1.8", "express": "4.18.2", "express-ejs-layouts": "2.5.0", "express-fileupload": "1.4.0", "express-session": "1.17.3", "gulp": "4.0.2", "gulp-autoprefixer": "8.0.0", "gulp-cached": "1.1.1", "gulp-clean-css": "4.3.0", "gulp-cssnano": "2.1.3", "gulp-file-include": "2.3.0", "gulp-if": "3.0.0", "gulp-npm-dist": "1.0.3", "gulp-rename": "2.0.0", "gulp-replace": "1.1.3", "gulp-rtlcss": "1.4.2", "gulp-sass": "5.1.0", "gulp-sourcemaps": "3.0.0", "gulp-uglify": "3.0.2", "gulp-useref-plus": "0.0.8", "i18n": "0.15.1", "i18n-express": "1.1.3", "jsonwebtoken": "9.0.0", "mongoose": "6.10.0", "node-localstorage": "2.2.1", "node-sass": "9.0.0", "nodemailer": "6.9.1", "nodemon": "2.0.20", "path": "0.12.7", "sass": "1.56.0", "validator": "13.9.0"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "27.1.0", "@curiosityx/bootstrap-session-timeout": "1.0.0", "@fullcalendar/bootstrap": "4.4.0", "@fullcalendar/core": "4.4.0", "@fullcalendar/daygrid": "4.4.0", "@fullcalendar/interaction": "4.4.0", "@fullcalendar/timegrid": "4.4.0", "@paddle/paddle-node-sdk": "^2.7.3", "@simonwep/pickr": "1.8.1", "adm-zip": "^0.5.16", "admin-resources": "git+https://github.com/themesbrand/admin-resources#master", "alertifyjs": "1.13.1", "apexcharts": "3.26.3", "archiver": "^7.0.1", "axios": "^1.10.0", "bootstrap": "5.3.0", "chance": "1.1.9", "chargebee": "^2.43.0", "chart.js": "4.1.1", "choices.js": "10.2.0", "chromium": "^3.0.3", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "datatables.net": "1.13.1", "datatables.net-autofill": "2.5.1", "datatables.net-autofill-bs4": "2.5.1", "datatables.net-bs4": "1.13.1", "datatables.net-buttons": "2.3.3", "datatables.net-buttons-bs4": "2.3.3", "datatables.net-keytable": "2.8.0", "datatables.net-keytable-bs4": "2.8.0", "datatables.net-responsive": "2.4.0", "datatables.net-responsive-bs4": "2.4.0", "datatables.net-select": "1.5.0", "datatables.net-select-bs4": "1.5.0", "dragula": "^3.7.3", "dropzone": "^5.7.2", "echarts": "5.4.1", "express-rate-limit": "^7.4.0", "express-useragent": "^1.0.15", "fabric": "^6.4.2", "feather-icons": "4.29.0", "flatpickr": "4.6.13", "glightbox": "3.0.9", "gmaps": "0.4.24", "gulp-rtlcss": "1.4.1", "helmet": "^7.1.0", "imask": "6.0.7", "jimp": "^1.6.0", "jquery": "3.5.1", "jquery-countdown": "2.2.0", "jquery-knob": "1.2.11", "jquery-sparkline": "2.4.0", "json2csv": "^6.0.0-alpha.2", "jsvectormap": "1.5.3", "jszip": "3.2.2", "leaflet": "1.6.0", "masonry-layout": "4.2.2", "maxmind": "^4.3.21", "metismenu": "3.0.4", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.7", "node-cron": "^4.0.6", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "node-waves": "0.7.6", "nouislider": "15.1.1", "openai": "^4.63.0", "pace-js": "1.2.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "passport-microsoft": "^2.1.0", "pdf-lib": "^1.17.1", "pdfmake": "0.1.65", "pristinejs": "0.1.9", "puppeteer": "^23.11.1", "qrcode": "^1.5.4", "rater-js": "1.0.1", "sanitizer": "^0.1.3", "simplebar": "4.2.3", "sweetalert2": "11.0.12", "swiper": "6.7.0", "table-edits": "0.0.3", "twitter-bootstrap-wizard": "1.2.0", "uuid": "^11.1.0", "wait-on": "^7.2.0", "wnumb": "1.2.0"}}