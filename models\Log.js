// models/Log.js
const mongoose = require("mongoose");

const logSchema = new mongoose.Schema(
  {
    timestamp: { type: Date, default: Date.now },
    user: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    role: { type: String, required: true },
    eventType: { type: String, required: true },
    action: { type: String, required: true },
    target: { type: String },
    ipAddress: { type: String },
    userAgent: { type: String },
    businessId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Business",
      required: true,
    },
    requestId: { type: String, required: true },
  },
  { timestamps: true }
);
logSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });
module.exports = mongoose.model("Log", logSchema);
