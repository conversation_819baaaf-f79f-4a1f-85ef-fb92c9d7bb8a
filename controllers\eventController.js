const Event = require("../models/Event");
const User = require("../models/User");
const ContactList = require("../models/ContactList");
// eventController.js
const {
  generateBulkCertificates,
  generateCertificateForNewlyAddedContactAfterBulkGeneration,
} = require("./certificateTemplateController");
const Contact = require("../models/Contact");
const Webhook = require("../models/Webhook");
const nodemailer = require("nodemailer");
const path = require("path");
const AdmZip = require("adm-zip");
const fs = require("fs");

// eventController.js

exports.createEvent = async (req, res) => {
  try {
    const isUserVerified = await User.findById(req.user._id);
    if (!isUserVerified?.isVerified) {
      return res.status(400).json({ error: "User is not verified" });
    }

    const event = new Event(req.body);
    event.creatorId = req.user._id;
    await event.save();

    console.log(
      req.shouldCertificateBeSentToRecipient,
      "shouldCertificateBeSentToRecipient"
    );

    // ✅ Send response early for confirmation screen
    res.status(201).json(event);

    // 🔄 Continue processing in background
    (async () => {
      try {
        const generateReq = {
          body: {
            designId: req.body.designId,
            recipients: await getRecipientsFromContactList(
              req.body.contactList
            ),
            event: event,
          },
          user: req.user,
          shouldCertificateBeSentToRecipient:
            req.body.shouldCertificateBeSentToRecipient,
          ip: req.ip,
          headers: req.headers?.["user-agent"] || "Unknown",
        };
        console.log("generateReq", generateReq);
        await generateBulkCertificates(generateReq, res);
      } catch (err) {
        console.error(
          "Error in background certificate generation:",
          err.message
        );
        await Event.findByIdAndUpdate(event._id, { status: "Failed" });
      }
    })();
  } catch (error) {
    console.error("Error creating event:", error.message);
    res.status(400).json({ error: error.message });
  }
};

// Create a new event
exports.createEventOld = async (req, res) => {
  try {
    const event = new Event(req.body);
    //console.log("Event Received:",event )
    event.creatorId = req.user._id; // Set the userId to the current user's ID
    await event.save();

    //send the bulk certificate generation request from here and let it run

    res.status(201).json(event);
  } catch (error) {
    //console.log("error creating event", error.message, error)

    res.status(400).json({ error: error.message });
  }
};

// Get all events
exports.getAllEvents = async (req, res) => {
  try {
    const events = await Event.find({}).sort({ createdAt: -1 });
    res.status(200).json({
      events,
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get a single event by ID
exports.getEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.status(200).json(event);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get a single event by ID with populated data
exports.getCertificateEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id)
      .populate("designId") // Populate design ID
      .populate({
        path: "contactList",
        populate: {
          path: "contacts", // Ensure contacts in contactList are populated
          model: "Contact", // Specify the model for contacts if necessary
        },
      });

    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    res.status(200).json(event);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Update an event by ID
exports.updateEvent = async (req, res) => {
  try {
    const event = await Event.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.status(200).json(event);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Delete an event by ID
exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findByIdAndDelete(req.params.id);
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.status(200).json({ message: "Event deleted successfully" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

//Event locking and unlocking

// Controller to lock/unlock certificate generation
// controllers/eventController.js
exports.toggleCertificateGenerationLock = async (req, res) => {
  try {
    const { id } = req.params;
    const { lockStatus } = req.body;

    const updatedEvent = await Event.findByIdAndUpdate(
      { _id: id },
      { locked: lockStatus },
      { new: true }
    );

    if (!updatedEvent) {
      return res.status(404).json({ error: "Event not found" });
    }

    res.status(200).json({
      message: "Lock status updated",
      lockStatus: updatedEvent.lockNewCertificateGeneration,
    });
  } catch (error) {
    res.status(500).json({ error: error.message }); // Use error.message here
  }
};

//get contact id from contact list
async function getRecipientsFromContactList(contactListId) {
  try {
    // Find the contact list by its ID and populate the contacts field if necessary
    const contactList = await ContactList.findById(contactListId).lean();

    if (!contactList) {
      throw new Error("Contact list not found");
    }

    // Assuming `contactList.contacts` is an array of `Contact` IDs
    const recipients = contactList.contacts || [];
    return recipients; // This will be an array of Contact IDs
  } catch (error) {
    console.error("Error fetching recipients:", error.message);
    throw error;
  }
}

exports.createCertificateForNewContact = async (req, res) => {
  try {
    await generateCertificateForNewlyAddedContactAfterBulkGeneration(req, res);
  } catch (error) {
    console.error("Error in createCertificateForNewContact:", error.message);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

exports.sendBulkEmailsCertificateRecipients = async (req, res) => {
  try {
    const { recipientEmails, eventId } = req.body;
    console.log(recipientEmails, eventId);

    const event = await Event.findById(eventId).populate("certificates");
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }

    const certIds = event.certificates.map((cert) => cert._id.toString());

    const emailPromises = recipientEmails.map(async (email) => {
      const contact = await Contact.findOne({
        businessEmail: email,
        certificates: { $in: certIds },
      }).populate("certificates");

      if (!contact) {
        console.warn(`Contact not found for email: ${email}`);
        return;
      }

      const relevantCertificates = contact.certificates.filter((cert) =>
        certIds.includes(cert._id.toString())
      );

      if (relevantCertificates.length === 0) {
        console.warn(`No matching certificates for ${email}`);
        return;
      }

      await Promise.all(
        relevantCertificates.map((cert) => sendCertificateEmail(contact, cert))
      );
    });

    await Promise.all(emailPromises);

    return res
      .status(200)
      .json({ message: "Emails sent successfully", success: true });
  } catch (error) {
    console.error(
      "Error in sendBulkEmailsCertificateRecipients:",
      error.message
    );
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

async function sendCertificateEmail(recipient, certificate) {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: recipient.businessEmail,
      subject: "Your Certificate",
      text: "Please find your certificate attached.",
      attachments: [
        {
          filename: path.basename(certificate.pdfURL),
          path: path.join(__dirname, "../public", certificate.pdfURL),
        },
      ],
    };
    await transporter.sendMail(mailOptions);
  } catch (err) {
    console.error(
      `Failed to send email to ${recipient.businessEmail}:`,
      err.message
    );
  }
}

exports.bulkDownloadCertificates = async (req, res) => {
  try {
    const { eventId } = req.query;

    if (!eventId) {
      return res
        .status(400)
        .json({ success: false, error: "Event ID is required" });
    }

    const event = await Event.findById(eventId).populate("certificates");

    if (!event) {
      return res.status(404).json({ success: false, error: "Event not found" });
    }

    const zip = new AdmZip();
    let filesAdded = 0;

    event.certificates.forEach((certificate) => {
      const pdfPath = path.join(__dirname, "../public", certificate.pdfURL);
      const imagePath = path.join(__dirname, "../public", certificate.imageURL);
      if (fs.existsSync(pdfPath)) {
        zip.addLocalFile(pdfPath);

        filesAdded++;
      }

      if (fs.existsSync(imagePath)) {
        zip.addLocalFile(imagePath);
        filesAdded++;
      }
    });

    if (filesAdded === 0) {
      return res
        .status(404)
        .json({ success: false, error: "No certificates found to download" });
    }

    const zipFileName = `certificates-${Date.now()}.zip`;
    const zipFilePath = path.join(__dirname, "../public", zipFileName);

    zip.writeZip(zipFilePath);

    res.download(zipFilePath, zipFileName, (err) => {
      fs.unlink(zipFilePath, () => {});
      if (err) {
        console.error("Download error:", err);
        return res.status(500).end();
      }
    });
  } catch (error) {
    console.error("Error in bulkDownloadCertificates:", error.message);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};
exports.deleteCandidateFromEvent = async (req, res) => {
  try {
    const { eventId, contactId } = req.body;

    const event = await Event.findById(eventId).populate("certificates");
    if (!event) {
      return res.status(404).json({ success: false, error: "Event not found" });
    }

    const certIds = event.certificates.map((cert) => cert._id.toString());

    const existingContact = await Contact.findById(contactId).populate(
      "certificates"
    );
    if (!existingContact) {
      return res
        .status(404)
        .json({ success: false, error: "Contact not found" });
    }

    const certificateIdOfCandidate = existingContact.certificates.find((cert) =>
      certIds.includes(cert._id.toString())
    );

    await ContactList.findByIdAndUpdate(event.contactList, {
      $pull: {
        contacts: contactId,
      },
    });

    const generatedWebhooks = await Webhook.find({
      isEnabled: true,
      triggers: ["candidate.deleted"],
    });

    if (generatedWebhooks.length > 0) {
      const webhookPromises = generatedWebhooks.map((webhook) =>
        sendWebhookWithRetry(
          webhook.url,
          {
            event: "candidate.deleted",
            timeStamp: Date.now(),
            data: {
              eventId,
              candidateId: contactId,
              designId: event.designId,
              certificateId: certificateIdOfCandidate
                ? certificateIdOfCandidate._id
                : null,
            },
          },
          webhook.secret || null
        )
      );
      await Promise.all(webhookPromises);
    }

    return res
      .status(200)
      .json({ success: true, message: "Candidate removed from event." });
  } catch (error) {
    console.error("Error deleting candidate:", error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

async function sendWebhookWithRetry(url, body, signature = null, attempt = 1) {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    if (signature) {
      headers["X-Signature"] = signature;
    }

    await axios.post(url, body, { headers });

    console.log(`✅ Webhook sent successfully to ${url} (Attempt ${attempt})`);
  } catch (err) {
    console.error(
      `❌ Failed to send webhook to ${url} (Attempt ${attempt}):`,
      err.message
    );

    if (attempt < 3) {
      const delay = Math.pow(2, attempt) * 1000;
      console.log(`🔁 Retrying in ${delay / 1000} seconds...`);

      setTimeout(() => {
        sendWebhookWithRetry(url, body, signature, attempt + 1);
      }, delay);
    } else {
      console.error(`🛑 Max retries reached. Giving up on ${url}`);
    }
  }
}
