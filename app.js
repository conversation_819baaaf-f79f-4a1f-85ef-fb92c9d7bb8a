const dotenv = require("dotenv");
dotenv.config({ path: "./.env" });
const fs = require("fs");
const initializeAccountDeletionCronJob = require("./cronjobs/accountDeletion");
const updateExpiredKeys = require("./cronjobs/updateExpiredKeys");
initializeAccountDeletionCronJob();
updateExpiredKeys();
const webhookRoutes = require("./routes/webhookRoutes.js");
const DBURL = process.env.DATABASE_URL;
const PORT = process.env.PORT;
const { createLog } = require("./controllers/logController.js");
const morgan = require("morgan");
const mongoose = require("mongoose");
mongoose.set("strictQuery", false);

const express = require("express");
const app = express();
const path = require("path");
const passport = require("passport");

const expressLayouts = require("express-ejs-layouts");
const session = require("express-session");
const cookieParser = require("cookie-parser");
const upload = require("express-fileupload");
const flash = require("connect-flash");
var i18n = require("i18n-express");
var bodyParser = require("body-parser");
const cors = require("cors");
const MongoStore = require("connect-mongo");

//importing models and routes
const pageRouter = require("./routes/routes");
const User = require("./models/User");
const initializePassport = require("./config/passportConfig");

const publicRoutes = require("./routes/publicRoutes"); //public api
const publicCertificateRoutes = require("./routes/publicCertificateRoutes"); //public certificate routes
const publicBadgeRoutes = require("./routes/publicBadgeRoutes"); //public badge routes
const publicProfileRoutes = require("./routes/publicProfileRoutes"); //public badge routes

const visitorLogger = require("./middleware/visitorLogger");
const useragent = require("express-useragent");
const Business = require("./models/Business.js");

require("./utility/checkUploadsDir");

// add the config.env to deploy local and prodtest separately

//impliment santizer using middleware

//URL configuration
//var urlencodeParser = bodyParser.urlencoded({ extended: true });
//app.use(urlencodeParser);

// Create a write stream of logs in append mode for the log using morgan

app.use("/api/webhooks", webhookRoutes);
const accessLogStream = fs.createWriteStream(
  path.join(__dirname, "access.log"),
  { flags: "a" }
);
// Use morgan to log requests to the file instead of the console
app.use(morgan("combined", { stream: accessLogStream }));

//mixcertificate internal analytics, removed as it recorded all hits than page load
// Use useragent middleware to parse user-agent header
app.use(useragent.express());

// Apply the visitorLogger middleware to all routes
//app.use(visitorLogger);

//for passport
app.use(bodyParser.urlencoded({ extended: false }));

//increase upload size
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ limit: "10mb", extended: true }));

//View Engine Configuration
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));

//Static Directory Setup
app.use(express.static(__dirname + "/public"));

//MIDDLEWARE

// Use CORS middleware
app.use(cors());

app.use(upload());

app.use(express.json());
app.use(
  session({
    secret: process.env.SECRET || "nodedemo",
    resave: false, // Don't save session if unmodified
    saveUninitialized: false, // Don't save uninitialized (new but unmodified) sessions
    store: MongoStore.create({
      mongoUrl: process.env.DATABASE_URL,
      ttl: 30 * 24 * 60 * 60, // Session expiration time in seconds (30 days)
    }),
  })
);

app.use(cookieParser());

//Layout confuguration
app.set("layout", "layouts/layout");
app.use(expressLayouts);

//Page Message Notification Configuration
app.use(flash());

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Passport config
initializePassport(passport);

// database connection
mongoose
  .connect(DBURL, {
    useNewUrlParser: true,
  })
  .then((con) => console.log("DB connection successfully..!"))
  .catch((err) => console.log("Error connecting DB", err));

// for i18 usr | localization
app.use(
  i18n({
    translationsPath: path.join(__dirname, "i18n"), // <--- use here. Specify translations files path.
    siteLangs: ["en", "ru", "it", "gr", "sp"],
    textsVarName: "translation",
  })
);

const isMobileDevice = (req) => {
  const userAgent = req.headers["user-agent"] || "";
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    userAgent.toLowerCase()
  );
};
const populateSessionAndRedirect = async (req, res, user) => {
  req.session.userid = user._id;
  req.session.username = user.displayName;
  req.session.useremail = user.email;

  req.session.save(async (err) => {
    if (err) {
      console.error("Session save error:", err);
      return res.redirect("/login");
    }
    const isUserOnBoarded = Boolean(user?.businessId);
    if (isUserOnBoarded) {
      try {
        await createLog(
          {
            eventType: "Authentication",
            action: "Login",
            target: user.email,
          },
          {
            user: {
              _id: user._id,
              businessId: user.businessId,
              role: user.role,
            },
            ip: req.ip,
            headers: req.headers["user-agent"] || "Unknown",
          },
          res
        );
      } catch (logErr) {
        console.error("Log creation failed:", logErr);
      }
    }
    let redirectPath;

    if (!isUserOnBoarded && isMobileDevice(req)) {
      redirectPath = "/branding";
    } else if (isUserOnBoarded) {
      redirectPath = "/dashboard";
    } else {
      redirectPath = "/onboard";
    }

    return res.redirect(redirectPath);
  });
};

app.get(
  "/auth/google",
  passport.authenticate("google", { scope: ["profile", "email"] })
);

app.get(
  "/auth/callback/google",
  passport.authenticate("google", { failureRedirect: "/login" }),
  function (req, res) {
    const user = req.user;
    if (!user) return res.redirect("/login");
    populateSessionAndRedirect(req, res, user);
  }
);

app.get(
  "/auth/microsoft",
  passport.authenticate("microsoft", {
    // prompt: "select_account",
    scope: ["user.read"],
  })
);
app.get(
  "/auth/callback/microsoft",
  passport.authenticate("microsoft", { failureRedirect: "/login" }),
  function (req, res) {
    const user = req.user;
    if (!user) return res.redirect("/login");
    populateSessionAndRedirect(req, res, user);
  }
);

//middleware
app.use((err, req, res, next) => {
  let error = { ...err };
  if (error.name === "JsonWebTokenError") {
    err.message = "please login again";
    err.statusCode = 401;
    return res.status(401).redirect("view/login");
  }
  err.statusCode = err.statusCode || 500;
  err.status = err.status || "errors";

  res.status(err.statusCode).json({
    status: err.status,
    message: err.message,
  });
});

// // Existing route in app.js
// app.get('/certificate-public1/', (req, res, next) => {
//     // Serve the certificate-public.html from the views directory
//     res.sendFile(path.join(__dirname, 'views', 'certificate-profile.html'));
// });

// // Serving the profile page from the views directory
// app.get('/certificate1/', (req, res, next) => {
//     res.sendFile(path.join(__dirname, 'views', 'certificate.html'));
// });

// app.use('/api/public', publicRoutes);

async function nameSpaceCodeMiddleware(req, res, next) {
  const { nameSpaceCode } = req.params;
 

  try {
    const business = await Business.findOne({ nameSpaceCode });
    if (!business) {
      return res.status(404).send("Business not found");
    }

  
    req.business = business;
    next();
  } catch (err) {
    next(err);
  }
}

app.use("/:nameSpaceCode/badge", nameSpaceCodeMiddleware, publicBadgeRoutes);
app.use(
  "/:nameSpaceCode/certificate",
  nameSpaceCodeMiddleware,
  publicCertificateRoutes
);
app.use(
  "/:nameSpaceCode/profile",
  nameSpaceCodeMiddleware,
  publicProfileRoutes
);
app.use("/:nameSpaceCode/public", nameSpaceCodeMiddleware, publicRoutes);

//home
app.get("/:nameSpaceCode/p/home", async(req, res) => {
   const business = await Business.findOne({ nameSpaceCode: req.params.nameSpaceCode });
   const businessId=business._id;
  res.render("public-home", {
    title: "Shine Events Certificates -  Home",
    layout: "layouts/publicPages",
    businessId,
  });
});

//certififcates
app.get("/:nameSpaceCode/p/about", (req, res) => {
  res.render("public-certificate", {
    title: "Shine Events Certificates -  Home",
    layout: "layouts/publicPages",
  });
});

//contacts
app.get("/:nameSpaceCode/p/contact", (req, res) => {
  res.render("public-certificate", {
    title: "Shine Events Certificates -  Home",
    layout: "layouts/publicPages",
  });
});

//certififcates
app.get("/:nameSpaceCode/p/certificates", (req, res) => {
  res.render("public-certificate", {
    title: "Certificates",
    layout: "layouts/publicPages",
  });
});

//badges
app.get("/:nameSpaceCode/p/badges", (req, res) => {
  res.render("public-badges", {
    title: "Badges",
    layout: "layouts/publicPages",
  });
});

/*  templates  */
app.get("/:nameSpaceCode/public/templates/", async (req, res, next) => {
  try {
    console.log("req.params.nameSpaceCode", req.params.nameSpaceCode);
    const response = await fetch(
      `${req.protocol}://${req.get("host")}/${
        req.params.nameSpaceCode
      }/public/api/templates?page=1&limit=8`
    );
    const data = await response.json();

    res.render("templates/templates", {
      title: "All Templates - MixCertificate",
      layout: "layouts/publicPageClient",
      ogImage:
        data.templates.length > 0
          ? data.templates[0].imageURL // Use the first template's image as OG image
          : "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
      ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
      ogDescription:
        "Browse and select from a wide range of certificate templates to suit your needs.",
    });
  } catch (error) {
    console.error("Error fetching templates:", error);
    res.render("templates/templates", {
      title: "All Templates - MixCertificate",
      layout: "layouts/publicPageClient",
      ogImage:
        data.templates.length > 0
          ? data.templates[0].imageURL // Use the first template's image as OG image
          : "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
      ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
      ogDescription:
        "Browse and select from a wide range of certificate templates to suit your needs.",
    });
  }
});

/*  templates category   */
app.get(
  "/:nameSpaceCode/public/templates-category/",
  async (req, res, next) => {
    try {
      const response = await fetch(
        `${req.protocol}://${req.get("host")}/${
          req.params.nameSpaceCode
        }/public/api/certificate-template?page=1&limit=8`
      );
      const data = await response.json();

      res.render("templates/templates-category", {
        title: " Templates Category - MixCertificate",
        layout: "layouts/publicPageClient",
        ogImage:
          data.templates.length > 0
            ? data.templates[0].imageURL // Use the first template's image as OG image
            : "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
        ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
        ogDescription:
          "Find certificate templates categorized for different purposes.",
      });
    } catch (error) {
      console.error("Error fetching templates:", error);
      res.render("templates/templates-category", {
        title: " Templates Category - MixCertificate",
        layout: "layouts/publicPageClient",
        ogImage:
          data.templates.length > 0
            ? data.templates[0].imageURL // Use the first template's image as OG image
            : "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
        ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
        ogDescription:
          "Find certificate templates categorized for different purposes.",
      });
    }
  }
);

/* single template page   */

app.get("/:nameSpaceCode/public/template/:id", async (req, res, next) => {
  try {
    const templateId = req.params.id;
    const response = await fetch(
      `${req.protocol}://${req.get("host")}/${
        req.params.nameSpaceCode
      }/public/api/template/${templateId}`
    );
    const data = await response.json();

    res.render("templates/templates-single", {
      title: " Single Template - MixCertificate",
      layout: "layouts/publicPageClient",
      ogImage:
        data?.imageURL || // Use the first template's image as OG image
        "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
      ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
      ogDescription:
        data?.description || "View this certificate template in detail.",
    });
  } catch (error) {
    console.error("Error fetching templates:", error);
    res.render("templates/templates-single", {
      title: " Single Template - MixCertificate",
      layout: "layouts/publicPageClient",
      ogImage:
        data?.imageURL || // Use the first template's image as OG image
        "/assets/images/certificate/sample-certificate-horizontal.jpg", // Fallback image
      ogUrl: `${req.protocol}://${req.get("host")}${req.originalUrl}`, // Full URL
      ogDescription:
        data?.description || "View this certificate template in detail.",
    });
  }
});
//reset password
app.get("/resetpswdview/", (req, res) => {
  console.log("req.query.token", req.query.token);
  res.render("auth/resetpassword", {
    title: "Reset Password",
    layout: "layouts/publicPages",
    token: req.query.token,
  });
});

//add public 404 page for each public route

// Add more routes as needed

// Define All Route
pageRouter(app);

//404 page when page not found
app.all("*", function (req, res) {
  res.locals = { title: "Error 404" };
  res.render("auth/auth-404", { layout: "layouts/layout-without-nav" });
});

//start the server
app.listen(PORT, () => console.log(`Server started on port ${PORT}`));
