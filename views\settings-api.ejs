<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", { "title":"API Settings" }) %>
<style>
  .api-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
  }

  .api-header {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    padding: 30px;
    margin-bottom: 30px;
  }

  .api-header h2 {
    color: #5156be;
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 28px;
  }

  .api-header p {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
  }

  .api-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .stat-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    text-align: center;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(81, 86, 190, 0.15);
  }

  .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #5156be;
    margin-bottom: 8px;
  }

  .stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .api-keys-section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
  }

  .section-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
  }

  .btn-primary-custom {
    background: #ffffff;
    color: #5156be;
    border: 2px solid #ffffff;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }

  .btn-primary-custom:hover {
    background: #f8f9fa;
    color: #5156be;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
  }

  .btn-danger-custom {
    background: #dc3545;
    color: #ffffff;
    border: none;
    padding: 4px 6px;
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 11px;
    margin: 1px;
    min-width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-danger-custom:hover {
    background: #c82333;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-edit-custom {
    background: #28a745;
    color: #ffffff;
    border: none;
    padding: 4px 6px;
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 11px;
    margin: 1px;
    min-width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-edit-custom:hover {
    background: #218838;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-revoke-custom {
    background: #fd7e14;
    color: #ffffff;
    border: none;
    padding: 4px 6px;
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 11px;
    margin: 1px;
    min-width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-revoke-custom:hover {
    background: #e8650e;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .actions-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 1px;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
  }

  .api-keys-table {
    width: 100%;
    margin: 0;
    table-layout: fixed;
  }

  .api-keys-table thead {
    background: #f8f9fa;
  }

  .api-keys-table th {
    padding: 15px 10px;
    font-weight: 600;
    color: #495057;
    border: none;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .api-keys-table td {
    padding: 15px 10px;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
    word-wrap: break-word;
    overflow: hidden;
  }

  /* Column width distribution */
  .api-keys-table th:nth-child(1), /* Name */
  .api-keys-table td:nth-child(1) {
    width: 15%;
    min-width: 120px;
  }

  .api-keys-table th:nth-child(2), /* API Key */
  .api-keys-table td:nth-child(2) {
    width: 18%;
    min-width: 140px;
  }

  .api-keys-table th:nth-child(3), /* Scopes */
  .api-keys-table td:nth-child(3) {
    width: 22%;
    min-width: 160px;
  }

  .api-keys-table th:nth-child(4), /* Last Used */
  .api-keys-table td:nth-child(4) {
    width: 12%;
    min-width: 90px;
  }

  .api-keys-table th:nth-child(5), /* Expires */
  .api-keys-table td:nth-child(5) {
    width: 12%;
    min-width: 90px;
  }

  .api-keys-table th:nth-child(6), /* Status */
  .api-keys-table td:nth-child(6) {
    width: 10%;
    min-width: 70px;
  }

  .api-keys-table th:nth-child(7), /* Actions */
  .api-keys-table td:nth-child(7) {
    width: 11%;
    min-width: 100px;
  }

  .api-key-name {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
  }

  .api-key-value {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 0; /* Allow shrinking */
    overflow: hidden;
  }

  .api-key-value span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    word-break: break-all;
    overflow-wrap: break-word;
  }

  /* Special styling for modal API key display */
  .modal-body .api-key-value {
    max-width: 100%;
  }

  .modal-body .api-key-value span {
    white-space: normal;
    word-break: break-all;
    overflow-wrap: break-word;
    text-overflow: initial;
    overflow: visible;
  }

  .api-key-hidden {
    color: #6c757d;
    font-style: italic;
  }

  .api-key-actions {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .copy-btn,
  .eye-btn {
    background: none;
    border: none;
    color: #5156be;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
  }

  .copy-btn:hover,
  .eye-btn:hover {
    background: #5156be;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .eye-btn {
    color: #28a745;
  }

  .eye-btn:hover {
    background: #28a745;
    color: #ffffff;
  }

  /* OTP Modal Styles */
  #otpCode {
    font-size: 1.2rem;
    letter-spacing: 0.2rem;
    font-weight: 600;
  }

  .font-monospace {
    font-family: "Courier New", Courier, monospace !important;
    font-size: 0.9rem;
  }

  /* Timer styles */
  #otpTimer,
  #accessTimer {
    font-weight: 600;
    color: #dc3545;
  }

  /* Security icons */
  .fa-shield-alt,
  .fa-lock {
    color: #5156be;
  }

  /* Modal header colors */
  .modal-header {
    border-bottom: none;
  }

  /* Alert improvements */
  .alert {
    border: none;
    border-radius: 8px;
  }

  .alert-info {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  .alert-warning {
    background-color: #fff3cd;
    color: #856404;
  }

  .alert-success {
    background-color: #d4edda;
    color: #155724;
  }

  .alert-danger {
    background-color: #f8d7da;
    color: #721c24;
  }

  /* Details Modal Secure Section */
  #secureKeySection {
    border: 2px solid #28a745;
    border-radius: 8px;
    padding: 15px;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
  }

  #secureKeySection .alert-success {
    margin-bottom: 15px;
    border: none;
  }

  #secureKeySection .alert-warning {
    margin-bottom: 0;
    border: none;
  }

  #detailsSecureKeyValue {
    background: #ffffff;
    border: 1px solid #28a745;
  }

  #detailsAccessTimer {
    font-weight: 600;
    color: #dc3545;
  }

  .scope-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    margin: 1px;
    display: inline-block;
    white-space: nowrap;
  }

  .scopes-container {
    max-height: 60px;
    overflow-y: auto;
    line-height: 1.2;
  }

  .scopes-container::-webkit-scrollbar {
    width: 4px;
  }

  .scopes-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .scopes-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  .scopes-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    white-space: nowrap;
    display: inline-block;
    min-width: 50px;
    text-align: center;
    border: 1px solid transparent;
  }

  .status-active {
    background: #d1edff;
    color: #0c5460;
    border-color: #bee5eb;
  }

  .status-expired {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
  }

  .status-revoked {
    background: #ffeaa7;
    color: #856404;
    border-color: #ffd93d;
  }

  .status-never {
    background: #e2e3e5;
    color: #6c757d;
    border-color: #ced4da;
  }

  .empty-state {
    text-align: center;
    padding: 60px 30px;
    color: #6c757d;
  }

  .empty-state i {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
  }

  .empty-state h3 {
    color: #495057;
    margin-bottom: 10px;
  }

  .pagination-container {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .pagination-info {
    color: #6c757d;
    font-size: 14px;
  }

  .pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .pagination-btn {
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .pagination-btn:hover:not(.disabled) {
    background: #5156be;
    color: #ffffff;
    border-color: #5156be;
  }

  .pagination-btn.active {
    background: #5156be;
    color: #ffffff;
    border-color: #5156be;
  }

  .pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Modal Styles */
  .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    border-radius: 12px 12px 0 0;
    padding: 25px 30px;
    border-bottom: none;
  }

  .modal-title {
    font-weight: 600;
    font-size: 20px;
    color: #ffffff;
  }

  .btn-close {
    background: none;
    border: none;
    color: #ffffff;
    opacity: 0.8;
    font-size: 24px;
  }

  .btn-close:hover {
    opacity: 1;
    color: #ffffff;
  }

  .modal-body {
    padding: 30px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
  }

  .form-control-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
  }

  .form-control-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .form-select-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
    background: #ffffff;
  }

  .form-select-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .scope-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
  }

  .scope-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
  }

  .scope-item:hover {
    border-color: #5156be;
    background: #f0f0ff;
  }

  .scope-item input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
  }

  .scope-item label {
    margin: 0;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
  }

  .modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
  }

  .btn-modal-primary {
    background: #5156be;
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-modal-primary:hover {
    background: #4c51b8;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-modal-secondary {
    background: #6c757d;
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 10px;
  }

  .btn-modal-secondary:hover {
    background: #5a6268;
    color: #ffffff;
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #5156be;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .alert-custom {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .alert-success-custom {
    background: #d1edff;
    color: #155724;
    border-left: 4px solid #28a745;
  }

  .alert-error-custom {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .api-keys-table th:nth-child(4), /* Last Used */
    .api-keys-table td:nth-child(4) {
      display: none;
    }
  }

  @media (max-width: 992px) {
    .api-keys-table th:nth-child(5), /* Expires */
    .api-keys-table td:nth-child(5) {
      display: none;
    }

    .api-keys-table th:nth-child(3), /* Scopes */
    .api-keys-table td:nth-child(3) {
      width: 30%;
    }
  }

  @media (max-width: 768px) {
    .api-header {
      padding: 20px;
      text-align: center;
    }

    .section-header {
      flex-direction: column;
      text-align: center;
    }

    .api-keys-table {
      font-size: 12px;
    }

    .api-keys-table th,
    .api-keys-table td {
      padding: 10px 5px;
    }

    .api-key-value {
      font-size: 11px;
    }

    .pagination-container {
      flex-direction: column;
      text-align: center;
    }

    .scope-selection {
      grid-template-columns: 1fr;
    }

    .modal-body {
      padding: 20px;
    }

    .api-keys-table th:nth-child(3), /* Scopes */
    .api-keys-table td:nth-child(3) {
      display: none;
    }

    /* Adjust column widths for mobile */
    .api-keys-table th:nth-child(1), /* Name */
    .api-keys-table td:nth-child(1) {
      width: 25%;
    }

    .api-keys-table th:nth-child(2), /* API Key */
    .api-keys-table td:nth-child(2) {
      width: 35%;
    }

    .api-keys-table th:nth-child(6), /* Status */
    .api-keys-table td:nth-child(6) {
      width: 20%;
    }

    .api-keys-table th:nth-child(7), /* Actions */
    .api-keys-table td:nth-child(7) {
      width: 20%;
    }
  }

  @media (max-width: 576px) {
    .api-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .stat-card {
      padding: 15px;
    }

    .api-keys-table th:nth-child(6), /* Status */
    .api-keys-table td:nth-child(6) {
      display: none;
    }

    /* Keep action buttons in one line but make them smaller */
    .actions-container {
      flex-direction: row;
      flex-wrap: nowrap;
      gap: 1px;
      justify-content: center;
    }

    .btn-edit-custom,
    .btn-revoke-custom,
    .btn-danger-custom {
      min-width: 24px;
      height: 24px;
      font-size: 9px;
      padding: 2px 4px;
    }

    .api-keys-table th:nth-child(7), /* Actions */
    .api-keys-table td:nth-child(7) {
      width: 25%;
      min-width: 80px;
    }
  }

  /* Table container with horizontal scroll for very small screens */
  @media (max-width: 480px) {
    .api-keys-section {
      overflow-x: auto;
    }

    .api-keys-table {
      min-width: 500px;
    }

    .btn-edit-custom,
    .btn-revoke-custom,
    .btn-danger-custom {
      min-width: 20px;
      height: 20px;
      font-size: 8px;
      padding: 1px 2px;
    }

    .actions-container {
      gap: 0px;
    }

    .status-badge {
      font-size: 8px;
      padding: 2px 4px;
      min-width: 40px;
    }
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title",{"title": "API Settings" , "pagetitle": "Settings" }) %>

<div
  class="d-flex justify-content-between align-items-center p-3 border-start border-4 border-primary rounded bg-light shadow-sm mt-3"
>
  <span class="fw-semibold text-dark mb-0">Need help?</span>
  <a
    href="https://help.mixcommerce.co/docs-category/api/"
    class="btn btn-primary btn-sm"
    target="_blank"
  >
    Visit Docs →
  </a>
</div>

<div class="api-container">
  <div class="container-fluid">
    <!-- API Header -->
    <div class="api-header">
      <h2><i class="fas fa-key me-2"></i>API Key Management</h2>
      <p>Create and manage API keys to integrate with your applications</p>
    </div>

    <!-- API Statistics -->
    <div class="api-stats">
      <div class="stat-card">
        <div class="stat-number" id="totalKeysCount">0</div>
        <div class="stat-label">Total Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="activeKeysCount">0</div>
        <div class="stat-label">Active Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="expiredKeysCount">0</div>
        <div class="stat-label">Expired Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="revokedKeysCount">0</div>
        <div class="stat-label">Revoked Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="lastUsedCount">0</div>
        <div class="stat-label">Recently Used</div>
      </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- API Keys Section -->
    <div class="api-keys-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="fas fa-list me-2"></i>Your API Keys
        </h3>
        <button class="btn-primary-custom" onclick="openCreateModal()">
          <i class="fas fa-plus"></i>
          Create New Key
        </button>
      </div>

      <!-- Loading State -->
      <div id="loadingState" class="text-center p-5" style="display: none">
        <div
          class="loading-spinner"
          style="width: 40px; height: 40px; border-width: 4px"
        ></div>
        <p class="mt-3 text-muted">Loading API keys...</p>
      </div>

      <!-- API Keys Table -->
      <div id="apiKeysTableContainer">
        <table class="api-keys-table table table-hover">
          <thead>
            <tr>
              <th>Name</th>
              <th>API Key</th>
              <th>Scopes</th>
              <th>Last Used</th>
              <th>Expires</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="apiKeysTableBody">
            <!-- API keys will be loaded here -->
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div id="emptyState" class="empty-state" style="display: none">
        <i class="fas fa-key"></i>
        <h3>No API Keys Found</h3>
        <p>You haven't created any API keys yet</p>
        <!-- <button class="btn-primary-custom" onclick="openCreateModal()" style="background: #5156be; color: #ffffff; margin-top: 20px;">
          <i class="fas fa-plus"></i>
          Create Your First API Key
        </button> -->
      </div>

      <!-- Pagination -->
      <div
        class="pagination-container"
        id="paginationContainer"
        style="display: none"
      >
        <div class="pagination-info">
          <span id="paginationInfo">Showing 0 of 0 results</span>
        </div>
        <div class="pagination-controls" id="paginationControls">
          <!-- Pagination buttons will be generated here -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit API Key Modal -->
<div
  class="modal fade"
  id="apiKeyModal"
  tabindex="-1"
  aria-labelledby="apiKeyModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="apiKeyModalLabel">Create New API Key</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <form id="apiKeyForm">
        <div class="modal-body">
          <div class="form-group">
            <label for="keyName" class="form-label">API Key Name *</label>
            <input
              type="text"
              class="form-control-custom"
              id="keyName"
              name="name"
              required
              placeholder="e.g., Production App, Mobile App, Testing"
            />
            <small class="text-muted"
              >Choose a descriptive name to identify this API key</small
            >
          </div>

          <div class="form-group">
            <label for="expiresAt" class="form-label">Expiration Date</label>
            <select class="form-select-custom" id="expiresAt" name="expiresAt">
              <option value="">Never expires</option>
              <option value="30">30 days</option>
              <option value="90">90 days</option>
              <option value="180">6 months</option>
              <option value="365">1 year</option>
              <option value="custom">Custom date</option>
            </select>
          </div>

          <div class="form-group" id="customDateGroup" style="display: none">
            <label for="customExpiryDate" class="form-label"
              >Custom Expiry Date</label
            >
            <input
              type="date"
              class="form-control-custom"
              id="customExpiryDate"
              name="customExpiryDate"
            />
          </div>

          <div class="form-group">
            <label class="form-label">API Scopes *</label>
            <small class="text-muted d-block mb-3"
              >Select the permissions this API key should have</small
            >
            <div class="scope-selection" id="scopeSelection">
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_certificates_read"
                  name="scopes"
                  value="certificates:read"
                />
                <label for="scope_certificates_read">Read Certificates</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_certificates_create"
                  name="scopes"
                  value="certificates:create"
                />
                <label for="scope_certificates_create"
                  >Create Certificates</label
                >
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_certificates_update"
                  name="scopes"
                  value="certificates:update"
                />
                <label for="scope_certificates_update"
                  >Update Certificates</label
                >
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_certificates_delete"
                  name="scopes"
                  value="certificates:delete"
                />
                <label for="scope_certificates_delete"
                  >Delete Certificates</label
                >
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_badges_read"
                  name="scopes"
                  value="badges:read"
                />
                <label for="scope_badges_read">Read Badges</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_badges_create"
                  name="scopes"
                  value="badges:create"
                />
                <label for="scope_badges_create">Create Badges</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_contacts_read"
                  name="scopes"
                  value="contacts:read"
                />
                <label for="scope_contacts_read">Read Contacts</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_contacts_create"
                  name="scopes"
                  value="contacts:create"
                />
                <label for="scope_contacts_create">Create Contacts</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_events_read"
                  name="scopes"
                  value="events:read"
                />
                <label for="scope_events_read">Read Events</label>
              </div>
              <div class="scope-item">
                <input
                  type="checkbox"
                  id="scope_events_create"
                  name="scopes"
                  value="events:create"
                />
                <label for="scope_events_create">Create Events</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn-modal-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="submit" class="btn-modal-primary" id="saveApiKeyBtn">
            <span id="saveApiKeyText">Create API Key</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div
  class="modal fade"
  id="deleteConfirmModal"
  tabindex="-1"
  aria-labelledby="deleteConfirmModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background: #dc3545">
        <h5
          class="modal-title"
          id="deleteConfirmModalLabel"
          style="color: #ffffff"
        >
          <i class="fas fa-exclamation-triangle me-2"></i>Delete API Key
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
          style="filter: invert(1)"
        ></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to delete this API key?</p>
        <div class="alert alert-warning">
          <strong>Warning:</strong> This action cannot be undone. Any
          applications using this API key will lose access immediately.
        </div>
        <div class="bg-light p-3 rounded">
          <strong>API Key Name:</strong> <span id="deleteKeyName"></span><br />
          <strong>Created:</strong> <span id="deleteKeyCreated"></span>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn-modal-secondary"
          data-bs-dismiss="modal"
        >
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span id="deleteButtonText">Delete API Key</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Revoke Confirmation Modal -->
<div
  class="modal fade"
  id="revokeConfirmModal"
  tabindex="-1"
  aria-labelledby="revokeConfirmModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background: #fd7e14">
        <h5
          class="modal-title"
          id="revokeConfirmModalLabel"
          style="color: #ffffff"
        >
          <i class="fas fa-ban me-2"></i>Revoke API Key
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
          style="filter: invert(1)"
        ></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to revoke this API key?</p>
        <div class="alert alert-warning">
          <strong>Warning:</strong> This action will immediately disable the API
          key. Applications using this key will lose access, but the key can
          still be viewed for reference.
        </div>
        <div class="bg-light p-3 rounded">
          <strong>API Key Name:</strong> <span id="revokeKeyName"></span><br />
          <strong>Created:</strong> <span id="revokeKeyCreated"></span>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn-modal-secondary"
          data-bs-dismiss="modal"
        >
          Cancel
        </button>
        <button type="button" class="btn btn-warning" id="confirmRevokeBtn">
          <span id="revokeButtonText">Revoke API Key</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- API Key Details Modal -->
<div
  class="modal fade"
  id="apiKeyDetailsModal"
  tabindex="-1"
  aria-labelledby="apiKeyDetailsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="apiKeyDetailsModalLabel">
          <i class="fas fa-info-circle me-2"></i>API Key Details
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Name</label>
              <div
                class="form-control-custom"
                id="detailsKeyName"
                style="background: #f8f9fa"
              ></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Status</label>
              <div id="detailsKeyStatus"></div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">API Key</label>
          <div class="api-key-value">
            <span id="detailsKeyValue"
              >••••••••••••••••••••••••••••••••••••••••</span
            >
            <div class="api-key-actions">
              <button
                class="eye-btn"
                onclick="secureViewApiKeyFromDetails()"
                title="View API Key"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button
                class="copy-btn"
                onclick="secureCopyApiKeyFromDetails()"
                title="Copy to clipboard"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>

          <!-- Secure Key Display Section (Hidden by default) -->
          <div id="secureKeySection" class="mt-3 d-none">
            <div class="alert alert-success">
              <i class="fas fa-check-circle me-2"></i>
              <strong>Identity Verified!</strong> You can now view and copy your
              API key.
            </div>

            <div class="input-group">
              <input
                type="text"
                class="form-control font-monospace"
                id="detailsSecureKeyValue"
                readonly
              />
              <button
                class="btn btn-outline-primary"
                type="button"
                onclick="copyDetailsSecureApiKey()"
                title="Copy to clipboard"
              >
                <i class="fas fa-copy me-1"></i>Copy
              </button>
            </div>

            <div class="alert alert-warning mt-2">
              <i class="fas fa-clock me-2"></i>
              <strong>Access expires in:</strong>
              <span id="detailsAccessTimer">59:59</span> minutes
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Created</label>
              <div
                class="form-control-custom"
                id="detailsKeyCreated"
                style="background: #f8f9fa"
              ></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Last Used</label>
              <div
                class="form-control-custom"
                id="detailsKeyLastUsed"
                style="background: #f8f9fa"
              ></div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Expires</label>
          <div
            class="form-control-custom"
            id="detailsKeyExpires"
            style="background: #f8f9fa"
          ></div>
        </div>

        <div class="form-group">
          <label class="form-label">Scopes</label>
          <div id="detailsKeyScopes"></div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn-modal-secondary"
          data-bs-dismiss="modal"
        >
          Close
        </button>
        <button type="button" class="btn btn-primary" onclick="editApiKey()">
          <i class="fas fa-edit"></i> Edit
        </button>
      </div>
    </div>
  </div>
</div>

<!-- OTP Verification Modal -->
<div
  class="modal fade"
  id="otpVerificationModal"
  tabindex="-1"
  aria-labelledby="otpVerificationModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background: #5156be">
        <h5
          class="modal-title"
          id="otpVerificationModalLabel"
          style="color: #ffffff"
        >
          <i class="fas fa-shield-alt me-2"></i>Security Verification
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="text-center mb-4">
          <i class="fas fa-lock fa-3x text-primary mb-3"></i>
          <h6>Verify Your Identity</h6>
          <p class="text-muted">
            For security reasons, we need to verify your identity before showing
            the API key.
          </p>
        </div>

        <form id="otpVerificationForm">
          <div class="form-group mb-3">
            <label for="userEmail" class="form-label">Your Email Address</label>
            <input
              type="email"
              class="form-control"
              id="userEmail"
              name="email"
              required
              placeholder="Enter your email address"
            />
            <div class="form-text">
              We'll send a verification code to this email
            </div>
          </div>

          <div id="otpInputSection" class="d-none">
            <div class="form-group mb-3">
              <label for="otpCode" class="form-label">Verification Code</label>
              <input
                type="text"
                class="form-control text-center"
                id="otpCode"
                name="otp"
                placeholder="Enter 6-digit code"
                maxlength="6"
                pattern="[0-9]{6}"
              />
              <div class="form-text">
                Enter the 6-digit code sent to your email
              </div>
            </div>

            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              <small
                >Code expires in <span id="otpTimer">30:00</span> minutes</small
              >
            </div>
          </div>

          <div id="otpError" class="alert alert-danger d-none"></div>
          <div id="otpSuccess" class="alert alert-success d-none"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-primary"
          id="sendOtpBtn"
          onclick="sendOtpCode()"
        >
          <span
            id="sendOtpSpinner"
            class="spinner-border spinner-border-sm d-none me-2"
          ></span>
          <i class="fas fa-paper-plane me-1"></i>
          Send Code
        </button>
        <button
          type="button"
          class="btn btn-success d-none"
          id="verifyOtpBtn"
          onclick="verifyOtpCode()"
        >
          <span
            id="verifyOtpSpinner"
            class="spinner-border spinner-border-sm d-none me-2"
          ></span>
          <i class="fas fa-check me-1"></i>
          Verify Code
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Secure API Key Display Modal -->
<div
  class="modal fade"
  id="secureApiKeyModal"
  tabindex="-1"
  aria-labelledby="secureApiKeyModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header" style="background: #28a745">
        <h5
          class="modal-title"
          id="secureApiKeyModalLabel"
          style="color: #ffffff"
        >
          <i class="fas fa-key me-2"></i>API Key Access Granted
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-success">
          <i class="fas fa-check-circle me-2"></i>
          <strong>Identity Verified!</strong> You can now view and copy your API
          key.
        </div>

        <div class="form-group mb-3">
          <label class="form-label">API Key Name</label>
          <div
            class="form-control-custom"
            id="secureKeyName"
            style="background: #f8f9fa"
          ></div>
        </div>

        <div class="form-group mb-3">
          <label class="form-label">API Key Value</label>
          <div class="input-group">
            <input
              type="text"
              class="form-control font-monospace"
              id="secureKeyValue"
              readonly
            />
            <button
              class="btn btn-outline-primary"
              type="button"
              onclick="copySecureApiKey()"
              title="Copy to clipboard"
            >
              <i class="fas fa-copy me-1"></i>Copy
            </button>
          </div>
        </div>

        <div class="alert alert-warning">
          <i class="fas fa-clock me-2"></i>
          <strong>Access expires in:</strong>
          <span id="accessTimer">59:59</span> minutes
          <br />
          <small
            >After this time, you'll need to verify your identity again to view
            the API key.</small
          >
        </div>

        <div class="alert alert-info">
          <i class="fas fa-shield-alt me-2"></i>
          <strong>Security Notice:</strong> Keep your API key secure and never
          share it publicly. This key provides access to your account and should
          be treated like a password.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
        <button
          type="button"
          class="btn btn-primary"
          onclick="copySecureApiKey()"
        >
          <i class="fas fa-copy me-1"></i>Copy Key
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<script>
  // Global variables - Declare in window scope to avoid conflicts
  window.currentPage = 1;
  window.totalPages = 1;
  window.currentApiKeys = [];
  window.editingKeyId = null;
  window.deletingKeyId = null;
  window.revokingKeyId = null;
  window.currentKeyDetails = null;

  // ===== GLOBAL FUNCTIONS (Called from HTML) =====

  // Test function to verify global access
  window.testFunction = function () {
    console.log("Test function called successfully!");
    alert("All functions are working correctly!");
  };

  // Open create modal - Global function
  window.openCreateModal = function () {
    window.editingKeyId = null;
    $("#apiKeyModalLabel").text("Create New API Key");
    $("#saveApiKeyText").text("Create API Key");
    resetForm();
    $("#apiKeyModal").modal("show");
  };

  // Load API keys with pagination - Global function
  window.loadApiKeys = function (page = 1) {
    window.currentPage = page;
    showLoading(true);

    $.ajax({
      url: `/api/keys?page=${page}&limit=10`,
      method: "GET",
      success: function (response) {
        window.currentApiKeys = response.data;
        renderApiKeys(response.data);
        renderPagination(response.pagination);
        showLoading(false);
        // Reload stats after loading keys to ensure they're up to date
        loadApiKeyStats();
      },
      error: function (xhr) {
        showLoading(false);
        showToast(
          "Error loading API keys: " +
            (xhr.responseJSON?.message || "Unknown error"),
          "danger"
        );
      },
    });
  };

  // View API key details - Global function
  window.viewApiKeyDetails = function (keyId) {
    const apiKey = window.currentApiKeys.find((key) => key._id === keyId);
    if (!apiKey) {
      showToast("API key not found", "danger");
      return;
    }

    window.currentKeyDetails = apiKey;

    // Populate modal
    $("#detailsKeyName").text(apiKey.name);
    $("#detailsKeyValue").text("••••••••••••••••••••••••••••••••••••••••");
    $("#detailsKeyCreated").text(formatDate(apiKey.createdAt));
    $("#detailsKeyLastUsed").text(
      apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : "Never"
    );
    $("#detailsKeyExpires").text(
      apiKey.expiresAt ? formatDate(apiKey.expiresAt) : "Never"
    );

    // Status
    let statusHtml;
    if (apiKey.status === "revoked") {
      statusHtml = '<span class="status-badge status-revoked">Revoked</span>';
    } else if (
      apiKey.status === "expired" ||
      (apiKey.expiresAt && new Date(apiKey.expiresAt) <= new Date())
    ) {
      statusHtml = '<span class="status-badge status-expired">Expired</span>';
    } else {
      statusHtml = '<span class="status-badge status-active">Active</span>';
    }
    $("#detailsKeyStatus").html(statusHtml);

    // Scopes
    const scopesHtml = apiKey.scopes
      .map((scope) => `<span class="scope-badge">${scope}</span>`)
      .join(" ");
    $("#detailsKeyScopes").html(scopesHtml);

    // Reset secure section
    $("#secureKeySection").addClass("d-none");
    $("#detailsSecureKeyValue").val("");

    // Clear any existing timers
    if (window.detailsAccessTimer) {
      clearInterval(window.detailsAccessTimer);
      window.detailsAccessTimer = null;
    }

    $("#apiKeyDetailsModal").modal("show");
  };

  // Edit API key - Global function
  window.editApiKey = function () {
    if (!window.currentKeyDetails) return;

    window.editingKeyId = window.currentKeyDetails._id;
    $("#apiKeyDetailsModal").modal("hide");

    // Populate form
    $("#keyName").val(window.currentKeyDetails.name);

    // Set scopes
    $('input[name="scopes"]').prop("checked", false);
    window.currentKeyDetails.scopes.forEach((scope) => {
      $(`input[name="scopes"][value="${scope}"]`).prop("checked", true);
    });

    // Set expiry
    if (window.currentKeyDetails.expiresAt) {
      const expiryDate = new Date(window.currentKeyDetails.expiresAt);
      const now = new Date();
      const diffDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));

      if ([30, 90, 180, 365].includes(diffDays)) {
        $("#expiresAt").val(diffDays.toString());
      } else {
        $("#expiresAt").val("custom");
        $("#customExpiryDate").val(expiryDate.toISOString().split("T")[0]);
        $("#customDateGroup").show();
      }
    } else {
      $("#expiresAt").val("");
    }

    $("#apiKeyModalLabel").text("Edit API Key");
    $("#saveApiKeyText").text("Update API Key");
    $("#apiKeyModal").modal("show");
  };

  // Confirm delete - Global function
  window.confirmDelete = function (keyId, keyName) {
    window.deletingKeyId = keyId;
    const apiKey = window.currentApiKeys.find((key) => key._id === keyId);

    $("#deleteKeyName").text(keyName);
    $("#deleteKeyCreated").text(
      apiKey ? formatDate(apiKey.createdAt) : "Unknown"
    );
    $("#deleteConfirmModal").modal("show");
  };

  // Confirm revoke - Global function
  window.confirmRevoke = function (keyId, keyName) {
    window.revokingKeyId = keyId;
    const apiKey = window.currentApiKeys.find((key) => key._id === keyId);

    $("#revokeKeyName").text(keyName);
    $("#revokeKeyCreated").text(
      apiKey ? formatDate(apiKey.createdAt) : "Unknown"
    );
    $("#revokeConfirmModal").modal("show");
  };

  // Toggle key visibility - Global function
  window.toggleKeyVisibility = function (context) {
    const valueElement = context === "details" ? $("#detailsKeyValue") : null;
    const iconElement = context === "details" ? $("#detailsToggleIcon") : null;

    if (!valueElement || !window.currentKeyDetails) return;

    if (iconElement.hasClass("fa-eye")) {
      // Show key
      valueElement.text(window.currentKeyDetails.key);
      iconElement.removeClass("fa-eye").addClass("fa-eye-slash");
    } else {
      // Hide key
      valueElement.text("••••••••••••••••••••••••••••••••••••••••");
      iconElement.removeClass("fa-eye-slash").addClass("fa-eye");
    }
  };

  // Copy to clipboard - Global function
  window.copyToClipboard = function (text) {
    navigator.clipboard
      .writeText(text)
      .then(function () {
        showToast("API key copied to clipboard!");
      })
      .catch(function () {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        showToast("API key copied to clipboard!");
      });
  };

  // ===== INITIALIZATION =====

  // Initialize page
  $(document).ready(function () {
    window.loadApiKeys();
    loadApiKeyStats();
    setupEventListeners();
  });

  // Setup event listeners
  function setupEventListeners() {
    // Form submission
    $("#apiKeyForm").on("submit", function (e) {
      e.preventDefault();
      saveApiKey();
    });

    // Expiry date change
    $("#expiresAt").on("change", function () {
      if ($(this).val() === "custom") {
        $("#customDateGroup").show();
        $("#customExpiryDate").attr("required", true);
      } else {
        $("#customDateGroup").hide();
        $("#customExpiryDate").attr("required", false);
      }
    });

    // Delete confirmation
    $("#confirmDeleteBtn").on("click", function () {
      deleteApiKey();
    });

    // Revoke confirmation
    $("#confirmRevokeBtn").on("click", function () {
      revokeApiKey();
    });

    // Modal events
    $("#apiKeyModal").on("hidden.bs.modal", function () {
      resetForm();
    });
  }

  // Load API key statistics
  function loadApiKeyStats() {
    $.ajax({
      url: "/api/keys/all/stats",
      method: "GET",
      success: function (response) {
        console.log("API Stats Response:", response); // Debug log
        updateStatisticsFromAPI(response);
      },
      error: function (xhr) {
        console.error(
          "Error loading API key stats:",
          xhr.responseJSON?.message || "Unknown error"
        );
        // Fallback to showing zeros
        updateStatisticsFromAPI({
          totalKeys: 0,
          activeKeys: 0,
          expiredKeys: 0,
          revokedKeys: 0,
          recentlyUsed: 0,
        });
      },
    });
  }

  // Render API keys table
  function renderApiKeys(apiKeys) {
    const tbody = $("#apiKeysTableBody");
    tbody.empty();

    if (apiKeys.length === 0) {
      $("#apiKeysTableContainer").hide();
      $("#emptyState").show();
      $("#paginationContainer").hide();
      return;
    }

    $("#emptyState").hide();
    $("#apiKeysTableContainer").show();

    apiKeys.forEach((key) => {
      const row = createApiKeyRow(key);
      tbody.append(row);
    });
  }

  // Create API key table row
  function createApiKeyRow(key) {
    // Determine status based on backend status field
    let statusClass, statusText;
    if (key.status === "revoked") {
      statusClass = "status-revoked";
      statusText = "Revoked";
    } else if (
      key.status === "expired" ||
      (key.expiresAt && new Date(key.expiresAt) < new Date())
    ) {
      statusClass = "status-expired";
      statusText = "Expired";
    } else {
      statusClass = "status-active";
      statusText = "Active";
    }

    const lastUsed = key.lastUsedAt
      ? formatDate(key.lastUsedAt)
      : '<span class="status-never">Never</span>';

    const expires = key.expiresAt
      ? formatDate(key.expiresAt)
      : '<span class="text-muted">Never</span>';

    const scopes = key.scopes
      .map((scope) => `<span class="scope-badge">${scope}</span>`)
      .join(" ");

    return `
        <tr>
            <td>
                <div class="api-key-name">${escapeHtml(key.name)}</div>
            </td>
            <td>
                <div class="api-key-value">
                    <span class="api-key-hidden">••••••••••••••••••••••••••••••••••••••••</span>
                    <div class="api-key-actions">
                        <button class="eye-btn" onclick="secureViewApiKey('${
                          key._id
                        }')" title="View API Key">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="copy-btn" onclick="secureCopyApiKey('${
                          key._id
                        }')" title="Copy to clipboard">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </td>
            <td>
                <div class="scopes-container">
                    ${scopes}
                </div>
            </td>
            <td>${lastUsed}</td>
            <td>${expires}</td>
            <td>
                <span class="status-badge ${statusClass}">${statusText}</span>
            </td>
            <td>
                <div class="actions-container gap-1">
                    <button class="btn btn-primary" onclick="viewApiKeyDetails('${
                      key._id
                    }')" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${
                      key.status !== "revoked"
                        ? `
                        <button class="btn btn-warning" onclick="confirmRevoke('${
                          key._id
                        }', '${escapeHtml(key.name)}')" title="Revoke Key">
                            <i class="fas fa-ban"></i>
                        </button>
                    `
                        : ""
                    }
                    <button class="btn btn-danger" onclick="confirmDelete('${
                      key._id
                    }', '${escapeHtml(key.name)}')" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
  }

  // Render pagination
  function renderPagination(pagination) {
    if (pagination.totalPages <= 1) {
      $("#paginationContainer").hide();
      return;
    }

    $("#paginationContainer").show();
    $("#paginationInfo").text(
      `Showing ${(pagination.page - 1) * pagination.limit + 1} to ${Math.min(
        pagination.page * pagination.limit,
        pagination.total
      )} of ${pagination.total} results`
    );

    const controls = $("#paginationControls");
    controls.empty();

    // Previous button
    const prevDisabled = pagination.page === 1 ? "disabled" : "";
    controls.append(`
        <button class="pagination-btn ${prevDisabled}" onclick="loadApiKeys(${
      pagination.page - 1
    })" ${prevDisabled ? "disabled" : ""}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `);

    // Page numbers
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.totalPages, pagination.page + 2);

    if (startPage > 1) {
      controls.append(
        `<button class="pagination-btn" onclick="loadApiKeys(1)">1</button>`
      );
      if (startPage > 2) {
        controls.append(`<span class="pagination-btn disabled">...</span>`);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      const activeClass = i === pagination.page ? "active" : "";
      controls.append(`
            <button class="pagination-btn ${activeClass}" onclick="loadApiKeys(${i})">${i}</button>
        `);
    }

    if (endPage < pagination.totalPages) {
      if (endPage < pagination.totalPages - 1) {
        controls.append(`<span class="pagination-btn disabled">...</span>`);
      }
      controls.append(
        `<button class="pagination-btn" onclick="loadApiKeys(${pagination.totalPages})">${pagination.totalPages}</button>`
      );
    }

    // Next button
    const nextDisabled =
      pagination.page === pagination.totalPages ? "disabled" : "";
    controls.append(`
        <button class="pagination-btn ${nextDisabled}" onclick="loadApiKeys(${
      pagination.page + 1
    })" ${nextDisabled ? "disabled" : ""}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `);
  }

  // Update statistics from API response
  function updateStatisticsFromAPI(stats) {
    $("#totalKeysCount").text(stats.totalKeys || 0);
    $("#activeKeysCount").text(stats.activeKeys || 0);
    $("#expiredKeysCount").text(stats.expiredKeys || 0);
    $("#revokedKeysCount").text(stats.revokedKeys || 0);
    $("#lastUsedCount").text(stats.recentlyUsed || 0);
  }

  // Legacy function for backward compatibility (if needed)
  function updateStatistics(apiKeys) {
    // This function is kept for any local calculations if needed
    // But we primarily use the API stats now
    console.log("Using API stats instead of local calculation");
  }

  // Save API key (create or update)
  function saveApiKey() {
    const saveBtn = $("#saveApiKeyBtn");
    const originalText = $("#saveApiKeyText").text();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Show loading state
    saveBtn.prop("disabled", true);
    $("#saveApiKeyText").html('<span class="loading-spinner"></span>Saving...');

    // Prepare data
    const formData = {
      name: $("#keyName").val().trim(),
      scopes: $('input[name="scopes"]:checked')
        .map(function () {
          return $(this).val();
        })
        .get(),
    };

    // Handle expiration
    const expiresAt = $("#expiresAt").val();
    if (expiresAt && expiresAt !== "") {
      if (expiresAt === "custom") {
        formData.expiresAt = $("#customExpiryDate").val();
      } else {
        const days = parseInt(expiresAt);
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + days);
        formData.expiresAt = expiryDate.toISOString();
      }
    }

    // Determine URL and method
    const url = window.editingKeyId
      ? `/api/keys/${window.editingKeyId}`
      : "/api/keys";
    const method = window.editingKeyId ? "PUT" : "POST";

    // Make request
    $.ajax({
      url: url,
      method: method,
      data: JSON.stringify(formData),
      contentType: "application/json",
      success: function (response) {
        $("#apiKeyModal").modal("hide");
        showToast(
          window.editingKeyId
            ? "API key updated successfully!"
            : "API key created successfully!"
        );
        window.loadApiKeys(window.currentPage);
        loadApiKeyStats(); // Reload stats after creating/updating

        // Show the new API key if it was just created
        if (!window.editingKeyId && response.key) {
          showToast("Your new API key is: " + response.key);
        }
      },
      error: function (xhr) {
        showToast(
          "Error saving API key: " +
            (xhr.responseJSON?.message || "Unknown error"),
          "danger"
        );
      },
      complete: function () {
        saveBtn.prop("disabled", false);
        $("#saveApiKeyText").text(originalText);
      },
    });
  }

  // Validate form
  function validateForm() {
    const name = $("#keyName").val().trim();
    const scopes = $('input[name="scopes"]:checked').length;
    const expiresAt = $("#expiresAt").val();
    const customDate = $("#customExpiryDate").val();

    if (!name) {
      showToast("Please enter a name for the API key", "danger");
      $("#keyName").focus();
      return false;
    }

    if (scopes === 0) {
      showToast("Please select at least one scope", "danger");
      return false;
    }

    if (expiresAt === "custom" && !customDate) {
      showToast("Please select a custom expiry date", "danger");
      $("#customExpiryDate").focus();
      return false;
    }

    if (expiresAt === "custom" && new Date(customDate) <= new Date()) {
      showToast("Expiry date must be in the future", "danger");
      $("#customExpiryDate").focus();
      return false;
    }

    return true;
  }
  // Delete API key
  function deleteApiKey() {
    if (!window.deletingKeyId) return;

    const deleteBtn = $("#confirmDeleteBtn");
    const originalText = $("#deleteButtonText").text();

    deleteBtn.prop("disabled", true);
    $("#deleteButtonText").html(
      '<span class="loading-spinner"></span>Deleting...'
    );

    $.ajax({
      url: `/api/keys/${window.deletingKeyId}`,
      method: "DELETE",
      success: function () {
        $("#deleteConfirmModal").modal("hide");
        showToast("API key deleted successfully!");
        window.loadApiKeys(window.currentPage);
        loadApiKeyStats(); // Reload stats after deleting
      },
      error: function (xhr) {
        showToast(
          "Error deleting API key: " +
            (xhr.responseJSON?.message || "Unknown error"),
          "danger"
        );
      },
      complete: function () {
        deleteBtn.prop("disabled", false);
        $("#deleteButtonText").text(originalText);
        window.deletingKeyId = null;
      },
    });
  }

  // Revoke API key
  function revokeApiKey() {
    if (!window.revokingKeyId) return;

    const revokeBtn = $("#confirmRevokeBtn");
    const originalText = $("#revokeButtonText").text();

    revokeBtn.prop("disabled", true);
    $("#revokeButtonText").html(
      '<span class="loading-spinner"></span>Revoking...'
    );

    $.ajax({
      url: `/api/keys/revoke/${window.revokingKeyId}`,
      method: "PATCH",
      success: function () {
        $("#revokeConfirmModal").modal("hide");
        showToast("API key revoked successfully!");
        window.loadApiKeys(window.currentPage);
        loadApiKeyStats(); // Reload stats after revoking
      },
      error: function (xhr) {
        showToast(
          "Error revoking API key: " +
            (xhr.responseJSON?.message || "Unknown error"),
          "danger"
        );
      },
      complete: function () {
        revokeBtn.prop("disabled", false);
        $("#revokeButtonText").text(originalText);
        window.revokingKeyId = null;
      },
    });
  }

  // Reset form
  function resetForm() {
    $("#apiKeyForm")[0].reset();
    $('input[name="scopes"]').prop("checked", false);
    $("#customDateGroup").hide();
    $("#customExpiryDate").attr("required", false);
    window.editingKeyId = null;
  }

  // Show loading state
  function showLoading(show) {
    if (show) {
      $("#loadingState").show();
      $("#apiKeysTableContainer").hide();
      $("#emptyState").hide();
    } else {
      $("#loadingState").hide();
    }
  }

  // Global variables for secure viewing
  let currentSecureKeyId = null;
  let otpTimer = null;
  let accessTimer = null;

  // Secure API Key Viewing Functions
  window.secureViewApiKey = function (keyId) {
    currentSecureKeyId = keyId;
    $("#otpVerificationModal").modal("show");
    $("#userEmail").val(""); // Clear previous email
    $("#otpCode").val(""); // Clear previous OTP
    $("#otpInputSection").addClass("d-none");
    $("#sendOtpBtn").removeClass("d-none");
    $("#verifyOtpBtn").addClass("d-none");
    $("#otpError").addClass("d-none");
    $("#otpSuccess").addClass("d-none");
  };

  window.secureCopyApiKey = function (keyId) {
    // First try to get the API key securely
    $.ajax({
      url: `/api/keys/secure-view/${keyId}`,
      method: "GET",
      success: function (response) {
        // If successful, copy to clipboard
        copyToClipboard(response.key);
        showToast("API key copied to clipboard!", "success");
      },
      error: function (xhr) {
        if (xhr.responseJSON?.requiresVerification) {
          // If verification required, open OTP modal
          secureViewApiKey(keyId);
          showToast(
            "Please verify your identity to copy the API key",
            "warning"
          );
        } else {
          showToast(
            "Error accessing API key: " +
              (xhr.responseJSON?.message || "Unknown error"),
            "danger"
          );
        }
      },
    });
  };

  function sendOtpCode() {
    const email = $("#userEmail").val().trim();

    if (!email) {
      showOtpError("Please enter your email address");
      return;
    }

    if (!currentSecureKeyId) {
      showOtpError("Invalid API key ID");
      return;
    }

    const sendBtn = $("#sendOtpBtn");
    const spinner = $("#sendOtpSpinner");

    sendBtn.prop("disabled", true);
    spinner.removeClass("d-none");

    $.ajax({
      url: "/api/keys/send-otp",
      method: "POST",
      contentType: "application/json",
      data: JSON.stringify({
        email: email,
        apiKeyId: currentSecureKeyId,
      }),
      success: function (response) {
        showOtpSuccess("Verification code sent to your email");
        $("#otpInputSection").removeClass("d-none");
        $("#sendOtpBtn").addClass("d-none");
        $("#verifyOtpBtn").removeClass("d-none");
        $("#otpCode").focus();
        startOtpTimer();
      },
      error: function (xhr) {
        showOtpError(
          xhr.responseJSON?.message || "Failed to send verification code"
        );
      },
      complete: function () {
        sendBtn.prop("disabled", false);
        spinner.addClass("d-none");
      },
    });
  }

  function verifyOtpCode() {
    const otp = $("#otpCode").val().trim();

    if (!otp || otp.length !== 6) {
      showOtpError("Please enter a valid 6-digit code");
      return;
    }

    if (!currentSecureKeyId) {
      showOtpError("Invalid API key ID");
      return;
    }

    const verifyBtn = $("#verifyOtpBtn");
    const spinner = $("#verifyOtpSpinner");

    verifyBtn.prop("disabled", true);
    spinner.removeClass("d-none");

    $.ajax({
      url: "/api/keys/verify-otp",
      method: "POST",
      contentType: "application/json",
      data: JSON.stringify({
        apiKeyId: currentSecureKeyId,
        otp: otp,
      }),
      success: function (response) {
        showOtpSuccess("Identity verified successfully!");
        setTimeout(() => {
          $("#otpVerificationModal").modal("hide");
          loadSecureApiKey();
        }, 1000);
      },
      error: function (xhr) {
        showOtpError(xhr.responseJSON?.message || "Invalid verification code");
      },
      complete: function () {
        verifyBtn.prop("disabled", false);
        spinner.addClass("d-none");
      },
    });
  }

  function loadSecureApiKey() {
    if (!currentSecureKeyId) return;

    $.ajax({
      url: `/api/keys/secure-view/${currentSecureKeyId}`,
      method: "GET",
      success: function (response) {
        // Check if we're in details modal context
        if (window.detailsModalContext) {
          // Load in details modal
          $("#detailsSecureKeyValue").val(response.key);
          $("#secureKeySection").removeClass("d-none");

          // Start access timer for details modal
          if (response.expiresAt) {
            startDetailsAccessTimer(new Date(response.expiresAt));
          }

          // Reset context flag
          window.detailsModalContext = false;
        } else {
          // Load in standalone modal
          $("#secureKeyName").text(response.name);
          $("#secureKeyValue").val(response.key);
          $("#secureApiKeyModal").modal("show");

          // Start access timer
          if (response.expiresAt) {
            startAccessTimer(new Date(response.expiresAt));
          }
        }
      },
      error: function (xhr) {
        showToast(
          "Error loading API key: " +
            (xhr.responseJSON?.message || "Unknown error"),
          "danger"
        );
        // Reset context flag on error
        window.detailsModalContext = false;
      },
    });
  }

  function copySecureApiKey() {
    const keyValue = $("#secureKeyValue").val();
    if (keyValue) {
      copyToClipboard(keyValue);
      showToast("API key copied to clipboard!", "success");
    }
  }

  function showOtpError(message) {
    $("#otpError").text(message).removeClass("d-none");
    $("#otpSuccess").addClass("d-none");
  }

  function showOtpSuccess(message) {
    $("#otpSuccess").text(message).removeClass("d-none");
    $("#otpError").addClass("d-none");
  }

  function startOtpTimer() {
    let timeLeft = 30 * 60; // 30 minutes in seconds

    if (otpTimer) clearInterval(otpTimer);

    otpTimer = setInterval(() => {
      const minutes = Math.floor(timeLeft / 60);
      const seconds = timeLeft % 60;
      $("#otpTimer").text(
        `${minutes.toString().padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}`
      );

      if (timeLeft <= 0) {
        clearInterval(otpTimer);
        $("#otpTimer").text("00:00");
        showOtpError(
          "Verification code has expired. Please request a new one."
        );
      }

      timeLeft--;
    }, 1000);
  }

  function startAccessTimer(expiresAt) {
    if (accessTimer) clearInterval(accessTimer);

    accessTimer = setInterval(() => {
      const now = new Date();
      const timeLeft = Math.max(0, expiresAt - now);

      if (timeLeft <= 0) {
        clearInterval(accessTimer);
        $("#accessTimer").text("00:00");
        $("#secureApiKeyModal").modal("hide");
        showToast("API key access has expired", "warning");
        return;
      }

      const minutes = Math.floor(timeLeft / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
      $("#accessTimer").text(
        `${minutes.toString().padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}`
      );
    }, 1000);
  }

  // Functions for Details Modal Secure Viewing
  window.secureViewApiKeyFromDetails = function () {
    if (!window.currentKeyDetails) {
      showToast("No API key selected", "danger");
      return;
    }

    currentSecureKeyId = window.currentKeyDetails._id;
    window.detailsModalContext = true; // Flag to know we're coming from details modal
    $("#otpVerificationModal").modal("show");
    $("#userEmail").val(""); // Clear previous email
    $("#otpCode").val(""); // Clear previous OTP
    $("#otpInputSection").addClass("d-none");
    $("#sendOtpBtn").removeClass("d-none");
    $("#verifyOtpBtn").addClass("d-none");
    $("#otpError").addClass("d-none");
    $("#otpSuccess").addClass("d-none");
  };

  window.secureCopyApiKeyFromDetails = function () {
    if (!window.currentKeyDetails) {
      showToast("No API key selected", "danger");
      return;
    }

    const keyId = window.currentKeyDetails._id;

    // First try to get the API key securely
    $.ajax({
      url: `/api/keys/secure-view/${keyId}`,
      method: "GET",
      success: function (response) {
        // If successful, copy to clipboard
        copyToClipboard(response.key);
        showToast("API key copied to clipboard!", "success");
      },
      error: function (xhr) {
        if (xhr.responseJSON?.requiresVerification) {
          // If verification required, open OTP modal
          secureViewApiKeyFromDetails();
          showToast(
            "Please verify your identity to copy the API key",
            "warning"
          );
        } else {
          showToast(
            "Error accessing API key: " +
              (xhr.responseJSON?.message || "Unknown error"),
            "danger"
          );
        }
      },
    });
  };

  function copyDetailsSecureApiKey() {
    const keyValue = $("#detailsSecureKeyValue").val();
    if (keyValue) {
      copyToClipboard(keyValue);
      showToast("API key copied to clipboard!", "success");
    }
  }

  // Function removed - integrated into main loadSecureApiKey function

  function startDetailsAccessTimer(expiresAt) {
    if (window.detailsAccessTimer) clearInterval(window.detailsAccessTimer);

    window.detailsAccessTimer = setInterval(() => {
      const now = new Date();
      const timeLeft = Math.max(0, expiresAt - now);

      if (timeLeft <= 0) {
        clearInterval(window.detailsAccessTimer);
        $("#detailsAccessTimer").text("00:00");
        $("#secureKeySection").addClass("d-none");
        showToast("API key access has expired", "warning");
        return;
      }

      const minutes = Math.floor(timeLeft / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
      $("#detailsAccessTimer").text(
        `${minutes.toString().padStart(2, "0")}:${seconds
          .toString()
          .padStart(2, "0")}`
      );
    }, 1000);
  }

  // Clean up timers when modals are closed
  $("#otpVerificationModal").on("hidden.bs.modal", function () {
    if (otpTimer) {
      clearInterval(otpTimer);
      otpTimer = null;
    }
  });

  $("#secureApiKeyModal").on("hidden.bs.modal", function () {
    if (accessTimer) {
      clearInterval(accessTimer);
      accessTimer = null;
    }
  });

  $("#apiKeyDetailsModal").on("hidden.bs.modal", function () {
    if (window.detailsAccessTimer) {
      clearInterval(window.detailsAccessTimer);
      window.detailsAccessTimer = null;
    }
  });

  // Auto-call reset API every hour
  function scheduleResetToBeShown() {
    setInterval(() => {
      $.ajax({
        url: "/api/keys/reset-tobe-shown",
        method: "GET",
        success: function (response) {
          console.log("Reset tobeShown completed:", response.message);
        },
        error: function (xhr) {
          console.error("Reset tobeShown failed:", xhr.responseJSON?.message);
        },
      });
    }, 60 * 60 * 1000); // Every hour
  }

  // Initialize reset schedule when page loads
  $(document).ready(function () {
    scheduleResetToBeShown();
  });

  // Utility functions
  function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  function escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  // Refresh data every 30 minutes
  setInterval(() => {
    window.loadApiKeys(window.currentPage);
    loadApiKeyStats();
  }, 1800000); // 30 minutes = 30 * 60 * 1000 milliseconds
</script>
