<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title":"API Settings" }) %>
<style>

  .api-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
  }

  .api-header {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    padding: 30px;
    margin-bottom: 30px;
  }

  .api-header h2 {
    color: #5156be;
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 28px;
  }

  .api-header p {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
  }

  .api-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .stat-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    text-align: center;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(81, 86, 190, 0.15);
  }

  .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #5156be;
    margin-bottom: 8px;
  }

  .stat-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .api-keys-section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
  }

  .section-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
  }

  .btn-primary-custom {
    background: #ffffff;
    color: #5156be;
    border: 2px solid #ffffff;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }

  .btn-primary-custom:hover {
    background: #f8f9fa;
    color: #5156be;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
  }

  .btn-danger-custom {
    background: #dc3545;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
  }

  .btn-danger-custom:hover {
    background: #c82333;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-edit-custom {
    background: #28a745;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
    margin-right: 8px;
  }

  .btn-edit-custom:hover {
    background: #218838;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-revoke-custom {
    background: #fd7e14;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
    margin-right: 8px;
  }

  .btn-revoke-custom:hover {
    background: #e8650e;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .api-keys-table {
    width: 100%;
    margin: 0;
  }

  .api-keys-table thead {
    background: #f8f9fa;
  }

  .api-keys-table th {
    padding: 20px;
    font-weight: 600;
    color: #495057;
    border: none;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .api-keys-table td {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
  }

  .api-key-name {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
  }

  .api-key-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 300px;
  }

  .api-key-hidden {
    color: #6c757d;
    font-style: italic;
  }

  .copy-btn {
    background: none;
    border: none;
    color: #5156be;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .copy-btn:hover {
    background: #5156be;
    color: #ffffff;
  }

  .scope-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
    display: inline-block;
  }

  .status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .status-active {
    background: #d4edda;
    color: #155724;
  }

  .status-expired {
    background: #f8d7da;
    color: #721c24;
  }

  .status-revoked {
    background: #f5c6cb;
    color: #721c24;
  }

  .status-never {
    background: #e2e3e5;
    color: #6c757d;
  }

  .empty-state {
    text-align: center;
    padding: 60px 30px;
    color: #6c757d;
  }

  .empty-state i {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
  }

  .empty-state h3 {
    color: #495057;
    margin-bottom: 10px;
  }

  .pagination-container {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .pagination-info {
    color: #6c757d;
    font-size: 14px;
  }

  .pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .pagination-btn {
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .pagination-btn:hover:not(.disabled) {
    background: #5156be;
    color: #ffffff;
    border-color: #5156be;
  }

  .pagination-btn.active {
    background: #5156be;
    color: #ffffff;
    border-color: #5156be;
  }

  .pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Modal Styles */
  .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: #ffffff;
    border-radius: 12px 12px 0 0;
    padding: 25px 30px;
    border-bottom: none;
  }

  .modal-title {
    font-weight: 600;
    font-size: 20px;
    color: #ffffff;
  }

  .btn-close {
    background: none;
    border: none;
    color: #ffffff;
    opacity: 0.8;
    font-size: 24px;
  }

  .btn-close:hover {
    opacity: 1;
    color: #ffffff;
  }

  .modal-body {
    padding: 30px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
  }

  .form-control-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
  }

  .form-control-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .form-select-custom {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
    background: #ffffff;
  }

  .form-select-custom:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
    outline: none;
  }

  .scope-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
  }

  .scope-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
  }

  .scope-item:hover {
    border-color: #5156be;
    background: #f0f0ff;
  }

  .scope-item input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
  }

  .scope-item label {
    margin: 0;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
  }

  .modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
  }

  .btn-modal-primary {
    background: #5156be;
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-modal-primary:hover {
    background: #4c51b8;
    color: #ffffff;
    transform: translateY(-1px);
  }

  .btn-modal-secondary {
    background: #6c757d;
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 10px;
  }

  .btn-modal-secondary:hover {
    background: #5a6268;
    color: #ffffff;
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #5156be;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .alert-custom {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .alert-success-custom {
    background: #d1edff;
    color: #155724;
    border-left: 4px solid #28a745;
  }

  .alert-error-custom {
    background: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .api-header {
      padding: 20px;
      text-align: center;
    }

    .section-header {
      flex-direction: column;
      text-align: center;
    }

    .api-keys-table {
      font-size: 14px;
    }

    .api-keys-table th,
    .api-keys-table td {
      padding: 15px 10px;
    }

    .api-key-value {
      max-width: 200px;
      font-size: 12px;
    }

    .pagination-container {
      flex-direction: column;
      text-align: center;
    }

    .scope-selection {
      grid-template-columns: 1fr;
    }

    .modal-body {
      padding: 20px;
    }
  }

  @media (max-width: 576px) {
    .api-stats {
      grid-template-columns: 1fr;
    }

    .stat-card {
      padding: 20px;
    }

    .api-keys-table th:nth-child(3),
    .api-keys-table td:nth-child(3),
    .api-keys-table th:nth-child(4),
    .api-keys-table td:nth-child(4) {
      display: none;
    }
  }
</style>

<%- contentFor('body') %>
<%-include("partials/page-title",{"title": "API Settings" , "pagetitle": "Settings" }) %>

<div class="api-container">
  <div class="container-fluid">
    <!-- API Header -->
    <div class="api-header">
      <h2><i class="fas fa-key me-2"></i>API Key Management</h2>
      <p>Create and manage API keys to integrate with your applications. Keep your keys secure and rotate them regularly.</p>
    </div>

    <!-- API Statistics -->
    <div class="api-stats">
      <div class="stat-card">
        <div class="stat-number" id="totalKeysCount">0</div>
        <div class="stat-label">Total Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="activeKeysCount">0</div>
        <div class="stat-label">Active Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="expiredKeysCount">0</div>
        <div class="stat-label">Expired Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="revokedKeysCount">0</div>
        <div class="stat-label">Revoked Keys</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="lastUsedCount">0</div>
        <div class="stat-label">Recently Used</div>
      </div>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- API Keys Section -->
    <div class="api-keys-section">
      <div class="section-header">
        <h3 class="section-title"><i class="fas fa-list me-2"></i>Your API Keys</h3>
        <button class="btn-primary-custom" onclick="openCreateModal()">
          <i class="fas fa-plus"></i>
          Create New Key
        </button>
      </div>

      <!-- Loading State -->
      <div id="loadingState" class="text-center p-5" style="display: none;">
        <div class="loading-spinner" style="width: 40px; height: 40px; border-width: 4px;"></div>
        <p class="mt-3 text-muted">Loading API keys...</p>
      </div>

      <!-- API Keys Table -->
      <div id="apiKeysTableContainer">
        <table class="api-keys-table table table-hover">
          <thead>
            <tr>
              <th>Name</th>
              <th>API Key</th>
              <th>Scopes</th>
              <th>Last Used</th>
              <th>Expires</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="apiKeysTableBody">
            <!-- API keys will be loaded here -->
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div id="emptyState" class="empty-state" style="display: none;">
        <i class="fas fa-key"></i>
        <h3>No API Keys Found</h3>
        <p>You haven't created any API keys yet. Create your first API key to get started with our API.</p>
        <button class="btn-primary-custom" onclick="openCreateModal()" style="background: #5156be; color: #ffffff; margin-top: 20px;">
          <i class="fas fa-plus"></i>
          Create Your First API Key
        </button>
      </div>

      <!-- Pagination -->
      <div class="pagination-container" id="paginationContainer" style="display: none;">
        <div class="pagination-info">
          <span id="paginationInfo">Showing 0 of 0 results</span>
        </div>
        <div class="pagination-controls" id="paginationControls">
          <!-- Pagination buttons will be generated here -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit API Key Modal -->
<div class="modal fade" id="apiKeyModal" tabindex="-1" aria-labelledby="apiKeyModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="apiKeyModalLabel">Create New API Key</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="apiKeyForm">
        <div class="modal-body">
          <div class="form-group">
            <label for="keyName" class="form-label">API Key Name *</label>
            <input type="text" class="form-control-custom" id="keyName" name="name" required
                   placeholder="e.g., Production App, Mobile App, Testing">
            <small class="text-muted">Choose a descriptive name to identify this API key</small>
          </div>

          <div class="form-group">
            <label for="expiresAt" class="form-label">Expiration Date</label>
            <select class="form-select-custom" id="expiresAt" name="expiresAt">
              <option value="">Never expires</option>
              <option value="30">30 days</option>
              <option value="90">90 days</option>
              <option value="180">6 months</option>
              <option value="365">1 year</option>
              <option value="custom">Custom date</option>
            </select>
          </div>

          <div class="form-group" id="customDateGroup" style="display: none;">
            <label for="customExpiryDate" class="form-label">Custom Expiry Date</label>
            <input type="date" class="form-control-custom" id="customExpiryDate" name="customExpiryDate">
          </div>

          <div class="form-group">
            <label class="form-label">API Scopes *</label>
            <small class="text-muted d-block mb-3">Select the permissions this API key should have</small>
            <div class="scope-selection" id="scopeSelection">
              <div class="scope-item">
                <input type="checkbox" id="scope_certificates_read" name="scopes" value="certificates:read">
                <label for="scope_certificates_read">Read Certificates</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_certificates_create" name="scopes" value="certificates:create">
                <label for="scope_certificates_create">Create Certificates</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_certificates_update" name="scopes" value="certificates:update">
                <label for="scope_certificates_update">Update Certificates</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_certificates_delete" name="scopes" value="certificates:delete">
                <label for="scope_certificates_delete">Delete Certificates</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_badges_read" name="scopes" value="badges:read">
                <label for="scope_badges_read">Read Badges</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_badges_create" name="scopes" value="badges:create">
                <label for="scope_badges_create">Create Badges</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_contacts_read" name="scopes" value="contacts:read">
                <label for="scope_contacts_read">Read Contacts</label>
              </div>
              <div class="scope-item">
                <input type="checkbox" id="scope_contacts_create" name="scopes" value="contacts:create">
                <label for="scope_contacts_create">Create Contacts</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn-modal-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn-modal-primary" id="saveApiKeyBtn">
            <span id="saveApiKeyText">Create API Key</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background: #dc3545;">
        <h5 class="modal-title" id="deleteConfirmModalLabel" style="color: #ffffff;">
          <i class="fas fa-exclamation-triangle me-2"></i>Delete API Key
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to delete this API key?</p>
        <div class="alert alert-warning">
          <strong>Warning:</strong> This action cannot be undone. Any applications using this API key will lose access immediately.
        </div>
        <div class="bg-light p-3 rounded">
          <strong>API Key Name:</strong> <span id="deleteKeyName"></span><br>
          <strong>Created:</strong> <span id="deleteKeyCreated"></span>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn-modal-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn-danger-custom" id="confirmDeleteBtn">
          <span id="deleteButtonText">Delete API Key</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Revoke Confirmation Modal -->
<div class="modal fade" id="revokeConfirmModal" tabindex="-1" aria-labelledby="revokeConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="background: #fd7e14;">
        <h5 class="modal-title" id="revokeConfirmModalLabel" style="color: #ffffff;">
          <i class="fas fa-ban me-2"></i>Revoke API Key
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to revoke this API key?</p>
        <div class="alert alert-warning">
          <strong>Warning:</strong> This action will immediately disable the API key. Applications using this key will lose access, but the key can still be viewed for reference.
        </div>
        <div class="bg-light p-3 rounded">
          <strong>API Key Name:</strong> <span id="revokeKeyName"></span><br>
          <strong>Created:</strong> <span id="revokeKeyCreated"></span>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn-modal-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn-revoke-custom" id="confirmRevokeBtn">
          <span id="revokeButtonText">Revoke API Key</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- API Key Details Modal -->
<div class="modal fade" id="apiKeyDetailsModal" tabindex="-1" aria-labelledby="apiKeyDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="apiKeyDetailsModalLabel">
          <i class="fas fa-info-circle me-2"></i>API Key Details
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Name</label>
              <div class="form-control-custom" id="detailsKeyName" style="background: #f8f9fa;"></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Status</label>
              <div id="detailsKeyStatus"></div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">API Key</label>
          <div class="api-key-value">
            <span id="detailsKeyValue">••••••••••••••••••••••••••••••••••••••••</span>
            <button class="copy-btn" onclick="toggleKeyVisibility('details')" title="Show/Hide Key">
              <i class="fas fa-eye" id="detailsToggleIcon"></i>
            </button>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Created</label>
              <div class="form-control-custom" id="detailsKeyCreated" style="background: #f8f9fa;"></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Last Used</label>
              <div class="form-control-custom" id="detailsKeyLastUsed" style="background: #f8f9fa;"></div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Expires</label>
          <div class="form-control-custom" id="detailsKeyExpires" style="background: #f8f9fa;"></div>
        </div>

        <div class="form-group">
          <label class="form-label">Scopes</label>
          <div id="detailsKeyScopes"></div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn-modal-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn-edit-custom" onclick="editApiKey()">
          <i class="fas fa-edit"></i> Edit
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<script>
// Global variables
let currentPage = 1;
let totalPages = 1;
let currentApiKeys = [];
let editingKeyId = null;
let deletingKeyId = null;
let revokingKeyId = null;
let currentKeyDetails = null;

// ===== GLOBAL FUNCTIONS (Called from HTML) =====

// Test function to verify global access
window.testFunction = function() {
    console.log('Test function called successfully!');
    alert('All functions are working correctly!');
};

// Open create modal - Global function
window.openCreateModal = function() {
    editingKeyId = null;
    $('#apiKeyModalLabel').text('Create New API Key');
    $('#saveApiKeyText').text('Create API Key');
    resetForm();
    $('#apiKeyModal').modal('show');
};

// Load API keys with pagination - Global function
window.loadApiKeys = function(page = 1) {
    currentPage = page;
    showLoading(true);

    $.ajax({
        url: `/api/keys?page=${page}&limit=10`,
        method: 'GET',
        success: function(response) {
            currentApiKeys = response.data;
            renderApiKeys(response.data);
            renderPagination(response.pagination);
            showLoading(false);
            // Reload stats after loading keys to ensure they're up to date
            loadApiKeyStats();
        },
        error: function(xhr) {
            showLoading(false);
            showAlert('Error loading API keys: ' + (xhr.responseJSON?.message || 'Unknown error'), 'error');
        }
    });
};

// View API key details - Global function
window.viewApiKeyDetails = function(keyId) {
    const apiKey = currentApiKeys.find(key => key._id === keyId);
    if (!apiKey) {
        showAlert('API key not found', 'error');
        return;
    }

    currentKeyDetails = apiKey;

    // Populate modal
    $('#detailsKeyName').text(apiKey.name);
    $('#detailsKeyValue').text('••••••••••••••••••••••••••••••••••••••••');
    $('#detailsKeyCreated').text(formatDate(apiKey.createdAt));
    $('#detailsKeyLastUsed').text(apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : 'Never');
    $('#detailsKeyExpires').text(apiKey.expiresAt ? formatDate(apiKey.expiresAt) : 'Never');

    // Status
    let statusHtml;
    if (apiKey.status === 'revoked') {
        statusHtml = '<span class="status-badge status-revoked">Revoked</span>';
    } else if (apiKey.status === 'expired' || (apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date())) {
        statusHtml = '<span class="status-badge status-expired">Expired</span>';
    } else {
        statusHtml = '<span class="status-badge status-active">Active</span>';
    }
    $('#detailsKeyStatus').html(statusHtml);

    // Scopes
    const scopesHtml = apiKey.scopes.map(scope =>
        `<span class="scope-badge">${scope}</span>`
    ).join(' ');
    $('#detailsKeyScopes').html(scopesHtml);

    // Reset toggle icon
    $('#detailsToggleIcon').removeClass('fa-eye-slash').addClass('fa-eye');

    $('#apiKeyDetailsModal').modal('show');
};

// Edit API key - Global function
window.editApiKey = function() {
    if (!currentKeyDetails) return;

    editingKeyId = currentKeyDetails._id;
    $('#apiKeyDetailsModal').modal('hide');

    // Populate form
    $('#keyName').val(currentKeyDetails.name);

    // Set scopes
    $('input[name="scopes"]').prop('checked', false);
    currentKeyDetails.scopes.forEach(scope => {
        $(`input[name="scopes"][value="${scope}"]`).prop('checked', true);
    });

    // Set expiry
    if (currentKeyDetails.expiresAt) {
        const expiryDate = new Date(currentKeyDetails.expiresAt);
        const now = new Date();
        const diffDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));

        if ([30, 90, 180, 365].includes(diffDays)) {
            $('#expiresAt').val(diffDays.toString());
        } else {
            $('#expiresAt').val('custom');
            $('#customExpiryDate').val(expiryDate.toISOString().split('T')[0]);
            $('#customDateGroup').show();
        }
    } else {
        $('#expiresAt').val('');
    }

    $('#apiKeyModalLabel').text('Edit API Key');
    $('#saveApiKeyText').text('Update API Key');
    $('#apiKeyModal').modal('show');
};

// Confirm delete - Global function
window.confirmDelete = function(keyId, keyName) {
    deletingKeyId = keyId;
    const apiKey = currentApiKeys.find(key => key._id === keyId);

    $('#deleteKeyName').text(keyName);
    $('#deleteKeyCreated').text(apiKey ? formatDate(apiKey.createdAt) : 'Unknown');
    $('#deleteConfirmModal').modal('show');
};

// Confirm revoke - Global function
window.confirmRevoke = function(keyId, keyName) {
    revokingKeyId = keyId;
    const apiKey = currentApiKeys.find(key => key._id === keyId);

    $('#revokeKeyName').text(keyName);
    $('#revokeKeyCreated').text(apiKey ? formatDate(apiKey.createdAt) : 'Unknown');
    $('#revokeConfirmModal').modal('show');
};

// Toggle key visibility - Global function
window.toggleKeyVisibility = function(context) {
    const valueElement = context === 'details' ? $('#detailsKeyValue') : null;
    const iconElement = context === 'details' ? $('#detailsToggleIcon') : null;

    if (!valueElement || !currentKeyDetails) return;

    if (iconElement.hasClass('fa-eye')) {
        // Show key
        valueElement.text(currentKeyDetails.key);
        iconElement.removeClass('fa-eye').addClass('fa-eye-slash');
    } else {
        // Hide key
        valueElement.text('••••••••••••••••••••••••••••••••••••••••');
        iconElement.removeClass('fa-eye-slash').addClass('fa-eye');
    }
};

// Copy to clipboard - Global function
window.copyToClipboard = function(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('API key copied to clipboard!', 'success');
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('API key copied to clipboard!', 'success');
    });
};

// ===== INITIALIZATION =====

// Initialize page
$(document).ready(function() {
    // Debug: Check if functions are properly defined
    console.log('Functions check:', {
        openCreateModal: typeof window.openCreateModal,
        loadApiKeys: typeof window.loadApiKeys,
        viewApiKeyDetails: typeof window.viewApiKeyDetails,
        confirmDelete: typeof window.confirmDelete,
        copyToClipboard: typeof window.copyToClipboard,
        toggleKeyVisibility: typeof window.toggleKeyVisibility
    });

    // Also check if they're accessible globally
    console.log('Global access check:', {
        openCreateModal: typeof openCreateModal,
        loadApiKeys: typeof loadApiKeys,
        viewApiKeyDetails: typeof viewApiKeyDetails,
        confirmDelete: typeof confirmDelete,
        copyToClipboard: typeof copyToClipboard,
        toggleKeyVisibility: typeof toggleKeyVisibility
    });

    loadApiKeys();
    loadApiKeyStats();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Form submission
    $('#apiKeyForm').on('submit', function(e) {
        e.preventDefault();
        saveApiKey();
    });

    // Expiry date change
    $('#expiresAt').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#customDateGroup').show();
            $('#customExpiryDate').attr('required', true);
        } else {
            $('#customDateGroup').hide();
            $('#customExpiryDate').attr('required', false);
        }
    });

    // Delete confirmation
    $('#confirmDeleteBtn').on('click', function() {
        deleteApiKey();
    });

    // Revoke confirmation
    $('#confirmRevokeBtn').on('click', function() {
        revokeApiKey();
    });

    // Modal events
    $('#apiKeyModal').on('hidden.bs.modal', function() {
        resetForm();
    });
}

// Load API key statistics
function loadApiKeyStats() {
    $.ajax({
        url: '/api/keys/all/stats',
        method: 'GET',
        success: function(response) {
            console.log('API Stats Response:', response); // Debug log
            updateStatisticsFromAPI(response);
        },
        error: function(xhr) {
            console.error('Error loading API key stats:', xhr.responseJSON?.message || 'Unknown error');
            // Fallback to showing zeros
            updateStatisticsFromAPI({
                totalKeys: 0,
                activeKeys: 0,
                expiredKeys: 0,
                revokedKeys: 0,
                recentlyUsed: 0
            });
        }
    });
}



// Render API keys table
function renderApiKeys(apiKeys) {
    const tbody = $('#apiKeysTableBody');
    tbody.empty();

    if (apiKeys.length === 0) {
        $('#apiKeysTableContainer').hide();
        $('#emptyState').show();
        $('#paginationContainer').hide();
        return;
    }

    $('#emptyState').hide();
    $('#apiKeysTableContainer').show();

    apiKeys.forEach(key => {
        const row = createApiKeyRow(key);
        tbody.append(row);
    });
}

// Create API key table row
function createApiKeyRow(key) {
    // Determine status based on backend status field
    let statusClass, statusText;
    if (key.status === 'revoked') {
        statusClass = 'status-revoked';
        statusText = 'Revoked';
    } else if (key.status === 'expired' || (key.expiresAt && new Date(key.expiresAt) < new Date())) {
        statusClass = 'status-expired';
        statusText = 'Expired';
    } else {
        statusClass = 'status-active';
        statusText = 'Active';
    }

    const lastUsed = key.lastUsedAt ?
        formatDate(key.lastUsedAt) :
        '<span class="status-never">Never</span>';

    const expires = key.expiresAt ?
        formatDate(key.expiresAt) :
        '<span class="text-muted">Never</span>';

    const scopes = key.scopes.map(scope =>
        `<span class="scope-badge">${scope}</span>`
    ).join(' ');

    return `
        <tr>
            <td>
                <div class="api-key-name">${escapeHtml(key.name)}</div>
            </td>
            <td>
                <div class="api-key-value">
                    <span class="api-key-hidden">••••••••••••••••••••••••••••••••••••••••</span>
                    <button class="copy-btn" onclick="copyToClipboard('${key.key}')" title="Copy to clipboard">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </td>
            <td>
                <div style="max-width: 200px;">
                    ${scopes}
                </div>
            </td>
            <td>${lastUsed}</td>
            <td>${expires}</td>
            <td>
                <span class="status-badge ${statusClass}">${statusText}</span>
            </td>
            <td>
                <button class="btn-edit-custom" onclick="viewApiKeyDetails('${key._id}')" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                ${key.status !== 'revoked' ? `
                    <button class="btn-revoke-custom" onclick="confirmRevoke('${key._id}', '${escapeHtml(key.name)}')" title="Revoke Key">
                        <i class="fas fa-ban"></i>
                    </button>
                ` : ''}
                <button class="btn-danger-custom" onclick="confirmDelete('${key._id}', '${escapeHtml(key.name)}')" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
}

// Render pagination
function renderPagination(pagination) {
    if (pagination.totalPages <= 1) {
        $('#paginationContainer').hide();
        return;
    }

    $('#paginationContainer').show();
    $('#paginationInfo').text(
        `Showing ${((pagination.page - 1) * pagination.limit) + 1} to ${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} results`
    );

    const controls = $('#paginationControls');
    controls.empty();

    // Previous button
    const prevDisabled = pagination.page === 1 ? 'disabled' : '';
    controls.append(`
        <button class="pagination-btn ${prevDisabled}" onclick="loadApiKeys(${pagination.page - 1})" ${prevDisabled ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `);

    // Page numbers
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.totalPages, pagination.page + 2);

    if (startPage > 1) {
        controls.append(`<button class="pagination-btn" onclick="loadApiKeys(1)">1</button>`);
        if (startPage > 2) {
            controls.append(`<span class="pagination-btn disabled">...</span>`);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.page ? 'active' : '';
        controls.append(`
            <button class="pagination-btn ${activeClass}" onclick="loadApiKeys(${i})">${i}</button>
        `);
    }

    if (endPage < pagination.totalPages) {
        if (endPage < pagination.totalPages - 1) {
            controls.append(`<span class="pagination-btn disabled">...</span>`);
        }
        controls.append(`<button class="pagination-btn" onclick="loadApiKeys(${pagination.totalPages})">${pagination.totalPages}</button>`);
    }

    // Next button
    const nextDisabled = pagination.page === pagination.totalPages ? 'disabled' : '';
    controls.append(`
        <button class="pagination-btn ${nextDisabled}" onclick="loadApiKeys(${pagination.page + 1})" ${nextDisabled ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `);
}

// Update statistics from API response
function updateStatisticsFromAPI(stats) {
    $('#totalKeysCount').text(stats.totalKeys || 0);
    $('#activeKeysCount').text(stats.activeKeys || 0);
    $('#expiredKeysCount').text(stats.expiredKeys || 0);
    $('#revokedKeysCount').text(stats.revokedKeys || 0);
    $('#lastUsedCount').text(stats.recentlyUsed || 0);
}

// Legacy function for backward compatibility (if needed)
function updateStatistics(apiKeys) {
    // This function is kept for any local calculations if needed
    // But we primarily use the API stats now
    console.log('Using API stats instead of local calculation');
}



// Save API key (create or update)
function saveApiKey() {
    const saveBtn = $('#saveApiKeyBtn');
    const originalText = $('#saveApiKeyText').text();

    // Validate form
    if (!validateForm()) {
        return;
    }

    // Show loading state
    saveBtn.prop('disabled', true);
    $('#saveApiKeyText').html('<span class="loading-spinner"></span>Saving...');

    // Prepare data
    const formData = {
        name: $('#keyName').val().trim(),
        scopes: $('input[name="scopes"]:checked').map(function() {
            return $(this).val();
        }).get()
    };

    // Handle expiration
    const expiresAt = $('#expiresAt').val();
    if (expiresAt && expiresAt !== '') {
        if (expiresAt === 'custom') {
            formData.expiresAt = $('#customExpiryDate').val();
        } else {
            const days = parseInt(expiresAt);
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + days);
            formData.expiresAt = expiryDate.toISOString();
        }
    }

    // Determine URL and method
    const url = editingKeyId ? `/api/keys/${editingKeyId}` : '/api/keys';
    const method = editingKeyId ? 'PUT' : 'POST';

    // Make request
    $.ajax({
        url: url,
        method: method,
        data: JSON.stringify(formData),
        contentType: 'application/json',
        success: function(response) {
            $('#apiKeyModal').modal('hide');
            showAlert(
                editingKeyId ? 'API key updated successfully!' : 'API key created successfully!',
                'success'
            );
            loadApiKeys(currentPage);
            loadApiKeyStats(); // Reload stats after creating/updating

            // Show the new API key if it was just created
            if (!editingKeyId && response.key) {
                showNewApiKeyAlert(response);
            }
        },
        error: function(xhr) {
            showAlert('Error saving API key: ' + (xhr.responseJSON?.message || 'Unknown error'), 'error');
        },
        complete: function() {
            saveBtn.prop('disabled', false);
            $('#saveApiKeyText').text(originalText);
        }
    });
}

// Validate form
function validateForm() {
    const name = $('#keyName').val().trim();
    const scopes = $('input[name="scopes"]:checked').length;
    const expiresAt = $('#expiresAt').val();
    const customDate = $('#customExpiryDate').val();

    if (!name) {
        showAlert('Please enter a name for the API key', 'error');
        $('#keyName').focus();
        return false;
    }

    if (scopes === 0) {
        showAlert('Please select at least one scope', 'error');
        return false;
    }

    if (expiresAt === 'custom' && !customDate) {
        showAlert('Please select a custom expiry date', 'error');
        $('#customExpiryDate').focus();
        return false;
    }

    if (expiresAt === 'custom' && new Date(customDate) <= new Date()) {
        showAlert('Expiry date must be in the future', 'error');
        $('#customExpiryDate').focus();
        return false;
    }

    return true;
}

// Show new API key alert
function showNewApiKeyAlert(apiKey) {
    const alertHtml = `
        <div class="alert alert-success-custom alert-dismissible fade show" role="alert">
            <h5><i class="fas fa-check-circle me-2"></i>API Key Created Successfully!</h5>
            <p class="mb-2">Your new API key has been created. <strong>Please copy it now as you won't be able to see it again.</strong></p>
            <div class="api-key-value mt-2" style="background: #ffffff; border: 2px solid #28a745;">
                <span style="font-family: monospace; font-size: 14px; word-break: break-all;">${apiKey.key}</span>
                <button class="copy-btn" onclick="copyToClipboard('${apiKey.key}')" title="Copy to clipboard">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    $('#alertContainer').html(alertHtml);

    // Auto-dismiss after 30 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 30000);
}







// Delete API key
function deleteApiKey() {
    if (!deletingKeyId) return;

    const deleteBtn = $('#confirmDeleteBtn');
    const originalText = $('#deleteButtonText').text();

    deleteBtn.prop('disabled', true);
    $('#deleteButtonText').html('<span class="loading-spinner"></span>Deleting...');

    $.ajax({
        url: `/api/keys/${deletingKeyId}`,
        method: 'DELETE',
        success: function() {
            $('#deleteConfirmModal').modal('hide');
            showAlert('API key deleted successfully!', 'success');
            loadApiKeys(currentPage);
            loadApiKeyStats(); // Reload stats after deleting
        },
        error: function(xhr) {
            showAlert('Error deleting API key: ' + (xhr.responseJSON?.message || 'Unknown error'), 'error');
        },
        complete: function() {
            deleteBtn.prop('disabled', false);
            $('#deleteButtonText').text(originalText);
            deletingKeyId = null;
        }
    });
}

// Revoke API key
function revokeApiKey() {
    if (!revokingKeyId) return;

    const revokeBtn = $('#confirmRevokeBtn');
    const originalText = $('#revokeButtonText').text();

    revokeBtn.prop('disabled', true);
    $('#revokeButtonText').html('<span class="loading-spinner"></span>Revoking...');

    $.ajax({
        url: `/api/keys/revoke/${revokingKeyId}`,
        method: 'PATCH',
        success: function() {
            $('#revokeConfirmModal').modal('hide');
            showAlert('API key revoked successfully!', 'success');
            loadApiKeys(currentPage);
            loadApiKeyStats(); // Reload stats after revoking
        },
        error: function(xhr) {
            showAlert('Error revoking API key: ' + (xhr.responseJSON?.message || 'Unknown error'), 'error');
        },
        complete: function() {
            revokeBtn.prop('disabled', false);
            $('#revokeButtonText').text(originalText);
            revokingKeyId = null;
        }
    });
};





// Reset form
function resetForm() {
    $('#apiKeyForm')[0].reset();
    $('input[name="scopes"]').prop('checked', false);
    $('#customDateGroup').hide();
    $('#customExpiryDate').attr('required', false);
    editingKeyId = null;
}

// Show loading state
function showLoading(show) {
    if (show) {
        $('#loadingState').show();
        $('#apiKeysTableContainer').hide();
        $('#emptyState').hide();
    } else {
        $('#loadingState').hide();
    }
}

// Show alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success-custom' : 'alert-error-custom';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${icon} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    $('#alertContainer').html(alertHtml);

    // Auto-dismiss success alerts after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            $('.alert').alert('close');
        }, 5000);
    }
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Refresh data every 30 seconds
setInterval(() => {
    loadApiKeys(currentPage);
    loadApiKeyStats();
}, 30000);
</script>