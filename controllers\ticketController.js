const Ticket = require("../models/Ticket");
const ImportHistory = require("../models/ImportHistory");
const fs = require("fs");
const path = require("path");
const { Parser } = require("json2csv");
exports.createTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      priority,
      assignedTo,
      dueDate,
      status,
      tags,
      businessId,
    } = req.body;
    console.log("createTicket req.body", req.body);

    const attachments = [];

    if (req.files && req.files.attachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");
      if (!fs.existsSync(uploadDir))
        fs.mkdirSync(uploadDir, { recursive: true });

      const files = Array.isArray(req.files.attachments)
        ? req.files.attachments
        : [req.files.attachments];

      for (const file of files) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        attachments.push(`/uploads/${fileName}`);
      }
    }

    const ticket = new Ticket({
      title,
      description: description ? description.trim() : undefined,
      type: type ? type.trim() : undefined,
      priority: priority ? priority.trim() : undefined,
      assignee: assignedTo ? assignedTo.trim() : undefined,
      reporter: req.user._id,
      businessId: businessId ? businessId : req.user.businessId,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      status: status ? status.trim() : "Open",
      tags: tags ? JSON.parse(tags) : [],
      attachments,
    });

    const saved = await ticket.save();
    res.status(201).json(saved);
  } catch (err) {
    console.error("Create ticket error:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getTickets = async (req, res) => {
  try {
    const query = {
      isDeleted: false,
    };

    const tickets = await Ticket.find(query)
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });
    if (!tickets.length) {
      return res.status(200).json({ tickets: [] });
    }

    const transformedTickets = tickets.map((ticket) => {
      const plainTicket = ticket.toObject();

      plainTicket.comments = plainTicket.comments.map((comment) => {
        const isCurrentUser =
          comment.user?._id?.toString() === req.user._id.toString();

        return {
          ...comment,
          user: isCurrentUser
            ? { firstName: "You", lastName: "" }
            : comment.user,
        };
      });

      return plainTicket;
    });
    console.log(transformedTickets, "transformedTickets");
    console.log(transformedTickets[0].comments, "transformedTickets");

    res.status(200).json({ tickets: transformedTickets });
  } catch (err) {
    console.error("getTickets error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Get single ticket
exports.getTicketById = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id)
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    const plainTicket = ticket.toObject();

    plainTicket.comments = plainTicket.comments.map((comment) => {
      const isCurrentUser =
        comment.user?._id?.toString() === req.user._id.toString();

      return {
        ...comment,
        user: isCurrentUser ? { firstName: "You", lastName: "" } : comment.user,
      };
    });

    res.status(200).json(plainTicket);
  } catch (err) {
    console.error("getTicketById error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Update ticket
exports.updateTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      status,
      priority,
      assignee,
      dueDate,
      resolutionNote,
      tags,
      existingAttachments,
      newComments,
    } = req.body;

    const update = {
      modifiedDate: new Date(),
    };

    // Update fields if provided
    if (title) update.title = title;
    if (description !== undefined) update.description = description;
    if (type) update.type = type;
    if (priority) update.priority = priority;
    if (assignee) update.assignee = assignee;
    if (dueDate) update.dueDate = new Date(dueDate);

    if (status) {
      update.status = status;
      if (status === "Resolved") {
        update.resolvedDate = new Date();
        if (resolutionNote) update.resolutionNote = resolutionNote;
      }
    }

    // Handle new comments added during editing
    if (newComments) {
      const parsedNewComments = JSON.parse(newComments);
      const transformedComments = parsedNewComments.map((comment) => ({
        message: comment.message,
        user: req.user._id,
        createdAt: new Date(),
      }));

      // Get existing comments and add new ones
      const existingTicket = await Ticket.findById(req.params.id);
      const finalComments = [
        ...(existingTicket.comments || []),
        ...transformedComments,
      ];
      update.comments = finalComments;
    }

    const finalTags = tags ? JSON.parse(tags) : [];

    let finalAttachments = [];

    if (existingAttachments) {
      finalAttachments = JSON.parse(existingAttachments);
    }

    // Handle new file uploads
    if (req.files && req.files.newAttachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");

      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const newFiles = Array.isArray(req.files.newAttachments)
        ? req.files.newAttachments
        : [req.files.newAttachments];

      for (const file of newFiles) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        finalAttachments.push(`/uploads/${fileName}`);
      }
    }

    update.tags = finalTags;
    update.attachments = finalAttachments;

    const ticket = await Ticket.findByIdAndUpdate(
      req.params.id,
      { $set: update },
      { new: true }
    );

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    res.status(200).json(ticket);
  } catch (err) {
    console.error("Ticket update error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Delete ticket
exports.deleteTicket = async (req, res) => {
  try {
    const deleted = await Ticket.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true },
      { new: true }
    );
    if (!deleted) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json({ message: "Ticket marked for deletion deleted" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Toggle visibility of all tickets
exports.toggleAllVisibility = async (req, res) => {
  try {
    const { visible } = req.body;

    await Ticket.updateMany({}, { showOnPublicPage: visible });

    res
      .status(200)
      .json({ message: `All tickets updated to showOnPublicPage: ${visible}` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
exports.toggleSingleVisibility = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    ticket.showOnPublicPage = !ticket.showOnPublicPage;
    await ticket.save();
    res.status(200).json({
      message: `Ticket visibility toggled`,
      showOnPublicPage: ticket.showOnPublicPage,
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

exports.getPublicTickets = async (req, res) => {
  try {
    const tickets = await Ticket.find({
      showOnPublicPage: true,
      isDeleted: false,
    })
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });

    if (!tickets.length) {
      return res.status(200).json({ tickets: [] });
    }

    const transformedTickets = tickets.map((ticket) => {
      const plainTicket = ticket.toObject();

      plainTicket.comments = plainTicket.comments.map((comment) => {
        const isCurrentUser =
          comment.user?._id?.toString() === req.user._id.toString();

        return {
          ...comment,
          user: isCurrentUser
            ? { firstName: "You", lastName: "" }
            : comment.user,
        };
      });

      return plainTicket;
    });

    res.status(200).json({ tickets: transformedTickets });
  } catch (err) {
    console.error("getPublicTickets error:", err);
    res.status(500).json({ message: err.message });
  }
};
exports.addComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { message } = req.body;

    const comment = {
      message,
      user: req.user._id,
      createdAt: new Date(),
    };

    const ticket = await Ticket.findByIdAndUpdate(
      id,
      { $push: { comments: comment } },
      { new: true }
    );

    if (!ticket) {
      return res
        .status(404)
        .json({ message: "Ticket not found", success: false });
    }

    res.status(200).json(ticket);
  } catch (err) {
    console.error("addComment error:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.exportTicketsToCSV = async (req, res) => {
  try {
    const now = new Date();
    const dateStr = now.toISOString().split("T")[0];
    const timeStr = now.toTimeString().split(" ")[0].replace(/:/g, "-");
    const timestamp = `${dateStr}_${timeStr}`;

    const baseFolderPath = path.join(__dirname, "../logs");
    if (!fs.existsSync(baseFolderPath)) {
      fs.mkdirSync(baseFolderPath, { recursive: true });
    }

    const tickets = await Ticket.find({ isDeleted: false })
      .populate("comments.user", "firstName lastName email")
      .populate("businessId", "businessName")
      .populate("reporter", "firstName lastName email")
      .populate("assignee", "firstName lastName email")
      .sort({ createdDate: -1 })
      .lean();

    if (!tickets || tickets.length === 0) {
      return res.status(404).json({ message: "No tickets found" });
    }

    const flattenedTickets = tickets.map((ticket) => ({
      ticketId: ticket._id,
      title: ticket.title,
      description: ticket.description || "",
      status: ticket.status || "Open",
      priority: ticket.priority || "N/A",
      type: ticket.type || "N/A",
      createdDate: ticket.createdDate
        ? new Date(ticket.createdDate).toISOString()
        : "N/A",
      modifiedDate: ticket.modifiedDate
        ? new Date(ticket.modifiedDate).toISOString()
        : "N/A",
      resolvedDate: ticket.resolvedDate
        ? new Date(ticket.resolvedDate).toISOString()
        : "N/A",
      dueDate: ticket.dueDate ? new Date(ticket.dueDate).toISOString() : "N/A",
      assigneeName: ticket.assignee
        ? `${ticket.assignee.firstName} ${ticket.assignee.lastName}`
        : "Unassigned",
      assigneeEmail: ticket.assignee?.email || "N/A",
      reporterName: ticket.reporter
        ? `${ticket.reporter.firstName} ${ticket.reporter.lastName}`
        : "N/A",
      reporterEmail: ticket.reporter?.email || "N/A",
      businessName: ticket.businessId?.businessName || "N/A",
      tags:
        ticket.tags && ticket.tags.length > 0 ? ticket.tags.join(", ") : "N/A",
      attachments:
        ticket.attachments && ticket.attachments.length > 0
          ? ticket.attachments.join(", ")
          : "N/A",
      resolutionNote: ticket.resolutionNote || "N/A",
      showOnPublicPage: ticket.showOnPublicPage ? "Yes" : "No",
      isDeleted: ticket.isDeleted ? "Yes" : "No",
      comments:
        ticket.comments && ticket.comments.length > 0
          ? ticket.comments
              .map((c) => {
                const name = c.user
                  ? `${c.user.firstName} ${c.user.lastName}`
                  : "Unknown";
                const email = c.user?.email || "N/A";
                return `(${name} - ${email}): ${c.message}`;
              })
              .join(" | ")
          : "No comments",
    }));

    const parser = new Parser();
    const csvData = parser.parse(flattenedTickets);

    const fileName = `mixcertificate_tickets_${timestamp}.csv`;
    const filePath = path.join(baseFolderPath, fileName);
    fs.writeFileSync(filePath, csvData);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);

    return res.download(filePath);
  } catch (error) {
    console.error("Error exporting tickets:", error.message);
    return res.status(500).json({ message: "Failed to export tickets to CSV" });
  }
};

exports.importTicketsFromCSV = async (req, res) => {
  try {
  } catch (error) {
    
  }
};


exports.uploadCSVFile = (req, res) => {
  console.log("CSV upload request received");
  console.log("Request files:", req.files);


  try {
    // Ensure we always return JSON
    res.setHeader('Content-Type', 'application/json');

    // Check if file was uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      console.log("No file uploaded");
      return res.status(400).json({ error: "No file uploaded" });
    }

    // Get the uploaded file
    const file = req.files.file;
    console.log("File details:", { name: file.name, size: file.size, mimetype: file.mimetype });

    // Check file type
    if (!file.name.endsWith(".csv")) {
      console.log("Invalid file type:", file.name);
      return res.status(400).json({ error: "Only CSV files are allowed" });
    }

    // Create uploads directory if it doesn't exist
    const uploadDir = path.join(__dirname, "../public/uploads");
    if (!fs.existsSync(uploadDir)) {
      console.log("Creating uploads directory");
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${file.name}`;
    const filePath = path.join(uploadDir, uniqueFilename);
    console.log("Saving file to:", filePath);

    // Move the file to the uploads directory
    file.mv(filePath, function (err) {
      if (err) {
        console.error("Error moving file:", err);
        return res.status(500).json({ error: err.message });
      }

      // Generate file URL
      const baseUrl = `${req.protocol}://${req.get("host")}`;
      const fileUrl = `${baseUrl}/uploads/${uniqueFilename}`;
      console.log("File uploaded successfully. URL:", fileUrl);

      // Return success response with file URL
      return res.status(200).json({
        success: true,
        message: "File uploaded successfully",
        file: {
          originalName: file.name,
          filename: uniqueFilename,
          url: fileUrl,
        },
      });
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    return res.status(500).json({ error: error.message || "Internal server error" });
  }
};