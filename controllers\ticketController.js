const Ticket = require("../models/Ticket");
const fs = require("fs");
const path = require("path");

exports.createTicket = async (req, res) => {
  try {
    const {
      subject,
      description,
      priority,
      tags,
      assignedTo,
      associatedContact,
      associatedCompany,
      chat,
    } = req.body;

    const attachments = [];

    if (req.files && req.files.file) {
      const uploadDir = path.join(__dirname, "../public/uploads");
      if (!fs.existsSync(uploadDir))
        fs.mkdirSync(uploadDir, { recursive: true });

      const files = Array.isArray(req.files.file)
        ? req.files.file
        : [req.files.file];

      for (const file of files) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        attachments.push(`/uploads/${fileName}`);
      }
    }

    const ticket = new Ticket({
      subject,
      description,
      priority,
      createdBy: req.user._id,
      businessId: req.user.businessId,
      assignedTo,

      associatedCompany,
      tags,

      attachments,
    });

    const saved = await ticket.save();
    res.status(201).json(saved);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get all tickets
exports.getTickets = async (req, res) => {
  try {
    const tickets = await Ticket.find();
    res.status(200).json(tickets);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get single ticket
exports.getTicketById = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json(ticket);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Update ticket
exports.updateTicket = async (req, res) => {
  try {
    const { status, ...rest } = req.body;
    const update = { ...rest, modifiedDate: new Date() };
    if (status) {
      update.status = status;
      status === "Resolved" && (update.resolvedDate = new Date());
    }

    const ticket = await Ticket.findByIdAndUpdate(
      req.params.id,
      {
        $set: update,
      },
      {
        new: true,
      }
    );
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json(ticket);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ticket
exports.deleteTicket = async (req, res) => {
  try {
    const deleted = await Ticket.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json({ message: "Ticket deleted" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Toggle visibility of all tickets
exports.toggleAllVisibility = async (req, res) => {
  try {
    const { visible } = req.body;
    await Ticket.updateMany({}, { showOnPublicPage: visible });
    res
      .status(200)
      .json({ message: `All tickets updated to showOnPublicPage: ${visible}` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
exports.toggleSingleVisibility = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    ticket.showOnPublicPage = !ticket.showOnPublicPage;
    await ticket.save();
    res.status(200).json({
      message: `Ticket visibility toggled`,
      showOnPublicPage: ticket.showOnPublicPage,
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
