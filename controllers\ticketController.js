const Ticket = require("../models/Ticket");
const fs = require("fs");
const path = require("path");

exports.createTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      priority,
      assignedTo,
      dueDate,
      status,
      tags,
    } = req.body;

    const attachments = [];

    if (req.files && req.files.attachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");
      if (!fs.existsSync(uploadDir))
        fs.mkdirSync(uploadDir, { recursive: true });

      const files = Array.isArray(req.files.attachments)
        ? req.files.attachments
        : [req.files.attachments];

      for (const file of files) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        attachments.push(`/uploads/${fileName}`);
      }
    }

    const ticket = new Ticket({
      title,
      description: description ? description.trim() : undefined,
      type: type ? type.trim() : undefined,
      priority: priority ? priority.trim() : undefined,
      assignee: assignedTo,
      reporter: req.user._id,
      businessId: req.user.businessId,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      status: status ? status.trim() : undefined,
      tags: tags ? JSON.parse(tags) : [],
      attachments,
    });

    const saved = await ticket.save();
    res.status(201).json(saved);
  } catch (err) {
    console.error("Create ticket error:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getTickets = async (req, res) => {
  try {
    const query = {
      isDeleted: false,
    };

    const tickets = await Ticket.find(query)
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });
    if (!tickets.length) {
      return res.status(200).json({ tickets: [] });
    }

    const transformedTickets = tickets.map((ticket) => {
      const plainTicket = ticket.toObject();

      plainTicket.comments = plainTicket.comments.map((comment) => {
        const isCurrentUser =
          comment.user?._id?.toString() === req.user._id.toString();

        return {
          ...comment,
          user: isCurrentUser
            ? { firstName: "You", lastName: "" }
            : comment.user,
        };
      });

      return plainTicket;
    });
    console.log(transformedTickets, "transformedTickets");
    console.log(transformedTickets[0].comments, "transformedTickets");

    res.status(200).json({ tickets: transformedTickets });
  } catch (err) {
    console.error("getTickets error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Get single ticket
exports.getTicketById = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id)
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    const plainTicket = ticket.toObject();

    plainTicket.comments = plainTicket.comments.map((comment) => {
      const isCurrentUser =
        comment.user?._id?.toString() === req.user._id.toString();

      return {
        ...comment,
        user: isCurrentUser ? { firstName: "You", lastName: "" } : comment.user,
      };
    });

    res.status(200).json(plainTicket);
  } catch (err) {
    console.error("getTicketById error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Update ticket
exports.updateTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      status,
      priority,
      assignee,
      dueDate,
      resolutionNote,
      tags,
      existingAttachments,
      newComments,
    } = req.body;

    const update = {
      modifiedDate: new Date(),
    };

    // Update fields if provided
    if (title) update.title = title;
    if (description !== undefined) update.description = description;
    if (type) update.type = type;
    if (priority) update.priority = priority;
    if (assignee) update.assignee = assignee;
    if (dueDate) update.dueDate = new Date(dueDate);

    if (status) {
      update.status = status;
      if (status === "Resolved") {
        update.resolvedDate = new Date();
        if (resolutionNote) update.resolutionNote = resolutionNote;
      }
    }

    // Handle new comments added during editing
    if (newComments) {
      const parsedNewComments = JSON.parse(newComments);
      const transformedComments = parsedNewComments.map((comment) => ({
        message: comment.message,
        user: req.user._id,
        createdAt: new Date(),
      }));

      // Get existing comments and add new ones
      const existingTicket = await Ticket.findById(req.params.id);
      const finalComments = [
        ...(existingTicket.comments || []),
        ...transformedComments,
      ];
      update.comments = finalComments;
    }

    const finalTags = tags ? JSON.parse(tags) : [];

    let finalAttachments = [];

    if (existingAttachments) {
      finalAttachments = JSON.parse(existingAttachments);
    }

    // Handle new file uploads
    if (req.files && req.files.newAttachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");

      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const newFiles = Array.isArray(req.files.newAttachments)
        ? req.files.newAttachments
        : [req.files.newAttachments];

      for (const file of newFiles) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        finalAttachments.push(`/uploads/${fileName}`);
      }
    }

    update.tags = finalTags;
    update.attachments = finalAttachments;

    const ticket = await Ticket.findByIdAndUpdate(
      req.params.id,
      { $set: update },
      { new: true }
    );

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    res.status(200).json(ticket);
  } catch (err) {
    console.error("Ticket update error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Delete ticket
exports.deleteTicket = async (req, res) => {
  try {
    const deleted = await Ticket.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true },
      { new: true }
    );
    if (!deleted) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json({ message: "Ticket marked for deletion deleted" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Toggle visibility of all tickets
exports.toggleAllVisibility = async (req, res) => {
  try {
    const { visible } = req.body;

    await Ticket.updateMany({}, { showOnPublicPage: visible });

    res
      .status(200)
      .json({ message: `All tickets updated to showOnPublicPage: ${visible}` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
exports.toggleSingleVisibility = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    ticket.showOnPublicPage = !ticket.showOnPublicPage;
    await ticket.save();
    res.status(200).json({
      message: `Ticket visibility toggled`,
      showOnPublicPage: ticket.showOnPublicPage,
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

exports.getPublicTickets = async (req, res) => {
  try {
    const tickets = await Ticket.find({
      showOnPublicPage: true,
      isDeleted: false,
    })
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email")
      .populate({
        path: "comments.user",
        select: "firstName lastName",
      });

    if (!tickets.length) {
      return res.status(200).json({ tickets: [] });
    }

    const transformedTickets = tickets.map((ticket) => {
      const plainTicket = ticket.toObject();

      plainTicket.comments = plainTicket.comments.map((comment) => {
        const isCurrentUser =
          comment.user?._id?.toString() === req.user._id.toString();

        return {
          ...comment,
          user: isCurrentUser
            ? { firstName: "You", lastName: "" }
            : comment.user,
        };
      });

      return plainTicket;
    });

    res.status(200).json({ tickets: transformedTickets });
  } catch (err) {
    console.error("getPublicTickets error:", err);
    res.status(500).json({ message: err.message });
  }
};
exports.addComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { message } = req.body;

    const comment = {
      message,
      user: req.user._id,
      createdAt: new Date(),
    };

    const ticket = await Ticket.findByIdAndUpdate(
      id,
      { $push: { comments: comment } },
      { new: true }
    );

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    res.status(200).json(ticket);
  } catch (err) {
    console.error("addComment error:", err);
    res.status(500).json({ message: err.message });
  }
};
