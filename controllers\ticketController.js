const Ticket = require("../models/Ticket");
const fs = require("fs");
const path = require("path");

exports.createTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      priority,
      assignedTo,
      dueDate,
      status,
      tags,
    } = req.body;

    const attachments = [];

    if (req.files && req.files.attachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");
      if (!fs.existsSync(uploadDir))
        fs.mkdirSync(uploadDir, { recursive: true });

      const files = Array.isArray(req.files.attachments)
        ? req.files.attachments
        : [req.files.attachments];

      for (const file of files) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        attachments.push(`/uploads/${fileName}`);
      }
    }

    const ticket = new Ticket({
      title,
      description: description ? description.trim() : undefined,
      type: type ? type.trim() : undefined,
      priority: priority ? priority.trim() : undefined,
      assignee: assignedTo,
      reporter: req.user._id,
      businessId: req.user.businessId,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      status: status ? status.trim() : undefined,
      tags: tags ? JSON.parse(tags) : [],
      attachments,
    });

    const saved = await ticket.save();
    res.status(201).json(saved);
  } catch (err) {
    console.error("Create ticket error:", err);
    res.status(500).json({ message: err.message });
  }
};

exports.getTickets = async (req, res) => {
  try {
    const userRole = req.user.role;

    const query = {
      isDeleted: false,
    };

    // Only allow full access for Admins and Manager
    const elevatedRoles = ["Admin", "Manager"];
    if (!elevatedRoles.includes(userRole)) {
      query.reporter = req.user._id;
    }

    const tickets = await Ticket.find(query)
      .populate("assignee", "firstName lastName email")
      .populate("reporter", "firstName lastName email");

    res.status(200).json(tickets);
  } catch (err) {
    console.error("getTickets error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Get single ticket
exports.getTicketById = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json(ticket);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Update ticket
exports.updateTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      status,
      priority,
      assignee,
      dueDate,
      resolutionNote,
      tags,
      existingAttachments,
    } = req.body;

    const update = {
      modifiedDate: new Date(),
    };

    // Update fields if provided
    if (title) update.title = title;
    if (description !== undefined) update.description = description;
    if (type) update.type = type;
    if (priority) update.priority = priority;
    if (assignee) update.assignee = assignee;
    if (dueDate) update.dueDate = new Date(dueDate);

    if (status) {
      update.status = status;
      if (status === "Resolved") {
        update.resolvedDate = new Date();
        if (resolutionNote) update.resolutionNote = resolutionNote;
      }
    }

    const finalTags = tags ? JSON.parse(tags) : [];

    let finalAttachments = [];

    if (existingAttachments) {
      finalAttachments = JSON.parse(existingAttachments);
    }

    // Handle new file uploads
    if (req.files && req.files.newAttachments) {
      const uploadDir = path.join(__dirname, "../public/uploads");

      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const newFiles = Array.isArray(req.files.newAttachments)
        ? req.files.newAttachments
        : [req.files.newAttachments];

      for (const file of newFiles) {
        const fileName = `${Date.now()}-${file.name}`;
        const filePath = path.join(uploadDir, fileName);

        await new Promise((resolve, reject) => {
          file.mv(filePath, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        finalAttachments.push(`/uploads/${fileName}`);
      }
    }

    update.tags = finalTags;
    update.attachments = finalAttachments;

    const ticket = await Ticket.findByIdAndUpdate(
      req.params.id,
      { $set: update },
      { new: true }
    );

    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    res.status(200).json(ticket);
  } catch (err) {
    console.error("Ticket update error:", err);
    res.status(500).json({ message: err.message });
  }
};

// Delete ticket
exports.deleteTicket = async (req, res) => {
  try {
    const deleted = await Ticket.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true },
      { new: true }
    );
    if (!deleted) return res.status(404).json({ message: "Ticket not found" });
    res.status(200).json({ message: "Ticket marked for deletion deleted" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Toggle visibility of all tickets
exports.toggleAllVisibility = async (req, res) => {
  try {
    const { visible } = req.body;
    const tickets = await Ticket.find({
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });
    if (tickets.length === 0) {
      return res.status(200).json({ message: `No tickets found` });
    }
    await Ticket.updateMany(
      { businessId: req.user.businessId, createdBy: req.user._id },
      { showOnPublicPage: visible }
    );

    res
      .status(200)
      .json({ message: `All tickets updated to showOnPublicPage: ${visible}` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
exports.toggleSingleVisibility = async (req, res) => {
  try {
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) return res.status(404).json({ message: "Ticket not found" });
    ticket.showOnPublicPage = !ticket.showOnPublicPage;
    await ticket.save();
    res.status(200).json({
      message: `Ticket visibility toggled`,
      showOnPublicPage: ticket.showOnPublicPage,
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Add comment to ticket
exports.addComment = async (req, res) => {
  try {
    const { message } = req.body;
    const ticketId = req.params.id;

    if (!message || !message.trim()) {
      return res.status(400).json({ message: "Comment message is required" });
    }

    const ticket = await Ticket.findById(ticketId);
    if (!ticket) {
      return res.status(404).json({ message: "Ticket not found" });
    }

    // Create new comment
    const newComment = {
      message: message.trim(),
      user: `${req.user.firstName} ${req.user.lastName}`,
      createdAt: new Date(),
    };

    // Add comment to ticket
    ticket.comments.push(newComment);
    ticket.modifiedDate = new Date();

    await ticket.save();

    res.status(201).json({
      message: "Comment added successfully",
      comment: newComment,
    });
  } catch (err) {
    console.error("Add comment error:", err);
    res.status(500).json({ message: err.message });
  }
};
