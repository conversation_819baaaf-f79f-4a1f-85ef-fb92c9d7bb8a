<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", { "title":"Default Email Settings" }) %>
<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>
<style>
  body {
    background: #f8f9fa;
    color: #333;
  }
  .btn-primary {
    background: #5156be;
    border-color: #5156be;
  }
  .btn-primary:hover {
    background: #3f43a7;
    border-color: #3f43a7;
  }
  .table thead {
    background: #5156be;
    color: #fff;
  }
  .modal-header {
    background: #5156be;
    color: #fff;
  }
  .btn-close-white {
    filter: invert(100%);
  }
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  /* DataTable styling improvements */
  #emailSettingsTable_wrapper .dataTables_length,
  #emailSettingsTable_wrapper .dataTables_filter {
    margin-bottom: 1rem;
  }

  #emailSettingsTable_wrapper .dataTables_filter input {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
  }

  #emailSettingsTable_wrapper .dataTables_length select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
   
  }

  /* Button group styling */
  .btn-group .btn {
    margin-right: 2px;
  }

  .btn-group .btn:last-child {
    margin-right: 0;
  }

  /* Form switch styling */
  .form-check-input:checked {
    background-color: #5156be;
    border-color: #5156be;
  }

  /* Badge styling */
  .badge {
    font-size: 0.75rem;
  }

  /* Template editor styling */
  #templateEditor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
  }

  /* Variable instructions styling */
  .variable-instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .variable-tag {
    background: #5156be;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    margin: 0.125rem;
    display: inline-block;
  }

  /* Responsive improvements */
  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column;
    }

    .btn-group .btn {
      margin-bottom: 2px;
      margin-right: 0;
    }
  }

  /* Custom card styling */
  .custom-card-bordered {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
  }
</style>

<%- contentFor('body') %>
<%-include("partials/page-title",{"title": "Default Email Settings", "pagetitle": "Settings" }) %>

<div class="container-fluid px-2 py-2">
  <div class="row">
    <div class="col-12">
      <!-- Add New Email Setting Button -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="mb-0">Email Templates</h4>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#emailSettingModal">
          <i class="bx bx-plus"></i> <span style="color: #ffffff !important;">Add New Email Template</span>
        </button>
      </div>

      <!-- Email Settings Table -->
      <div class="card shadow-sm custom-card-bordered">
        <div class="card-body">
          <div class="table-responsive">
            <table id="emailSettingsTable" class="table table-hover align-middle w-100 mb-0">
              <thead class="table-light">
                <tr>
                  <th>Email</th>
                  <th>Template Preview</th>
                  <th>Default</th>
                  <th>Created By</th>
                  <th>Created Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Email settings will be loaded here by DataTable -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add/Edit Email Setting Modal -->
<div class="modal fade" id="emailSettingModal" tabindex="-1" aria-labelledby="emailSettingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 style="color: #ffffff;" class="modal-title" id="emailSettingModalLabel">Add New Email Template</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="emailSettingForm">
        <div class="modal-body">
          <input type="hidden" id="emailSettingId" />

          <!-- Variable Instructions -->
          <div class="variable-instructions">
            <h6><i class="bx bx-info-circle"></i> Dynamic Variables Instructions</h6>
            <p class="mb-2">You can use the following dynamic variables in your email template:</p>
            <div class="mb-2">
              <strong>Available Variables:</strong><br>
              <span class="variable-tag">{{title}}</span>
              <span class="variable-tag">{{description}}</span>
              <span class="variable-tag">{{designId}}</span>
              <span class="variable-tag">{{recipients}}</span>
              <span class="variable-tag">{{certificates}}</span>
            </div>
            <small class="text-muted">
              <i class="bx bx-lightbulb"></i>
              These variables will be automatically replaced with actual event data when emails are sent.
            </small>
          </div>

          <!-- Email Field -->
          <div class="mb-3">
            <label for="emailAddress" class="form-label">Email Address <span class="text-danger">*</span></label>
            <input type="email" class="form-control" id="emailAddress" required
                   placeholder="Enter email address (e.g., <EMAIL>)">
            <div class="form-text">This email will be used as the sender address for notifications.</div>
          </div>

          <!-- Template Field -->
          <div class="mb-3">
            <label for="templateEditor" class="form-label">HTML Email Template <span class="text-danger">*</span></label>
            <textarea class="form-control" id="templateEditor" rows="15" required
                      placeholder="Enter your HTML email template here..."><!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Notification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
        <h2 style="color: #5156be; text-align: center; margin-bottom: 30px;">Certificate Ready!</h2>

        <p>Dear Recipient,</p>

        <p>We're excited to inform you that your certificate for <strong>{{title}}</strong> is now ready!</p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #5156be; margin-top: 0;">Event Details:</h3>
            <p><strong>Title:</strong> {{title}}</p>
            <p><strong>Description:</strong> {{description}}</p>
        </div>

        <p>You can download your certificate or view it online using the link provided.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #5156be; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Download Certificate</a>
        </div>

        <p>Thank you for your participation!</p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent automatically. Please do not reply to this email.
        </p>
    </div>
</body>
</html></textarea>
            <div class="form-text">
              Write your HTML email template here. Use the dynamic variables shown above to personalize the content.
            </div>
          </div>

          <!-- Set as Default -->
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="setAsDefault">
            <label class="form-check-label" for="setAsDefault">
              Set as default email template
            </label>
            <div class="form-text">If checked, this will become the default template for new events.</div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" id="saveEmailSettingBtn">
            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
            Save Email Template
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete this email template?</p>
        <p class="text-muted">This action cannot be undone.</p>
        <input type="hidden" id="deleteEmailSettingId" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
          Delete Template
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>
<!-- DataTables JS -->
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>

<script>
$(function() {
  var currentEmailSettingId = null;
  var table;

  // Initialize DataTable
  function initializeDataTable() {
    table = $("#emailSettingsTable").DataTable({
      responsive: true,
      searching: true,
      ordering: true,
      pageLength: 10,
      autoWidth: false,
      lengthMenu: [
        [10, 25, 50, 100],
        [10, 25, 50, 100],
      ],
      order: [[4, "desc"]], // Order by Created Date (index 4)
      columnDefs: [
        { orderable: false, targets: [2, 5] }, // Disable ordering for Default toggle and Actions columns
        { className: "text-center", targets: [2] }, // Center align Default column
        { width: "20%", targets: 0 }, // Email
        { width: "30%", targets: 1 }, // Template Preview
        { width: "10%", targets: 2 }, // Default
        { width: "15%", targets: 3 }, // Created By
        { width: "12%", targets: 4 }, // Created Date
        { width: "13%", targets: 5 }  // Actions
      ],
      language: {
        
        lengthMenu: "Show _MENU_ templates per page",
    emptyTable: "No email templates found",
        zeroRecords: "No matching templates found"
      },
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
           '<"row"<"col-sm-12"tr>>' +
           '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
      drawCallback: function() {
        // Re-bind event handlers after table redraw
        bindEventHandlers();
      }
    });
  }

  // Load email settings data
  function loadEmailSettings() {
    $.get('/api/default-email-setting/all', function(response) {
      // Clear existing data
      table.clear();

      // Add new data
      $.each(response.data, function(i, setting) {
        const defaultToggle = `
          <div class='form-check form-switch d-flex justify-content-center'>
            <input type='checkbox' class='form-check-input toggle-default' data-id='${setting._id}' ${setting.default ? 'checked' : ''}>
          </div>
        `;

        const templatePreview = setting.template.length > 100 ?
          setting.template.substring(0, 100) + '...' :
          setting.template;

        const createdBy = setting.createdBy ?
          `${setting.createdBy.firstName} ${setting.createdBy.lastName}` :
          'Unknown';

        const createdDate = `<small class='text-muted'>${new Date(setting.createdAt).toLocaleDateString()}</small>`;

        const actions = `
          <div class="btn-group" role="group">
            <button class='btn btn-sm btn-outline-primary edit-email-setting' data-id='${setting._id}' title="Edit Template">
              <i class="bx bx-edit"></i>
            </button>
            <button class='btn btn-sm btn-outline-danger delete-email-setting' data-id='${setting._id}' title="Delete Template">
              <i class="bx bx-trash"></i>
            </button>
          </div>
        `;

        table.row.add([
          setting.email,
          `<div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${setting.template}">${templatePreview}</div>`,
          defaultToggle,
          createdBy,
          createdDate,
          actions
        ]);
      });

      // Draw the table
      table.draw();
    }).fail(function() {
      showToast('Error loading email settings', 'danger');
    });
  }

  // Bind event handlers (called after DataTable draw)
  function bindEventHandlers() {
    // Remove existing handlers to prevent duplicates
    $('.toggle-default, .edit-email-setting, .delete-email-setting').off();

    // Toggle default setting
    $('.toggle-default').on('change', function() {
      var id = $(this).data('id');
      var isDefault = $(this).is(':checked');

      $.ajax({
        url: '/api/default-email-setting/' + id + '/toggle-default',
        method: 'PATCH',
        contentType: 'application/json',
        success: function() {
          loadEmailSettings(); // Reload to update all toggles
          showToast('Default email template updated!', 'success');
        },
        error: function() {
          showToast('Error updating default setting', 'danger');
          // Revert the toggle on error
          $('.toggle-default[data-id="' + id + '"]').prop('checked', !isDefault);
        }
      });
    });

    // Edit email setting
    $('.edit-email-setting').on('click', function() {
      var id = $(this).data('id');

      // Find the setting data from the current table
      $.get('/api/default-email-setting/all', function(response) {
        const setting = response.data.find(s => s._id === id);
        if (setting) {
          currentEmailSettingId = setting._id;
          $('#emailSettingId').val(setting._id);
          $('#emailAddress').val(setting.email);
          $('#templateEditor').val(setting.template);
          $('#setAsDefault').prop('checked', setting.default);
          $('#emailSettingModalLabel').text('Edit Email Template');
          $('#emailSettingModal').modal('show');
        }
      }).fail(function() {
        showToast('Error loading email setting details', 'danger');
      });
    });

    // Delete email setting
    $('.delete-email-setting').on('click', function() {
      var id = $(this).data('id');
      $('#deleteEmailSettingId').val(id);
      $('#deleteConfirmModal').modal('show');
    });
  }

  // Initialize DataTable and load data on page load
  initializeDataTable();
  loadEmailSettings();

  // Form submission
  $('#emailSettingForm').submit(function(e) {
    e.preventDefault();

    const $saveBtn = $('#saveEmailSettingBtn');
    const $spinner = $saveBtn.find('.spinner-border');
    const originalText = $saveBtn.text();

    $saveBtn.prop('disabled', true);
    $spinner.removeClass('d-none');
    $saveBtn.html('<span class="spinner-border spinner-border-sm" role="status"></span> Saving...');

    const emailSettingData = {
      email: $('#emailAddress').val(),
      template: $('#templateEditor').val(),
      default: $('#setAsDefault').is(':checked')
    };

    const url = currentEmailSettingId ?
      '/api/default-email-setting/' + currentEmailSettingId :
      '/api/default-email-setting';
    const method = currentEmailSettingId ? 'PUT' : 'POST';

    $.ajax({
      url: url,
      method: method,
      contentType: 'application/json',
      data: JSON.stringify(emailSettingData),
      success: function() {
        $('#emailSettingModal').modal('hide');
        loadEmailSettings();
        resetForm();
        showToast(currentEmailSettingId ? 'Email template updated successfully!' : 'Email template created successfully!', 'success');
      },
      error: function(xhr) {
        const errorMessage = xhr.responseJSON && xhr.responseJSON.message ?
          xhr.responseJSON.message : 'Error saving email template';
        showToast(errorMessage, 'danger');
      },
      complete: function() {
        $saveBtn.prop('disabled', false);
        $spinner.addClass('d-none');
        $saveBtn.text(originalText);
      }
    });
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    const id = $('#deleteEmailSettingId').val();
    const $deleteBtn = $(this);
    const $spinner = $deleteBtn.find('.spinner-border');
    const originalText = $deleteBtn.text();

    $deleteBtn.prop('disabled', true);
    $spinner.removeClass('d-none');
    $deleteBtn.html('<span class="spinner-border spinner-border-sm" role="status"></span> Deleting...');

    $.ajax({
      url: '/api/default-email-setting/' + id,
      method: 'DELETE',
      success: function() {
        $('#deleteConfirmModal').modal('hide');
        loadEmailSettings();
        showToast('Email template deleted successfully!', 'success');
      },
      error: function() {
        showToast('Error deleting email template', 'danger');
      },
      complete: function() {
        $deleteBtn.prop('disabled', false);
        $spinner.addClass('d-none');
        $deleteBtn.text(originalText);
      }
    });
  });

  // Reset form function
  function resetForm() {
    $('#emailSettingForm')[0].reset();
    $('#emailSettingId').val('');
    $('#setAsDefault').prop('checked', false);
    currentEmailSettingId = null;

    // Reset template to default
    $('#templateEditor').val(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Notification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #f8f9fa; padding: 30px; border-radius: 10px;">
        <h2 style="color: #5156be; text-align: center; margin-bottom: 30px;">Certificate Ready!</h2>

        <p>Dear Recipient,</p>

        <p>We're excited to inform you that your certificate for <strong>{{title}}</strong> is now ready!</p>

        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #5156be; margin-top: 0;">Event Details:</h3>
            <p><strong>Title:</strong> {{title}}</p>
            <p><strong>Description:</strong> {{description}}</p>
        </div>

        <p>You can download your certificate or view it online using the link provided.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="#" style="background: #5156be; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Download Certificate</a>
        </div>

        <p>Thank you for your participation!</p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent automatically. Please do not reply to this email.
        </p>
    </div>
</body>
</html>`);
  }

  // Reset form when modal is hidden
  $('#emailSettingModal').on('hidden.bs.modal', function() {
    resetForm();
    $('#emailSettingModalLabel').text('Add New Email Template');
  });

  // Show modal for adding new email setting
  $('[data-bs-target="#emailSettingModal"]').on('click', function() {
    resetForm();
    currentEmailSettingId = null;
    $('#emailSettingModalLabel').text('Add New Email Template');
  });
});
</script>