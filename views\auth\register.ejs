<%- contentFor('HeaderCss') %>
<%-include("../partials/title-meta",{"title":"Register"}) %> <%-
contentFor('body') %>


<div class="auth-page min-vh-100">
  <div class="container-fluid h-100 p-0">
    <div class="row g-0 h-100">
      <div class="col-xxl-3 col-lg-4 col-md-5 h-100">
        <div class="auth-full-page-content d-flex p-sm-5 p-4 h-100">
          <div class="w-100 d-flex flex-column">
            <div class="d-flex flex-column h-100">
              <div class="mb-md-1 text-center">
                <a href="index" class="d-block auth-logo">
                  <img src="/assets/images/logo-sm.svg" alt="" height="28" />
                  <span class="logo-txt">MixCertificate</span>
                </a>
              </div>
              <div class="auth-content my-auto">
                <div class="text-center">
                  <h5 class="mb-0">Register Account</h5>
                  <p class="text-muted mt-1">
                    Get your free MixCertificate account now.
                  </p>
                </div>
                <% if(error.length> 0) { %>
                <div
                  class="alert alert-danger bg-danger border-danger text-white mb-4"
                  role="alert"
                >
                  <%= error %>
                </div>
                <% } %> <% if(message.length> 0) { %>
                <div
                  class="alert alert-success bg-success border-success text-white mb-4"
                  role="alert"
                >
                  <%= message %>
                </div>
                <% } %>
                <form
                  class="needs-validation mt-4 signupForm"
                  novalidate
                  action="/signup"
                  method="post"
                >
                  <div class="mb-3">
                    <label for="firstName" class="form-label">First Name</label>
                    <input
                      type="text"
                      class="form-control"
                      id="firstName"
                      name="firstName"
                      required
                      placeholder="First Name"
                      required
                    />
                    <div class="invalid-feedback">
                      Please Enter First Name <small></small>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="lastName" class="form-label">Last Name</label>
                    <input
                      type="text"
                      class="form-control"
                      id="lastName"
                      name="lastName"
                      required
                      placeholder="Last Name"
                      required
                    />
                    <div class="invalid-feedback">
                      Please Enter Last Name <small></small>
                    </div>
                  </div>
                  <div
                    class="mb-3 d-none"
                    title="shown in app when you are loged in"
                  >
                    <label for="displayName" class="form-label"
                      >Display Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="displayName"
                      name="displayName"
                      required
                      placeholder="Name"
                      required
                    />
                    <div class="invalid-feedback">
                      Please Enter Display Name
                      <small>(shown in app when you are loged in)</small>
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="useremail" class="form-label">Email</label>
                    <input
                      type="email"
                      class="form-control"
                      id="useremail"
                      name="email"
                      required
                      placeholder="Enter email"
                      required
                    />
                    <div class="invalid-feedback">Please Enter Email</div>
                  </div>
                  <div class="mb-3">
                    <label for="userpassword" class="form-label"
                      >Password</label
                    >
                    <div class="input-group auth-pass-inputgroup">
                      <input
                        type="password"
                        name="password"
                        class="form-control"
                        id="userpassword"
                        minlength="5"
                        required
                        placeholder="Enter password"
                        required
                        aria-describedby="password-addon"
                        aria-label="Password"
                      />
                      <button
                        class="btn btn-light shadow-none ms-0"
                        type="button"
                        id="password-addon"
                      >
                        <i class="mdi mdi-eye-outline"></i>
                      </button>
                    </div>
                  </div>
                  <div class="mb-4">
                    <p class="mb-0">
                      By registering you agree to the MixCommerce
                      <a
                        href="https://www.mixcommerce.co/terms-of-service/"
                        target="_blank"
                        class="text-primary"
                        >Terms of Use</a
                      >
                      <a
                        href="https://www.mixcommerce.co/privacy-policy/"
                        target="_blank"
                        class="text-primary"
                        >Privacy Policy</a
                      >
                    </p>
                  </div>
                  <div class="mb-3">
                    <button
                      class="btn btn-primary w-100 waves-effect waves-light registerBtn"
                      type="submit"
                    >
                      Register
                    </button>
                  </div>

                        <div class="mb-3 text-center">
                    <a
                      href="/auth/google"
                      class="btn btn-light w-100 d-flex align-items-center justify-content-center border"
                    >
                      <img
                        src="https://developers.google.com/identity/images/g-logo.png"
                        alt="Google"
                        style="width: 20px; height: 20px; margin-right: 10px"
                      />
                      <span>Sign in with Google</span>
                    </a>
                  </div>
                      <div class="mb-3 text-center">
                    <a
                      href="/auth/microsoft"
                      class="btn btn-light w-100 d-flex align-items-center justify-content-center border"
                    >
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg"
                        alt="Microsoft"
                        style="width: 20px; height: 20px; margin-right: 10px"
                      />

                      <span>Sign in with Microsoft</span>
                    </a>
                  </div>
                </form>

                <div class="mt-4 pt-2 text-center d-none">
                  <div class="signin-other-title">
                    <h5 class="font-size-14 mb-3 text-muted fw-medium">
                      - Sign up using -
                    </h5>
                  </div>

                  <ul class="list-inline mb-0">
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-primary text-white border-primary"
                      >
                        <i class="mdi mdi-facebook"></i>
                      </a>
                    </li>
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-info text-white border-info"
                      >
                        <i class="mdi mdi-twitter"></i>
                      </a>
                    </li>
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-danger text-white border-danger"
                      >
                        <i class="mdi mdi-google"></i>
                      </a>
                    </li>
                  </ul>
                </div>

                <div class=" text-center">
                  <p class="text-muted mb-0">
                    Already have an account ?
                    <a href="/login" class="text-primary fw-semibold">
                      Login
                    </a>
                  </p>
                </div>
              </div>
              <div class="mt-1 text-center">
                <p class="mb-0">
                  ©
                  <script>
                    document.write(new Date().getFullYear());
                  </script>
                  MixCertificate . Crafted with
                  <i alt="love" class="mdi mdi-heart text-danger"></i> by
                  <a target="_blank" href="https://www.mixcommerce.co/"
                    >MixCommerce</a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- end auth full page content -->
      </div>
      <!-- end col -->
      <div class="col-xxl-9 col-lg-8 col-md-7 d-none d-md-block">
        <div class="auth-bg h-100 pt-md-5 p-4 d-flex">
          <div class="bg-overlay bg-primary"></div>
          <ul class="bg-bubbles">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
          </ul>
          <!-- end bubble effect -->
          <div class="row justify-content-center align-items-center">
            <div class="col-xl-7">
              <div class="p-0 p-sm-4 px-xl-0">
                <div
                  id="reviewcarouselIndicators"
                  class="carousel slide"
                  data-bs-ride="carousel"
                >
                  <div
                    class="carousel-indicators carousel-indicators-rounded justify-content-start ms-0 mb-0"
                  >
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="0"
                      class="active"
                      aria-current="true"
                      aria-label="Slide 1"
                    ></button>
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="1"
                      aria-label="Slide 2"
                    ></button>
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="2"
                      aria-label="Slide 3"
                    ></button>
                  </div>
                  <!-- end carouselIndicators -->
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “I feel confident imposing change on myself. It's a
                          lot more progressing fun than looking back. That's why
                          I ultricies enim at malesuada nibh diam on tortor
                          neaded to throw curve balls.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                              <img
                                src="/assets/images/users/avatar-1.jpg"
                                class="avatar-md img-fluid rounded-circle"
                                alt="..."
                              />
                            </div>
                            <div class="flex-grow-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Richard Drews
                              </h5>
                              <p class="mb-0 text-white-50">Web Designer</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="carousel-item">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “Our task must be to free ourselves by widening our
                          circle of compassion to embrace all living creatures
                          and the whole of quis consectetur nunc sit amet semper
                          justo. nature and its beauty.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                              <img
                                src="/assets/images/users/avatar-2.jpg"
                                class="avatar-md img-fluid rounded-circle"
                                alt="..."
                              />
                            </div>
                            <div class="flex-grow-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Rosanna French
                              </h5>
                              <p class="mb-0 text-white-50">Web Developer</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="carousel-item">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “I've learned that people will forget what you said,
                          people will forget what you did, but people will never
                          forget how donec in efficitur lectus, nec lobortis
                          metus you made them feel.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <img
                              src="/assets/images/users/avatar-3.jpg"
                              class="avatar-md img-fluid rounded-circle"
                              alt="..."
                            />
                            <div class="flex-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Ilse R. Eaton
                              </h5>
                              <p class="mb-0 text-white-50">Manager</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- end carousel-inner -->
                </div>
                <!-- end review carousel -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- end container fluid -->
</div>
<%- contentFor('FooterJs') %>
<script>
  document.querySelector(".signupForm").addEventListener("submit", function () {
    const registerBtn = document.querySelector(".registerBtn");
    registerBtn.disabled = true;
    registerBtn.innerHTML = "Processing...";
  });
  document
    .getElementById("password-addon")
    .addEventListener("click", function () {
      const passwordInputField = document.getElementById("userpassword");
      if (passwordInputField.type === "password") {
        passwordInputField.type = "text";
      } else {
        passwordInputField.type = "password";
      }
    });
</script>
