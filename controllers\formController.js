const Form = require("../models/Form");
const sendEmail = require("../utils/email");
const axios = require("axios");
const { createLog } = require("../controllers/logController");
// Create a new form
const createForm = async (req, res) => {
  try {
    const userId = req.user._id;
    const businessId = req.user.businessId;

    const existingForms = await Form.find({ businessId, createdBy: userId });

    const isDefault = existingForms.length === 0 || req.body.isDefault;

    if (isDefault) {
      await Form.updateMany(
        { businessId, createdBy: userId },
        { $set: { isDefault: false } }
      );
    }

    const form = new Form({
      ...req.body,
      businessId,
      createdBy: userId,
      isDefault,
    });

    await form.save();
    await createLog(
      {
        eventType: "Form",
        action: "Create",
        target: form._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.status(201).json({ success: true, data: form });
  } catch (error) {
    res.status(400).json({ success: false, message: error.message });
  }
};

// Get all forms
const getAllForms = async (req, res) => {
  try {
    const forms = await Form.find({
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });
    res.status(200).json(forms);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Get a single form by ID
const getFormById = async (req, res) => {
  try {
    const form = await Form.findOne({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });
    if (!form) {
      return res
        .status(404)
        .json({ success: false, message: "Form not found" });
    }
    res.status(200).json(form);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Update a form by ID
const updateForm = async (req, res) => {
  try {
    const existingForms = await Form.find({businessId: req.user.businessId, createdBy: req.user._id });
    if (req.body.isDefault && existingForms.length > 0) {
      await Form.updateMany(
        { businessId: req.user.businessId, createdBy: req.user._id },
        { $set: { isDefault: false } }
      );
    }
    const form = await Form.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
        createdBy: req.user._id,
      },
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );
    if (!form) {
      return res
        .status(404)
        .json({ success: false, message: "Form not found" });
    }
    await createLog(
      {
        eventType: "Form",
        action: "Update",
        target: form._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res.status(200).json(form);
  } catch (error) {
    res.status(400).json({ success: false, message: error.message });
  }
};

// Delete a form by ID
const deleteForm = async (req, res) => {
  try {
    const form = await Form.findOneAndDelete({
      _id: req.params.id,
      businessId: req.user.businessId,
      createdBy: req.user._id,
    });
    if (!form) {
      return res
        .status(404)
        .json({ success: false, message: "Form not found" });
    }
    await createLog(
      {
        eventType: "Form",
        action: "Delete",
        target: form._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res
      .status(200)
      .json({ success: true, message: "Form deleted successfully" });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Enable/Disable form
const enableDisableForm = async (req, res) => {
  try {
    const form = await Form.findOneAndUpdate(
      {
        _id: req.params.id,
        businessId: req.user.businessId,
        createdBy: req.user._id,
      },
      { isEnabled: req.body.isEnabled },
      { new: true }
    );
    if (!form) {
      return res
        .status(404)
        .json({ success: false, message: "Form not found" });
    }
    res.status(200).json({
      success: true,
      message: `Form ${
        req.body.isEnabled ? "enabled" : "disabled"
      } successfully`,
      form,
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Toggle default form
const toggleDefaultForm = async (req, res) => {
  try {
    await Form.updateMany(
      { businessId: req.user.businessId, createdBy: req.user._id },
      { $set: { isDefault: false } }
    );
    const form = await Form.findByIdAndUpdate(
      req.params.id,
      { isDefault: req.body.isDefault },
      { new: true }
    );
    if (!form) {
      return res
        .status(404)
        .json({ success: false, message: "Form not found" });
    }
    res
      .status(200)
      .json({ success: true, message: `Form default status updated`, form });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

const submitForm = async (req, res) => {
  try {
    const { name, email, phone, note } = req.body;

    const defaultForm = await Form.findOne({
      isDefault: true,
      isEnabled: true,
    });

    if (!defaultForm) {
      return res.status(500).json({
        success: false,
        message: "No Default Form Found",
      });
    }
    console.log("reached");

    const message = `
      <h3>New Form Submission</h3>
      <p><strong>Name:</strong> ${name}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Phone:</strong> ${phone}</p>
      <p><strong>Note:</strong> ${note}</p>
    `;

    // Send to custom email if enabled
    if (
      defaultForm.emailNotifications?.sendToCustom &&
      defaultForm.emailNotifications?.customEmail
    ) {
      await sendEmail({
        email: defaultForm.emailNotifications.customEmail,
        subject: "New Form Submission - Custom Notification",
        message,
      });
      console.log("done 1");
    }

    // Send confirmation to candidate if enabled
    if (defaultForm.emailNotifications?.sendToCandidate) {
      await sendEmail({
        email,
        subject: "Thank You for Your Submission",
        message: `
          <p>Dear ${name},</p>
          <p>Thank you for submitting the form. We have received your details and will get back to you shortly.</p>
          <hr/>
           `,
      });
      console.log("done 2");
    }

    // Send webhook if URL is present
    if (defaultForm.webhookUrl) {
      sendWebhookWithRetry(
        defaultForm.webhookUrl,
        { name, email, phone, note },
        null
      );
    }

    await createLog(
      {
        eventType: "Form",
        action: "Submission Received",
        target: defaultForm._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res
      .status(200)
      .json({ success: true, message: "Form submitted successfully." });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

module.exports = {
  createForm,
  getAllForms,
  getFormById,
  updateForm,
  deleteForm,
  enableDisableForm,
  toggleDefaultForm,
  submitForm,
};

async function sendWebhookWithRetry(url, body, signature = null, attempt = 1) {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    if (signature) {
      headers["X-Signature"] = signature;
    }

    await axios.post(url, body, { headers });

    console.log(`✅ Webhook sent successfully to ${url} (Attempt ${attempt})`);
  } catch (err) {
    console.error(
      `❌ Failed to send webhook to ${url} (Attempt ${attempt}):`,
      err.message
    );

    if (attempt < 3) {
      const delay = Math.pow(2, attempt) * 1000;
      console.log(`🔁 Retrying in ${delay / 1000} seconds...`);

      setTimeout(() => {
        sendWebhookWithRetry(url, body, signature, attempt + 1);
      }, delay);
    } else {
      console.error(`🛑 Max retries reached. Giving up on ${url}`);
    }
  }
}
