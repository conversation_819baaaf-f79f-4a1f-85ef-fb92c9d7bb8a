const path = require("path");
const fs = require("fs");
const Migration = require("../models/migration");
const Event = require("../models/Event");
const Contact = require("../models/Contact");
const Design = require("../models/Design");
const Badge = require("../models/Badge");
const Certificate = require("../models/Certificate");
const User = require("../models/User");
const File = require("../models/File");
const { Parser } = require("json2csv");
const archiver = require("archiver");
const sendEmail = require("../utils/email");
const { createLog } = require("../controllers/logController");

async function getModelsData(body) {
  const dataMap = {};
  if (body.events) {
    dataMap.events = await Event.find({})
      .populate("designId contactList creatorId")
      .lean();
  }
  if (body.contacts) {
    dataMap.contacts = await Contact.find({}).populate("creatorId").lean();
  }
  if (body.designs) {
    dataMap.designs = await Design.find({}).lean();
  }
  if (body.badges) {
    dataMap.badges = await Badge.find({}).lean();
  }
  if (body.certificates) {
    dataMap.certificates = await Certificate.find({})
      .populate("contactId")
      .lean();
  }
  if (body.users) {
    dataMap.users = await User.find({})
      .populate("businessId")
      .lean()
      .select(
        "-password -resetPasswordToken -verificationToken -resetPasswordExpires"
      );
  }
  if (body.files) {
    dataMap.files = await File.find({}).populate("uploadedBy").lean();
  }

  return dataMap;
}
exports.createMigration = async (req, res) => {
  try {
    const now = new Date();
    const dateStr = now.toISOString().split("T")[0];
    const timeStr = now.toTimeString().split(" ")[0].replace(/:/g, "-"); // "HH-MM-SS"
    const timestamp = `${dateStr}_${timeStr}`;
    const folderName = `mixcertificate_migrations_${timestamp}`;
    const migrationsDir = path.join(__dirname, "../migrations");
    const baseFolderPath = path.join(migrationsDir, folderName);

    // Create the folder where CSVs will go
    if (!fs.existsSync(baseFolderPath)) {
      fs.mkdirSync(baseFolderPath, { recursive: true });
    }

    const result = await getModelsData(req.body);

    const csvFiles = [];

    for (const [key, data] of Object.entries(result)) {
      if (!Array.isArray(data) || data.length === 0) continue;
      const parser = new Parser();
      const csv = parser.parse(data);
      const csvPath = path.join(baseFolderPath, `${key}.csv`);
      fs.writeFileSync(csvPath, csv);
      csvFiles.push(csvPath);
    }

    if (req.body.certificates) {
      const certFolderPath = path.join(baseFolderPath, "certificates");
      fs.mkdirSync(certFolderPath, { recursive: true });

      const certificates = await Certificate.find({}).populate("contactId");

      const publicDir = path.join(__dirname, "../public");

      for (const cert of certificates) {
        const pdfSrcPath = path.join(publicDir, cert.pdfURL);
        const certFileName = `${cert.contactId?.fullName || "N/A"}-${
          cert.certificateId
        }.pdf`.replace(/[^a-z0-9\-\.]/gi, "_");
        const pdfDestPath = path.join(certFolderPath, certFileName);

        if (fs.existsSync(pdfSrcPath)) {
          fs.copyFileSync(pdfSrcPath, pdfDestPath);
        }
      }

      await createLog(
        {
          eventType: "Certificate",
          action: "Export",
          target: certificates.map(cert=>cert._id).join(","),
        },
        {
          user: {
            _id: req.user?._id,
            businessId: req.user?.businessId,
            role: req.user?.role,
          },
          ip: req.ip,
          headers: req.headers["user-agent"] || "Unknown",
        },
        res
      );
    }

    const zipFileName = `${folderName}.zip`;
    const zipFilePath = path.join(migrationsDir, zipFileName);
    const output = fs.createWriteStream(zipFilePath);
    const archive = archiver("zip", {
      zlib: { level: 9 }, // Sets the compression level.
    });

    archive.pipe(output);
    archive.directory(baseFolderPath, false);
    await archive.finalize();
    output.on("close", async () => {
      const stats = fs.statSync(zipFilePath);
      const fileSizeInBytes = stats.size;

      const migrationDoc = new Migration({
        migrationName: folderName,
        fileName: zipFileName,
        filePath: `/migrations/${zipFileName}`,
        fileSize: fileSizeInBytes,
        modelsIncluded: Object.keys(result),
        status: "completed",
        createdBy: req.user._id,
        businessId: req.user.businessId,
      });

      await migrationDoc.save();

      sendEmail({
        email: req.user.email,
        subject: "✅ Your Migration is Ready – Download Now",
        message: `
          <div style="max-width: 600px; margin: 20px auto; padding: 30px; background-color: #ffffff; font-family: Arial, sans-serif; border: 1px solid #ccc; border-radius: 8px;">
            <div style="text-align: center;">
              <h2 style="color: #5156be; margin-bottom: 20px;">🎉 Migration Completed</h2>
            </div>
            <p style="font-size: 16px; color: #333;">Hi <strong>${
              req.user.firstName && req.user.lastName
                ? req.user.firstName + " " + req.user.lastName
                : "there"
            }</strong>,</p>
            <p style="font-size: 15px; color: #333;">
              Your migration <strong>"${folderName}"</strong> has been successfully completed and archived.
            </p>
            <p style="font-size: 15px; color: #333;">
              Click the button below to download your migration ZIP file:
            </p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${req.protocol}://${req.get(
          "host"
        )}/api/migration/download/${migrationDoc._id}"
                 style="background-color: #5156be; color: #fff; padding: 12px 25px; text-decoration: none; font-size: 16px; border-radius: 6px; display: inline-block;">
                Download Migration ZIP
              </a>
            </div>
            <p style="font-size: 14px; color: #333;">
              If you did not request this migration or need assistance, please contact our support team.
            </p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;" />
            <p style="font-size: 13px; color: #74788d; text-align: center;">
              © ${new Date().getFullYear()} MixCertificate. All rights reserved.
            </p>
          </div>
        `,
      });

      res.status(200).json({
        message: "Migration completed and saved",
        zipFile: `/migrations/${zipFileName}`,
        migration: migrationDoc,
        success: true,
      });
    });
  } catch (error) {
    console.error("Migration error:", error);
    res.status(400).json({ error: error.message });
  }
};
exports.getAllMigrations = async (_, res) => {
  try {
    const migrations = await Migration.find({}).populate("businessId");
    res.status(200).json({ migrations, success: true });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

exports.downloadMigration = async (req, res) => {
  try {
    const migrationId = req.params.id;
    const migration = await Migration.findById(migrationId);

    if (!migration) {
      return res.status(404).json({ error: "Migration not found" });
    }

    const migrationZipPath = path.resolve(
      __dirname,
      "../migrations",
      path.basename(migration.filePath)
    );

    if (!fs.existsSync(migrationZipPath)) {
      return res.status(404).json({ error: "Migration ZIP file not found" });
    }

    res.download(migrationZipPath, migration.fileName, (err) => {
      if (err) {
        console.error("File download error:", err.message);
        if (!res.headersSent) {
          return res.status(500).json({
            error: "An error occurred while downloading the migration file.",
          });
        }
      } else {
        console.log(`Migration file downloaded: ${migration.fileName}`);
      }
    });
  } catch (error) {
    console.error("Download migration error:", error.message);
    res.status(400).json({ error: error.message });
  }
};

exports.deleteMigration = async (req, res) => {
  try {
    const migrationId = req.params.id;
    const migration = await Migration.findById(migrationId);
    if (!migration) {
      return res.status(404).json({ error: "Migration not found" });
    }

    const migrationsDir = path.join(__dirname, "../migrations");

    // Remove the ZIP file
    const zipFilePath = path.join(
      migrationsDir,
      path.basename(migration.filePath)
    );
    if (fs.existsSync(zipFilePath)) {
      fs.unlinkSync(zipFilePath);
    }

    // Remove the folder
    const baseFolderPath = path.join(migrationsDir, migration.migrationName);
    if (fs.existsSync(baseFolderPath)) {
      fs.rmSync(baseFolderPath, { recursive: true, force: true });
    }

    await Migration.findByIdAndDelete(migrationId);
    res.status(200).json({ message: "Migration deleted successfully" });
  } catch (error) {
    console.error("Delete migration error:", error);
    res.status(400).json({ error: error.message });
  }
};
