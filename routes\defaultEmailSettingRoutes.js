const express = require("express");
const router = express.Router();
const defaultEmailSettingController = require("../controllers/defaultEmailSettingController");
const { checkPermissions } = require("../middleware/middleware");

router.post("/", checkPermissions("DefaultEmailSetting", ["create"]), defaultEmailSettingController.setDefaultEmailSetting);
router.get("/", checkPermissions("DefaultEmailSetting", ["read"]), defaultEmailSettingController.getDefaultEmailSetting);
router.delete("/", checkPermissions("DefaultEmailSetting", ["delete"]), defaultEmailSettingController.deleteDefaultEmailSetting);
router.patch("/:id/toggle-default", checkPermissions("DefaultEmailSetting", ["update"]), defaultEmailSettingController.toggleDefault);
router.put("/:id", checkPermissions("DefaultEmailSetting", ["update"]), defaultEmailSettingController.updateDefaultEmailSetting);


module.exports = router;
