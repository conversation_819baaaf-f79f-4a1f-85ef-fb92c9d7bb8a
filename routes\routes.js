//importing packages
const axios = require('axios');
const express = require('express');
const path = require('path');
const router = express.Router();
const flash = require('connect-flash');

//importing controller
const AuthController = require("../controllers/authController")

//importing routes
const apiKeyRoutes=require("../routes/apiKeyRoute.js")
const customFontRoutes = require('../routes/customFontRoutes.js');
const webhookRoute = require('../routes/customWebhookRoutes.js');
const domainRoute = require('../routes/cloudFareRoute.js');
const discountRoutes = require('../routes/discountRoutes.js');
const paymentRoutes = require('../routes/paymentRoutes.js');
const customVariableRoutes=require('../routes/customVariableRoutes.js')
const faqRoutes=require('../routes/faqRoute.js')
const publicMenuRoutes = require('../routes/publicMenuRoutes.js')
const migrationRoutes = require('../routes/migrationRoutes.js')
const onBoardingRoutes=require('../routes/onBoardingRoutes.js')
const contactRoutes = require('../routes/contactRoutes.js');
const userRoutes = require('./userRoutes');
const dealRoutes = require('./dealRoutes');
const ticketRoutes = require('./ticketRoutes');
const fileRoutes = require('./fileRoutes');
const companyRoutes = require('./companyRoutes');
const segmentRoutes = require('./segmentRoutes');
const calendarRoutes = require('./eventRoutes');
const formRoutes = require('./formRoutes');
const productRoutes = require('./productRoutes');
const quoteRoutes = require('./quoteRoutes');
const invoiceRoutes = require('./invoiceRoutes');
const taskRoutes = require('./taskRoutes');
const proposalRoutes = require('./proposalRoutes');
const agreementRoutes = require('./agreementRoutes');
const commentRoutes = require('./commentRoutes');
const backupRoutes = require('./backupRoutes.js');
const campaignRoutes = require('./campaignRoutes.js');
const contactListRoutes = require('./contactListRoutes.js');
const notificationRoutes = require('./notificationRoutes.js');
const logRoutes = require('./logRoutes.js');
const dealPipelineRoutes = require('./dealPipelineRoutes.js');
const smtpRoutes = require('./smtpRoutes');
const emailSequenceRoutes = require('./emailSequenceRoutes.js');
const certificateRoutes = require('./certificateRoutes');
const jobRoutes = require('./jobRoutes');


// Importing the routes
const activityRoutes = require('./activityRoutes');
const noteRoutes = require('./noteRoutes');
const emailRoutes = require('./emailRoutes');
const callRoutes = require('./callRoutes');
const meetingRoutes = require('./meetingRoutes');

const serviceRoutes = require('./serviceRoutes');
const businessRoutes = require('./businessRoutes.js');
const workflowRoutes = require('./workflowRoutes');
const badgeRoutes = require('./badgeRoutes');
const badgeTemplateRoutes = require('./badgeTemplateRoutes.js');

const elementRoutes = require('./elementRoutes');
const backgroundRoutes = require('./backgroundRoutes.js');
const certificateTemplatesRoutes = require('./certificateTemplatesRoutes.js');
const brandRoutes = require('./brandRoutes.js');
const dashboardRoutes = require('./dashboardRoutes.js');
const visitorRoutes = require('./visitorRoutes.js');
const planRoutes = require('./planRoutes');
const importHistoryRoutes = require('./importHistoryRoutes');
const qrRoutes = require('./qrRoutes.js');
const designRoutes = require('./designRoutes');

//postponed for later
//const templateRoutes = require('./TemplateRoutes.js');

//THIRD PARTY ROUTES
const pixabayRoutes = require('./pixabay');
const pexelsRoutes = require('./pexelsRoutes');
const svgapiRoutes = require('./svgapiRoutes.js');
const unsplashRoutes = require('./unsplashRoutes.js');
const iconfinderRoutes = require('./iconfinderRoutes');


//subscription routes
const subscriptionRoutes = require('./subscriptionRoutes.js');
const User = require('../models/User.js');





//const publicRoutes = require('./publicRoutes'); //added in app.js to avoid auth for contact and any public data


//publically accesible page without auth
module.exports = function (router) {

    router.use(async(req, res, next) => {
        const uemail = req.session.useremail;

        // Fetch user from DB only if logged in
        let user = null;
        if (uemail) {
            user = await User.findOne({ email: uemail });
        }
        const publicRoutes  = [
            "/login",
            "/auth-validate",
            "/api/auth-validate",
            "/register",
            "/signup",
            "/forgotpassword",
            "/sendforgotpasswordlink",
            "/resetpswdview/",
            "/resetpassword",
            "/error",
            "/changepassword",
            "/status",
            "/api/visitors/public/record",
            "/public/template/:id",
            "/public/templates/",
            "/public/templates-category/",
            "/auth-email-verification",
            "/verify-email",
            "/resend-email",
            


        ]

        const isPublicRoute = publicRoutes.some(url => req.path.startsWith(url));

        // Allow access to email verification routes even if the user is unverified
        if (!isPublicRoute && user && !user.isVerified) {
            return res.redirect(`/auth-email-verification?token=${user.verificationToken}`);
        }

        // If the route requires authentication and user is not logged in, redirect to login
        if (!isPublicRoute && !user) {
            
            return res.redirect(`/login?redirect_uri=${encodeURIComponent(req.originalUrl)}`);
        }

        console.log("User logged in, allowing access to the requested page.");
        next();
    });


// Routes
router.use("/api/keys", apiKeyRoutes);
router.use("/api/custom-fonts",customFontRoutes );
router.use("/api/customWebhooks",webhookRoute );
router.use("/api/domain", domainRoute);
router.use("/api/discounts", discountRoutes);
router.use('/api/payments', paymentRoutes);
router.use('/api/custom-variable', customVariableRoutes);
router.use('/api/faq', faqRoutes);
router.use('/api/menu', publicMenuRoutes);
router.use('/api/migration', migrationRoutes);
router.use('/api/onboarding',onBoardingRoutes)
router.use('/api/users', userRoutes);
// router.use('/api/companies', companyRoutes);
router.use('/api/contacts', contactRoutes);
// router.use('/api/deals', dealRoutes);
router.use('/api/tickets', ticketRoutes);
// router.use('/api/campaigns', campaignRoutes);
// router.use('/api/segments', segmentRoutes);
router.use('/api/events', calendarRoutes);
router.use('/api/forms', formRoutes);
// router.use('/api/products', productRoutes);
// router.use('/api/quotes', quoteRoutes);
router.use('/api/invoices', invoiceRoutes);
// router.use('/api/tasks', taskRoutes);
// router.use('/api/proposals', proposalRoutes);
// router.use('/api/agreements', agreementRoutes);
// router.use('/api/comments', commentRoutes);
router.use('/api/files', fileRoutes);


router.use('/api/backup', backupRoutes);
router.use('/api/contactlists', contactListRoutes);
router.use('/api/notifications', notificationRoutes);
router.use('/api/logs', logRoutes);
// router.use('/api/deal-pipelines', dealPipelineRoutes);
router.use('/api/smtp', smtpRoutes);
// router.use('/api/email-sequences', emailSequenceRoutes);

// Use the imported routes
router.use('/api/activities', activityRoutes);
// router.use('/api/notes', noteRoutes);
// router.use('/api/emails', emailRoutes);
// router.use('/api/calls', callRoutes);
// router.use('/api/meetings', meetingRoutes);
// router.use('/api/services', serviceRoutes);
router.use('/api/business', businessRoutes);
// router.use('/api/workflows', workflowRoutes);

router.use('/api/certificate-templates', certificateTemplatesRoutes);
router.use('/api/certificates', certificateRoutes);

router.use('/api/badge-templates', badgeTemplateRoutes);
router.use('/api/badges', badgeRoutes);
// router.use('/api/elements', elementRoutes);
// router.use('/api/backgrounds', backgroundRoutes);
router.use('/api/brands', brandRoutes);
router.use('/api/dashboard', dashboardRoutes);
router.use('/api/plans', planRoutes);
router.use('/api/visitors', visitorRoutes);
router.use('/api/import-history', importHistoryRoutes);
router.use('/api/qr', qrRoutes);
router.use('/api/design', designRoutes);

//templateRoutes posted for later to be implemented using mixcroservice due to CSM and CommonJS compatabiity issue
//router.use('/api/templates', templateRoutes);


//THIRD PARTY INTEGRATIIONS
router.use('/api/pixabay', pixabayRoutes);
router.use('/api/pexels', pexelsRoutes);
router.use('/api/svgapi', svgapiRoutes);
router.use('/api/unsplash', unsplashRoutes);
router.use('/api/iconfinder', iconfinderRoutes);

router.use('/api/subscribe', subscriptionRoutes);
// router.use('/api/job', jobRoutes);




//! FRONTEND ROUTES STARTS HERE
router.get('/settings-fonts/',(req,res,next)=>{
    res.render('settings-font',{title:'Font Settings - MixCertificate'})
})



router.get('/settings-form/',(req,res,next)=>{
    res.render('settings-form',{title:'Form Settings - MixCertificate'})
})



router.get('/settings-api/',(req,res,next)=>{
    res.render('settings-api',{title:'API Settings - MixCertificate'})
})


router.get('/settings-custom-domain/',(req,res,next)=>{
    res.render('settings-custom-domain',{title:'Custom Domain - MixCertificate'})
})

router.get('/notifications/', (req, res, next) => {
    res.render('notifications', { title: 'Notifications - MixCertificate' });
});

router.get('/settings-scripts/', (req, res, next) => {
    res.render('settings-scripts', { title: 'Settings - MixCertificate' });
})



router.get('/subscription/', (req, res, next) => {

    res.render('subscription', { title: 'Subscription - MixCertificate' });
})




router.get('/homepage/', (req, res, next) => {

    res.render('settings-homepage', { title: 'HomePage - MixCertificate' });
})


router.get('/branding/', (req, res, next) => {

    res.render('settings-branding', { title: 'Branding Settings - MixCertificate' });
})

router.get('/menu/', (req, res, next) => {

    res.render('public-menu', { title: 'Menu Settings - MixCertificate' });
})

router.get('/social/', (req, res, next) => {

    res.render('social.ejs', { title: 'Social Media Settings - MixCertificate' });
})

router.get('/settings-custom-variables/', (req, res, next) => {

    res.render('settings-custom-variables', { title: 'Custom Variables Settings - MixCertificate' });
})

router.get('/settings-discounts/', (req, res, next) => {
    res.render('settings-discounts', { title: 'Discounts - MixCertificate' });
});


// Existing route in app.js
router.get('/design/', (req, res, next) => {
    // Serve the certificate-public.html from the views directory
    res.sendFile(path.join(__dirname, '../views', 'editor.html'));
});

// Existing route in app.js
router.get('/design/:id', (req, res, next) => {
    // Serve the certificate-public.html from the views directory
    res.sendFile(path.join(__dirname, '../views', 'editor.html'));
});

router.get('/design-new/', (req, res, next) => {
    // Serve the certificate-public.html from the views directory
    res.sendFile(path.join(__dirname, '../views', 'editorCopyAfterToolBarCustomization.html'));
});

router.get('/editor-3/', (req, res, next) => {
    // Serve the certificate-public.html from the views directory
    res.sendFile(path.join(__dirname, '../views', 'editor3.html'));
});

router.get('/editor-4/', (req, res, next) => {
    // Serve the certificate-public.html from the views directory
    res.sendFile(path.join(__dirname, '../views', 'editor4.html'));
});

router.get('/design-deleted/', (req, res, next) => {
    res.render('designs-deleted', { title: 'Deleted Designs - MixCertificate' });
});

//Payment Success page
router.get('/payment-successful/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('payment-successful', { title: 'Payment Successful - MixCertificate' });
})

router.get('/payment-failure/', (req, res, next) => {
    // Display a flash message when rendering the page
    req.flash('error', 'Payment Failed. Please try again');

    res.render('payment-failure', {
        title: 'Payment Failure - MixCertificate',
        message: req.flash('error'),  // Use the error flash message
        error: req.flash('error')
    });
});


//Old certificate Editor
router.get('/certificate-new/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificate-editor', { title: 'Certificate Editor - MixCertificate' });
})

//all certificates
router.get('/bulk-design-generator/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('design-bulk-generator', { title: 'Bulk Design Generators - MixCertificate' });
})


//all certificates
router.get('/certificates/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificates', { title: 'Certificates - MixCertificate' });
})

//all certificates
router.get('/certificate-templates/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificate-templates', { title: 'Certificate Templates - MixCertificate' });
})

//all certificates
router.get('/certificate-templates-business/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificate-templates-business', { title: 'Business Certificate Template - MixCertificate' });
})

//certificate issued
router.get('/events/:id', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('event-certificate-single', { title: 'Certificate Event - MixCertificate' });
})

//certificate mail editor
router.get('/certificate-mail-editor/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificate-mail-editor', { title: 'Certificate Mail Editor - MixCertificate' });
})

//certificates public
router.get('/certificate-public/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('certificate-public', { title: 'Certificate Editor - MixCertificate' });
})



//certififcate-editor
router.get('/designs/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('designs', { title: 'All Designs - MixCertificate' });
})


//EVENTS

//Events CRUD
router.get('/events/', (req, res, next) => {
    res.render('events', { title: 'Events - MixCertificate' });
})

//Plans
router.get('/plans/', (req, res, next) => {
    res.render('plans', { title: 'Plans - MixCertificate' });
})

//BADGES

//Badges CRUD
router.get('/badges/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('badges', { title: 'Badges - MixCertificate' });
})

//Single Badge
router.get('/badges/:id', (req, res, next) => {

    res.render('badge-single', { title: 'Single Badge - MixCertificate' });
})

//Brand

//Brands CRUD
router.get('/brands/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('brands', { title: 'Brands - MixCertificate' });
})





//Plans and pricing
router.get('/pricing/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('pages-pricing', { title: 'MixCertificate - Pricing!' });
})

//root of data
router.get('/', (req, res, next) => {
    res.render('dashboard', { title: 'Dashboard - MixCertificate' });
    })


// App Status
router.get("/status", AuthController.appstatus);




/* AUTHENTICATION */

    //Register a User
    router.get('/register/', (req, res, next) => {
        res.render('auth/register', { title: 'Register', layout: 'layouts/layout-without-nav', message: req.flash('message'), error: req.flash('error') })
    })

    // validate register form
    router.post("/signup/", AuthController.signup)

    // New Account verify email
      router.post("/verify-email", AuthController.verifyEmail)
      router.post("/resend-email", AuthController.resendEmail)

    // New Account Verify Email page
    router.get('/auth-email-verification/', (req, res, next) => {
        const email = req.session.useremail || "your email";
        const token= req.query.token || ""
        res.render('auth-email-verification', { layout: 'layouts/layout-without-nav', email,token });
        })

    //Register a User
       router.get('/onboard/', (req, res, next) => {
        res.render('onboard', { title: 'Onboarding', layout: 'layouts/layout-without-nav', message: req.flash('message'), error: req.flash('error') })
    })

    //Register a User
       router.get('/onboard2/', (req, res, next) => {
        res.render('onboard2', { title: 'Onboarding', layout: 'layouts/layout-without-nav', message: req.flash('message'), error: req.flash('error') })
    })


    //Email OTP Two Step Verification
    router.get('/auth-two-step-verification/', (req, res, next) => {
        res.render('auth-two-step-verification', { layout: 'layouts/layout-without-nav' });
    })

    // Authentication Verification Confiration
    router.get('/auth-confirm-mail/', (req, res, next) => {
        res.render('auth-confirm-mail', { layout: 'layouts/layout-without-nav' });
    })

    // Locked Screen
    router.get('/auth-lock-screen/', (req, res, next) => {
        res.render('auth-lock-screen', { layout: 'layouts/layout-without-nav' });
    })

    // logout the user
    router.get("/logout", AuthController.logout);

    //show Logout screen after logged out
    router.get('/auth-logout/', (req, res, next) => {
        res.render('auth-logout', { layout: 'layouts/layout-without-nav' });
    })

    //Reset Password
    router.get('/auth-recoverpw/', (req, res, next) => {
        res.render('auth-recoverpw', { layout: 'layouts/layout-without-nav' });
    })

    //login
    router.get('/login/', (req, res, next) => {
        res.render('auth/login', { title: 'Login', layout: 'layouts/layout-without-nav', 'message': req.flash('message'), error: req.flash('error') })
    })

    // validate login form
    router.post("/auth-validate/", AuthController.validate)

    // validate login form with json response
    // router.post("/api/auth-validate/", AuthController.validateApi)

    router.get('/forgotpassword/', (req, res, next) => {
        res.render('auth/forgotpassword', { title: 'Forgot password', layout: 'layouts/layout-without-nav', message: req.flash('message'), error: req.flash('error') })
    })

    // send forgot password link on user email
    router.post("/sendforgotpasswordlink/", AuthController.forgotpassword)

    // reset password
    router.get("/resetpassword/", ()=>console.log("auth controller route reached"),AuthController.resetpswdview);


    // Change password
    router.post("/changepassword/", AuthController.changepassword);


    //Auth 404
    router.get('/error/', (req, res, next) => {
        console.log("error page rendered")
        res.render('auth/error', { title: '404 Error', layout: 'layouts/layout-without-nav' });
    })


    /* MAINTENANCE */

    //Maintennace pages
    router.get('/pages-maintenance/', (req, res, next) => {
        res.render('pages-maintenance', { layout: 'layouts/layout-without-nav' });
    })
    router.get('/pages-comingsoon/', (req, res, next) => {
        res.render('pages-comingsoon', { layout: 'layouts/layout-without-nav' });
    })
    router.get('/pages-500/', (req, res, next) => {
        res.render('pages-500', { layout: 'layouts/layout-without-nav' });
    })
    router.get('/pages-404/', (req, res, next) => {
        res.render('pages-404', { layout: 'layouts/layout-without-nav' });
    })




/* DASHBOARD */

//Dashboard
router.get('/dashboard/', (req, res, next) => {
    res.render('dashboard', { title: 'Dashboard - MixCertificate' });
    })



//products
router.get('/products/', (req, res, next) => {
    res.render('products-all.ejs', { title: 'Products - MixCertificate' });
    })

    //services
router.get('/services/', (req, res, next) => {
    res.render('services-all.ejs', { title: 'Services - MixCertificate' });
    })



//Dashboard
router.get('/products-new/', (req, res, next) => {
    res.render('products-new.ejs', { title: 'Add New - MixCertificate' });
    })



/* CONTACTS */

//Contacts > Add New
router.get('/contact-add/', (req, res, next) => {
    res.render('contact-add', { title: 'Add Contact' });
})

//Contacts > All Contacts
    router.get('/contacts/', (req, res, next) => {
        res.render('contacts-all', { title: 'All Contacts' });
    })

//Contacts > Single Profile
    router.get('/contact-single/', (req, res, next) => {
        res.render('contact-profile', { title: 'Single Contact' });
    })

//Contacts > lists
router.get('/contact-lists/', (req, res, next) => {
    res.render('contact-lists', { title: 'All Contact List' });
})

router.get('/contact-lists/:id', (req, res, next) => {
    res.render('contact-lists-single', { title: 'Single Contact List!' });
})


//Contacts > Segments
router.get('/segments-all/', (req, res, next) => {
    res.render('contact-segments-all', { title: 'Hi, Welcome Back!' });
})

//Contacts > Overview
    router.get('/contact-overview/', (req, res, next) => {
        res.render('contact-overview', { title: 'Contacts Overview' });
    })

//Contacts > Import
router.get('/contact-import/', (req, res, next) => {
    res.render('contact-import', { title: 'Contacts Import' });
})

//Contacts > Workflow
router.get('/contact-workflow/', (req, res, next) => {
    res.render('contact-workflow', { title: 'Contacts Workflow' });
})

//Contacts > Workflow
router.get('/contact-import-history/', (req, res, next) => {
    res.render('import-history.ejs', { title: 'Import History - Contacts' });
})

router.get('/contact-import-stages/', (req, res, next) => {
    res.render('import-contacts.ejs', { title: 'Import Contact - Contacts' });
})


//Contacts > Workflow
router.get('/import-history/', (req, res, next) => {
    res.render('import-history.ejs', { title: 'Import History - All' });
})





 /* CAMPAIGNS */

 //Contacts > Workflow
router.get('/campaigns/', (req, res, next) => {
    res.render('campaigns', { title: 'campaigns' });
})
 //Contacts > Workflow
router.get('/campaigns-single/', (req, res, next) => {
    res.render('campaigns-single', { title: 'Single Campaign' });
})





 /* DEAL */

//Deal > Add New
router.get('/deal-new/', (req, res, next) => {
    res.render('deal-new', { title: 'New Deal' });
})

//Deal > All Deal
    router.get('/deal-all/', (req, res, next) => {
        res.render('deal-all', { title: 'All Deals' });
    })

//Deal > All Deal List
router.get('/deal-all-list/', (req, res, next) => {
    res.render('deal-all-list', { title: 'All Deals List' });
})

//Deal > Single Deal
    router.get('/deal-single/', (req, res, next) => {
        res.render('deal-single', { title: 'Single Deal' });
    })

//Deal > Single Deal
    router.get('/deal-pipelines/', (req, res, next) => {
        res.render('deal-pipelines', { title: 'Single Deal' });
    })

//Deal > Import
router.get('/deal-import/', (req, res, next) => {
    res.render('deal-import', { title: 'Bulk Import' });
})



    /* PROPOSAL */

    //Proposal > New
    router.get('/proposal-new/', (req, res, next) => {
        res.render('proposal-new', { title: 'New proposal' });
    })


    //Proposal > view
    router.get('/proposal-view/', (req, res, next) => {
        res.render('proposal-view', { title: 'Proposal view' });
    })

    //Proposal > All
    router.get('/proposal-all/', (req, res, next) => {
        res.render('proposal-all', { title: 'Proposal All' });
    })

    //Proposal > Editor
    router.get('/proposal-editor/', (req, res, next) => {
        res.render('proposal-editor', { title: 'Proposal Editor' });
    })

    //Proposal > Sign proposal
    router.get('/proposal-sign/', (req, res, next) => {
        res.render('proposal-sign', { title: 'Proposal Editor' });
    })

    //Proposal > Sign proposal
    router.get('/proposal-templates/', (req, res, next) => {
        res.render('proposal-templates', { title: 'Proposal Editor' });
    })

    //Proposal > Single proposal template
    router.get('/proposal-template-single/', (req, res, next) => {
        res.render('proposal-template-single', { title: 'Single Proposal Template' });
    })


/* AGREEMENT */

    //Agreement > New
    router.get('/agreement-new/', (req, res, next) => {
        res.render('agreement-new', { title: 'New Agreement!' });
    })

    //Agreement > All
    router.get('/agreement-all/', (req, res, next) => {
        res.render('agreement-all', { title: 'Agreement All' });
    })

    //Agreement > Editor
    router.get('/agreement-editor/', (req, res, next) => {
        res.render('agreement-editor', { title: 'Agreement Editor' });
    })

    //Agreement > Sign Agreement
    router.get('/agreement-sign/', (req, res, next) => {
        res.render('agreement-sign', { title: 'Agreement Editor' });
    })

    //Agreement > Sign Agreement
    router.get('/agreement-templates/', (req, res, next) => {
        res.render('agreement-templates', { title: 'Agreement Editor' });
    })



/* CAMPAIGN */



    //Campaign > Sequence
    router.get('/sequence-all/', (req, res, next) => {
        res.render('sequence-all', { title: 'Sequence All' });
    })



    /* INVOICE */

    //pricing and business
    router.get('/invoice-new/', (req, res, next) => {
        res.render('invoice-new', { title: 'Create - Invoice' });
    })

    router.get('/invoice-list/', (req, res, next) => {
        res.render('invoice-list', { title: 'Hi, Welcome Back!' });
    })

    router.get('/invoice-single/', (req, res, next) => {
        res.render('invoice-single', { title: 'Hi, Welcome Back!' });
    })



    /* CLIENTS */

    router.get('/client-new/', (req, res, next) => {
        res.render('client-new', { title: 'New Client' });
    })

    router.get('/client-edit/', (req, res, next) => {
        res.render('client-edit', { title: 'Edit Client' });
    })

    router.get('/client-all/', (req, res, next) => {
        res.render('client-all', { title: 'All Clients' });
    })

    router.get('/client-single/', (req, res, next) => {
        res.render('client-single', { title: 'Single Client' });
    })

    router.get('/client-import/', (req, res, next) => {
        res.render('client-import', { title: 'Single Client' });
    })

    router.get('/client-delete/', (req, res, next) => {
        res.render('client-delete', { title: 'Client Delete' });
    })

/* Workflow */

//Workflow > Add New
router.get('/workflows-all/', (req, res, next) => {
    res.render('workflows-all', { title: 'Add Contact' });
})

 /* SUPPORT */


    router.get('/ticket-new/', (req, res, next) => {
        res.render('ticket-new', { title: 'New ticket' });
    })

    router.get('/ticket-edit/', (req, res, next) => {
        res.render('ticket-edit', { title: 'Edit ticket' });
    })

    router.get('/ticket-all/', (req, res, next) => {
        res.render('ticket-all', { title: 'All tickets' });
    })

    router.get('/ticket-single/', (req, res, next) => {
        res.render('ticket-single', { title: 'Single ticket' });
    })

    router.get('/ticket-import/', (req, res, next) => {
        res.render('ticket-import', { title: 'Single ticket' });
    })

    router.get('/ticket-delete/', (req, res, next) => {
        res.render('ticket-delete', { title: 'ticket Delete' });
    })


/* REPORT */

    //report-dashboard-marketing
    router.get('/dashboard-marketing/', (req, res, next) => {
        res.render('dashboard-marketing', { title: 'Dashboard marketing' });
    })

    //report-dashboard-sales
    router.get('/dashboard-sales/', (req, res, next) => {
        res.render('dashboard-sales', { title: 'dashboard-sales' });
    })

    //report-dashboard-admin
    router.get('/dashboard-admin/', (req, res, next) => {
        res.render('dashboard-admin', { title: 'dashboard-admin' });
    })

    //report-dashboard-support
    router.get('/dashboard-support/', (req, res, next) => {
        res.render('dashboard-support', { title: 'dashboard-support' });
    })

    //report-dashboard-business
    router.get('/dashboard-business/', (req, res, next) => {
        res.render('dashboard-business', { title: 'dashboard-business' });
    })

    //report-dashboard-business
    router.get('/dashboard-business/', (req, res, next) => {
        res.render('dashboard-business', { title: 'dashboard-business' });
    })

    //report-dashboard-manager
    router.get('/dashboard-manager/', (req, res, next) => {
        res.render('dashboard-manager', { title: 'dashboard-manager' });
    })



/* USERS */



//users
    router.get('/users/', (req, res, next) => {
    res.render('users', { title: 'All Users' });
})

//Import users
router.get('/user-import/', (req, res, next) => {
    res.render('user-import', { title: 'Import Users' });
})


/* TASKS */

//tasks kanban
router.get('/task/', (req, res, next) => {
    res.render('task-kanban', { title: 'Task' });
})


//task list
router.get('/task-list/', (req, res, next) => {
    res.render('task-list', { title: 'Task List' });
})

//task single
router.get('/task-single/', (req, res, next) => {
    res.render('task-single', { title: 'Task Single Page' });
})

//task import
router.get('/task-import/', (req, res, next) => {
    res.render('task-import', { title: 'Tasks import' });
})



/* FILES */

//Files
router.get('/files/', (req, res, next) => {
    res.render('files', { title: 'Files' });
})


/* Calendar */

//calendar
router.get('/calendar/', (req, res, next) => {
    res.render('calendar', { title: 'Calendar!' });
})


/* SETTING */
router.get('/settings/', (req, res, next) => {
    res.render('settings', { title: 'Settings - MixCertificate' });
})

    //settings - profile settings
    router.get('/settings-profile/', (req, res, next) => {
        res.render('settings-profile', { title: 'Profile Settings - MixCertificate' });
    })


    //Settings > Contact
    router.get('/settings-contact/', (req, res, next) => {
        res.render('settings-contact', { title: 'Contacts Settings' });
    })

    //Settings > Contacts
    router.get('/settings-deal/', (req, res, next) => {
        res.render('settings-deal', { title: 'Deal Settings' });
    })



    //settings - notification settings
    router.get('/settings-notification/', (req, res, next) => {
        res.render('settings-notification', { title: 'Notification Settings - MixCertificate' });
    })

    //settings - business settings
    router.get('/settings-business/', (req, res, next) => {

        res.render('settings-business', { title: 'Business Settings - MixCertificate'});
    })

     //settings - Email settings
     router.get('/settings-email/', (req, res, next) => {
        res.render('settings-email', { title: 'Email Settings - MixCertificate' });
    })

    //settings - payment settings
    router.get('/plans/', (req, res, next) => {
        res.render('settings-payment', { title: 'Payment Settings - MixCertificate' });
    })

    //settings - profile settings
    router.get('/settings-crm/', (req, res, next) => {
        res.render('settings-crm', { title: 'Profile Settings - MixCertificate' });
    })

    //settings - integration settings
    router.get('/settings-integration/', (req, res, next) => {
        res.render('settings-integration', { title: 'Integration Settings - MixCertificate' });
    })

    //settings - customization settings
    router.get('/settings-customization/', (req, res, next) => {
        res.render('settings-customization', { title: 'Customization Settings - MixCertificate' });
    })


    //settings - backup settings
    router.get('/settings-backup/', (req, res, next) => {
        res.render('settings-backup', { title: 'Data Settings - MixCertificate' });
    })

    router.get('/settings-migration/', (req, res, next) => {
        res.render('settings-migration', { title: 'Migration Settings - MixCertificate' });
    })


    //settings - Delete Account settings
    router.get('/settings-account-delete/', (req, res, next) => {
        res.render('settings-account-delete', { title: 'Delete Account - MixCertificate' });
    })

    //settings - Log Settings settings
    router.get('/settings-logs/', (req, res, next) => {
        res.render('settings-logs', { title: 'Log Settings - MixCertificate' });
    })


    //settings - Security settings
    router.get('/settings-security/', (req, res, next) => {
        res.render('settings-security', { title: 'Security Settings - MixCertificate' });
    })

    //Plans and pricing
    router.get('/settings-smtp/', (req, res, next) => {
    //the pricing page currently indie dashboard ui, a new page should be made and move to be publically accesible
    res.render('settings-smtp', { title: 'Settings SMTP!' });
    })


    //Apps


    // blog grid
    router.get('/apps-blog-grid/', (req, res, next) => {
        res.render('apps-blog-grid', { title: 'Hi, Welcome Back!' });
    })

    // blog grid
    router.get('/apps-blog-detail/', (req, res, next) => {
    res.render('apps-blog-detail', { title: 'Hi, Welcome Back!' });
    })


    router.get('/apps-contacts-list/', (req, res, next) => {
        res.render('apps-contacts-list', { title: 'Hi, Welcome Back!' });
    })
    router.get('/apps-contacts-profile/', (req, res, next) => {
        res.render('apps-contacts-profile', { title: 'Hi, Welcome Back!' });
    })

    router.get('/apps-contacts-grid/', (req, res, next) => {
        res.render('apps-contacts-grid', { title: 'Hi, Welcome Back!' });
    })



    router.get('/apps-email-inbox/', (req, res, next) => {
        res.render('apps-email-inbox', { title: 'Hi, Welcome Back!' });
    })
    router.get('/apps-email-read/', (req, res, next) => {
        res.render('apps-email-read', { title: 'Hi, Welcome Back!' });
    })









}