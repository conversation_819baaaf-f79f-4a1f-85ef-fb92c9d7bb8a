const path = require("path");
const Business = require("../models/Business");
const User = require("../models/User");
const PublicMenu = require("../models/PublicMenuModel");
const File = require("../models/File");
const fs = require("fs");
const { createLog } = require("./logController");
exports.createOnBoardingOnUserSkip = async (req, res, next) => {
  try {
    const existingBusiness = await Business.findOne({
      createdBy: req.user._id,
    });

    if (existingBusiness) {
      return res.status(400).json({
        success: false,
        message: "OnBoarding Data already exists.",
      });
    }

    const nameSpaceCode = Math.floor(10000 + Math.random() * 90000);
    const business = new Business({
      ...req.body,
      nameSpaceCode: nameSpaceCode.toString(),
      createdBy: req.user._id,
      createdIP: req.ip,
      createdEmail: req.user.email,
      createdPhone: req.user.phone,
    });
    await User.findByIdAndUpdate(
      req.user._id,
      { businessId: business._id },
      { new: true }
    );

    await business.save();
    await createLog(
      {
        eventType: "Authentication",
        action: "Login",
        target: business._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: business._id,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );
    res
      .status(201)
      .json({ success: true, message: "OnBoarding Data saved successfully!" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

exports.createOnBoarding = async (req, res) => {
  try {
    const existingBusiness = await Business.findOne({
      createdBy: req.user._id,
    });
    let businessLogo = existingBusiness?.businessLogo || "";

    if (req.files?.businessLogo) {
      const uploadedFile = await saveFile(req.files.businessLogo, req.user._id);
      if (uploadedFile) businessLogo = uploadedFile.url;
    }

    if (existingBusiness) {
      const updatedBusiness = await updateExistingBusiness(
        existingBusiness,
        req.body,
        businessLogo
      );
      return res.status(200).json({
        success: true,
        message: "OnBoarding Data updated successfully!",
        business: updatedBusiness,
      });
    }
    const nameSpaceCode = Math.floor(10000 + Math.random() * 90000);
    const newBusiness = await createNewBusiness(
      req.body,
      req.user,
      businessLogo,
      req.ip,
      nameSpaceCode
    );
    await User.findByIdAndUpdate(
      req.user._id,
      { businessId: newBusiness._id },
      { new: true }
    );
    await PublicMenu.create({
      businessId: newBusiness._id,
      favIcon: req.body.favIcon,
      logoUrl: req.body.logoUrl,
      fontColor: req.body.fontColor,
      backgroundColor: req.body.backgroundColor,
      items: JSON.parse(req.body.menuItems),
      font: {
        fontFamily: req.body.fontFamily,
        fontWeight: req.body.fontWeight,
        fontSize: req.body.fontSize,
      },
    });
    await createLog(
      {
        eventType: "Authentication",
        action: "Login",
        target: newBusiness._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: newBusiness._id,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"] || "Unknown",
      },
      res
    );

    res.status(201).json({
      success: true,
      message: "OnBoarding Data saved successfully!",
      business: newBusiness,
    });
  } catch (error) {
    console.error("Error in createOnBoarding:", error);
    res.status(400).json({ error: error.message });
  }
};

async function saveFile(file, userId) {
  try {
    const uploadDir = path.join(__dirname, "../public/uploads");
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });

    const fileName = `${Date.now()}-${file.name}`;
    const filePath = path.join(uploadDir, fileName);

    await file.mv(filePath);

    return await File.create({
      fileName,
      fileType: file.mimetype,
      fileSize: file.size,
      url: `/uploads/${fileName}`,
      uploadedBy: userId,
    });
  } catch (error) {
    console.error("File upload error:", error);
    return null;
  }
}

async function updateExistingBusiness(existingBusiness, body, businessLogo) {
  const updatedFields = {
    businessLogo,
    businessName: body.businessName || existingBusiness.businessName,
    businessEmail: body.businessEmail || existingBusiness.businessEmail,
    businessStructure:
      body.businessStructure || existingBusiness.businessStructure,
    industry: body.industry || existingBusiness.industry,
    businessDescription:
      body.businessDescription || existingBusiness.businessDescription,
    businessAddress1:
      body.businessAddress1 || existingBusiness.businessAddress1,
    businessAddress2:
      body.businessAddress2 || existingBusiness.businessAddress2,
    city: body.city || existingBusiness.city,
    state: body.state || existingBusiness.state,
    postalCode: body.postalCode || existingBusiness.postalCode,
    country: body.country || existingBusiness.country,
  };
  return await Business.findByIdAndUpdate(
    existingBusiness._id,
    { $set: updatedFields },
    { new: true }
  );
}

async function createNewBusiness(body, user, businessLogo, ip, nameSpaceCode) {
  console.log("body", nameSpaceCode);
  return await Business.create({
    ...body,
    createdBy: user._id,
    createdIP: ip,
    nameSpaceCode: nameSpaceCode.toString(),
    createdEmail: user.email,
    createdPhone: user.phone,
    businessLogo,
  });
}
