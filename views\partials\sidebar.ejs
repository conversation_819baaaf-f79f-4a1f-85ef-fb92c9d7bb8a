<!-- ========== Left Sidebar Start ========== -->
<div class="vertical-menu">
  <div data-simplebar class="h-100">
    <!--- Sidemenu -->
    <div id="sidebar-menu">
      <!-- Left Menu Start -->
      <ul class="metismenu list-unstyled" id="side-menu">
        <li class="menu-title" data-key="t-menu"><%=translation.t_menu %></li>

        <!-- Dashboard -->

        <li>
          <a href="/dashboard">
            <i data-feather="home"></i>
            <span data-key="t-dashboard"> Dashboard </span>
          </a>
        </li>

        <!-- Contacts -->
        <li>
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="phone"></i>
            <span data-key="t-pages">Contacts</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li class="">
              <a href="/contact-add/" data-key="t-starter-page">Add New</a>
            </li>
            <li>
              <a href="/contacts/" data-key="t-maintenance">All Contacts</a>
            </li>
            <li class="d-none">
              <a href="/contact-single/" data-key="t-timeline"
                >Single Contact</a
              >
            </li>
            <li class="">
              <a href="/contact-lists/" data-key="t-maintenance">Lists</a>
            </li>

            <li class="d-none">
              <a href="/segments-all/" data-key="t-maintenance">Segments</a>
            </li>
            <li>
              <a href="/contact-import/" data-key="t-maintenance"
                >Bulk Import</a
              >
            </li>
            <li>
              <a href="/contact-import-stages/" data-key="t-maintenance"
                >Import Stages</a
              >
            </li>
            <li>
              <a href="/contact-import-history/" data-key="t-maintenance"
                >Import History</a
              >
            </li>

            <li class="d-none">
              <a href="/contact-overview/" data-key="t-timeline">Overview </a>
            </li>
          </ul>
        </li>

        <!-- Events -->
        <li class="">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="calendar"></i>
            <span> Events </span>
          </a>
          <ul class="sub-menu" aria-expanded="true">
            <!-- first level menu -->

            <li>
              <a href="/bulk-design-generator/?v=1" data-key="t-level-2-2"
                >New Event</a
              >
            </li>
            <li><a href="/events/" data-key="t-level-2-2">Events</a></li>
            <li>
              <a href="/events/67206f346a277f67ded68efd" data-key="t-agreement"
                >Event Overview</a
              >
            </li>
            <li class="d-none">
              <a href="/certificate-mail-editor/" data-key="t-agreement"
                >Mail Setup</a
              >
            </li>
          </ul>
        </li>

        <li>
          <a href="/designs/">
            <i data-feather="pen-tool"></i>
            <span>Designs</span></a
          >
        </li>
        <li>
          <a href="/design-new/">
            <i data-feather="pen-tool"></i>
            <span>New Editor</span></a
          >
        </li>
           <li>
          <a href="/editor-3/">
            <i data-feather="pen-tool"></i>
            <span>Editor 3</span></a
          >
        </li>
           <li>
          <a href="/editor-4/">
            <i data-feather="pen-tool"></i>
           <span>Editor 4</span></a
          >
        </li>

        <!-- Badges -->
        <li class="">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="award"></i>
            <span> Badges </span>
          </a>
          <ul class="sub-menu" aria-expanded="true">
            <!-- first level menu -->
            <li><a href="/badges/" data-key="t-level-2-1">All Badges</a></li>
            <li>
              <a
                id="publicBadgeLink"
                href="/badge/66c75a1870895e1d745cd5db"
                data-key="t-level-2-1"
                >Public Badge</a
              >
            </li>
            <li>
              <a
                id="publicBadgeSearchLink"
                href="/p/home"
                data-key="t-agreement"
                >Search</a
              >
            </li>
            <li class="d-none">
              <a href="/badges-category/" data-key="t-level-2-2">Category</a>
            </li>
          </ul>
        </li>

        <!-- Certificate -->
        <li>
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="file-minus"></i>
            <span data-key="t-pages">Certificate</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li class="d-none">
              <a href="/design/" data-key="t-agreement">New Design</a>
            </li>
            <li>
              <a href="/bulk-design-generator/" data-key="t-starter-page"
                >Bulk Generate</a
              >
            </li>
            <li>
              <a href="/designs/?type=certificate" data-key="t-agreement"
                >Add New</a
              >
            </li>
            <li>
              <a href="/certificates/" data-key="t-agreement"
                >Issued Certificates</a
              >
            </li>

            <li>
              <a id="publicSearchLink" href="/p/home" data-key="t-agreement"
                >Search</a
              >
            </li>
            <li>
              <a
                id="publicCertificateLink"
                href="/certificate/66c50226fa4ba885b46f124f"
                data-key="t-agreement"
                >Public Certificate</a
              >
            </li>
            <li>
              <a href="/proposal-sign/" data-key="t-agreement"
                >Sign Certificate</a
              >
            </li>
          </ul>
        </li>

        <!-- Users -->

        <li>
          <a href="/users/">
            <i data-feather="users"></i>
            <span data-key="t-dashboard"> Users </span>
          </a>
        </li>

        <!-- Campaigns starts - multi level menu  -->

        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="send"></i>
            <span data-key="t-multi-level"> Campaigns </span>
          </a>
          <ul class="sub-menu" aria-expanded="true">
            <!-- first level menu -->
            <li><a href="/campaigns/" data-key="t-level-2-2">Campaigns</a></li>
            <li>
              <a href="/sequence-all/" data-key="t-level-2-2">Sequences</a>
            </li>
            <li>
              <a href="/campaigns/reports/" data-key="t-level-2-2">Reports</a>
            </li>
            <li>
              <a href="/campaigns/overview/" data-key="t-level-2-2">Overview</a>
            </li>
            <li>
              <a href="/campaigns-settings/" data-key="t-level-2-2">Settings</a>
            </li>

            <!-- Email Marketing -->
            <li class="d-none">
              <a
                href="javascript: void(0);"
                class="has-arrow"
                data-key="t-level-1-2"
                >Email Marketing</a
              >
              <ul class="sub-menu" aria-expanded="true">
                <li>
                  <a href="/campaigns/email/new/" data-key="t-level-2-2"
                    >New Campaign</a
                  >
                </li>
                <li>
                  <a href="/campaigns/email/all/" data-key="t-level-2-1"
                    >Campaigns</a
                  >
                </li>
                <li>
                  <a href="/campaigns/email/templates/" data-key="t-level-2-2"
                    >Templates</a
                  >
                </li>
                <li>
                  <a href="/campaigns/email/reports/" data-key="t-level-2-2"
                    >Reports</a
                  >
                </li>
                <li>
                  <a href="/campaigns/email/settings/" data-key="t-level-2-2"
                    >Settings</a
                  >
                </li>
              </ul>
            </li>
          </ul>
        </li>

        <!-- Services -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="box"></i>
            <span> Services </span>
          </a>
          <ul class="sub-menu" aria-expanded="true">
            <!-- first level menu -->
            <li><a href="/services/" data-key="t-level-2-2">Services</a></li>
            <li><a href="/services/" data-key="t-level-2-1">New Service</a></li>
            <li><a href="/services/" data-key="t-level-2-1">Tags</a></li>
            <li><a href="/services/" data-key="t-level-2-1">Category</a></li>
          </ul>
        </li>

        <!-- Deals -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="dollar-sign"></i>
            <span data-key="t-pages">Deals</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li><a href="/deal-new/" data-key="t-starter-page">New Deal</a></li>
            <li><a href="/deal-all/" data-key="t-maintenance">All Deals</a></li>
            <li>
              <a href="/deal-pipelines/" data-key="t-maintenance"
                >All Pipeline</a
              >
            </li>
            <li>
              <a href="/deal-single/" data-key="t-maintenance">Single Deal</a>
            </li>
            <li>
              <a href="/deal-import/" data-key="t-maintenance">Import Deals</a>
            </li>
          </ul>
        </li>

        <!-- Proposal -->

        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="file-minus"></i>
            <span data-key="t-pages">Proposal</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li>
              <a href="/proposal-new/" data-key="t-agreement">New Proposal</a>
            </li>
            <li>
              <a href="/proposal-editor/" data-key="t-agreement"
                >Proposal Editor</a
              >
            </li>
            <li>
              <a href="/proposal-view/" data-key="t-agreement">View Proposal</a>
            </li>
            <li>
              <a href="/proposal-all/" data-key="t-agreement">All Proposal</a>
            </li>
            <li>
              <a href="/proposal-sign/" data-key="t-agreement">Sign Proposal</a>
            </li>
            <li>
              <a href="/proposal-templates/" data-key="t-agreement"
                >Templates</a
              >
            </li>
            <li>
              <a href="/proposal-template-single/" data-key="t-agreement"
                >Single Template</a
              >
            </li>
          </ul>
        </li>

        <!-- Agreement -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="file-minus"></i>
            <span data-key="t-pages">Agreement</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li>
              <a href="/agreement-new/" data-key="t-agreement">New Agreement</a>
            </li>
            <li>
              <a href="/agreement-editor/" data-key="t-agreement"
                >Agreement Editor</a
              >
            </li>
            <li>
              <a href="/agreement-all/" data-key="t-agreement"
                >All Agreements</a
              >
            </li>
            <li>
              <a href="/agreement-sign/" data-key="t-agreement"
                >Sign Agreement</a
              >
            </li>
            <li>
              <a href="/agreement-templates/" data-key="t-agreement"
                >Templates</a
              >
            </li>
          </ul>
        </li>

        <!-- Invoice -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="file-minus"></i>
            <span data-key="t-invoice">Invoice</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li>
              <a href="/invoice-new/" data-key="t-invoice">New Invoice</a>
            </li>
            <li>
              <a href="/invoice-list/" data-key="t-invoice">All Invoices</a>
            </li>
            <li>
              <a href="/invoice-single/" data-key="t-invoice">Single Invoice</a>
            </li>
          </ul>
        </li>

        <!-- Clients -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="circle"></i>
            <span data-key="t-pages">Clients</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li><a href="/client-new/" data-key="t-deal">New Client</a></li>
            <li><a href="/client-all/" data-key="t-deal">All Clients</a></li>
            <li>
              <a href="/client-single/" data-key="t-deal">Client Single</a>
            </li>
            <li><a href="/client-edit/" data-key="t-deal">Client Update</a></li>
            <li>
              <a href="/client-delete/" data-key="t-deal">Client Delete</a>
            </li>
            <li><a href="/client-import/" data-key="t-deal">Bulk Import</a></li>
          </ul>
        </li>

        <!-- Support -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i class="bx bx-support"></i>
            <span data-key="t-pages">Support</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li><a href="/ticket-new/" data-key="t-support">New Ticket</a></li>
            <li>
              <a href="/ticket-single/" data-key="t-support">Single Ticket</a>
            </li>
            <li>
              <a href="/ticket-edit/" data-key="t-support">Edit Tickets</a>
            </li>
            <li><a href="/ticket-all/" data-key="t-support">All Tickets</a></li>
            <li>
              <a href="/ticket-import/" data-key="t-support">Delete Ticket</a>
            </li>
          </ul>
        </li>

        <!-- Report -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="pie-chart"></i>
            <span data-key="t-pages">Report</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li>
              <a href="/dashboard-marketing/" data-key="t-report"
                >Marketing Dashboard</a
              >
            </li>
            <li>
              <a href="/dashboard-sales/" data-key="t-report"
                >Sales Dashboard</a
              >
            </li>
            <li>
              <a href="/dashboard-support/" data-key="t-report"
                >Support Dashboard</a
              >
            </li>
            <li>
              <a href="/dashboard-business/" data-key="t-report"
                >Business Dashboard</a
              >
            </li>
            <li>
              <a href="/dashboard-manager/" data-key="t-report"
                >Manager Dashboard</a
              >
            </li>
            <li>
              <a href="/dashboard-admin/" data-key="t-report"
                >Admin Dashboard</a
              >
            </li>
          </ul>
        </li>

        <!-- Tasks -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="check-square"></i>
            <span data-key="t-pages">Tasks</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li><a href="/task/" data-key="t-user">All</a></li>
            <li><a href="/task-list/" data-key="t-user">Task Kanban</a></li>
            <li><a href="/task-single/" data-key="t-user">Task Single</a></li>
            <li><a href="/task-single/" data-key="t-user">Task Single</a></li>
            <li><a href="/task-import/" data-key="t-user">Tasks Import</a></li>
          </ul>
        </li>

        <!-- Files -->
        <li class="d-none">
          <a href="/files/">
            <i data-feather="image"></i>
            <span data-key="t-file">Files</span>
          </a>
        </li>

        <!-- Calendar -->
        <li class="d-none">
          <a href="/calendar/">
            <i data-feather="calendar"></i>
            <span data-key="t-calendar">Calendar</span>
          </a>
        </li>

        <!-- Tasks -->
        <li class="">
          <a href="/files/">
            <i data-feather="file"></i>
            <span data-key="t-calendar">Files</span>
          </a>
        </li>

        <!-- Settings -->
        <li class="">
          <a href="/settings/">
            <i data-feather="settings"></i>
            <span data-key="t-pages">Settings</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <!-- <li><a href="/settings-business/" data-key="t-timeline">Business Settings</a></li> -->
            <!-- <li><a href="/settings-payment/" data-key="t-timeline">Payment Settings</a></li> -->
            <!-- <li><a href="/settings-profile/" data-key="t-starter-page">Profile Settings</a></li> -->
            <!-- <li><a href="/settings-crm/" data-key="t-timeline">CRM Settings</a></li> -->
            <!-- <li class="d-none"><a href="/settings-deal/" data-key="t-timeline">Deal Settings</a></li> -->

            <!-- <li><a href="/settings-contact/" data-key="t-starter-page">Contact Settings</a></li> -->

            <!-- <li><a href="/settings-notification/" data-key="t-settings">Notification Settings</a></li> -->

            <!-- <li><a href="/settings-email/" data-key="t-timeline">Email Settings</a></li> -->
            <!-- <li><a href="/settings-integration/" data-key="t-timeline">Integration Settings</a></li> -->
            <!-- <li><a href="/settings-customization/" data-key="t-timeline">Customization Settings</a></li> -->
            <!-- <li><a href="/settings-backup/" data-key="t-timeline">Data & Backup</a></li> -->
            <!-- <li><a href="/logs/" data-key="t-timeline">Logs Settings</a></li> -->
            <!-- <li><a href="/settings-security/" data-key="t-timeline">Security Settings</a></li> -->
            <!-- <li><a href="/import-history/" data-key="t-timeline">Import History</a></li> -->
            <!-- <li class="d-none"><a href="/settings-smtp/" data-key="">SMTP</a></li> -->
            <!-- <li><a href="/settings-account-delete/" data-key="t-timeline">Account Deletion</a></li> -->

            <!-- Email Marketing -->
            <li class="d-none">
              <a href="javascript: void(0);" class="has-arrow" data-key=""
                >Email Settings</a
              >
              <ul class="sub-menu" aria-expanded="true">
                <li><a href="/settings/smtp/" data-key="">SMTP</a></li>
                <li>
                  <a href="/campaigns/email/templates/" data-key=""
                    >Templates</a
                  >
                </li>
                <li>
                  <a href="/campaigns/email/reports/" data-key="">Reports</a>
                </li>
                <li>
                  <a href="/campaigns/email/overview/" data-key="">Overview</a>
                </li>
                <li>
                  <a href="/campaigns/email/settings/" data-key="">Settings</a>
                </li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="d-none">
          <a href="/subscription/">
            <i data-feather="credit-card"></i>
            <span data-key="t-menu">Manage Subscription</span>
          </a>
        </li>

        <li class="d-none">
          <a href="/menu/">
            <i data-feather="menu"></i>
            <span data-key="t-menu">Public Menu</span>
          </a>
        </li>

        <!-- Help & Support -->
        <li class="d-none">
          <a href="javascript: void(0);" class="has-arrow">
            <i data-feather="file-text"></i>
            <span data-key="t-pages">Help & Support</span>
          </a>
          <ul class="sub-menu" aria-expanded="false">
            <li>
              <a href="/pages-starter" data-key="t-starter-page">User Guide</a>
            </li>
            <li>
              <a href="/pages-maintenance" data-key="t-maintenance">FAQs</a>
            </li>
            <li>
              <a href="/pages-timeline" data-key="t-timeline"
                >Support Tickets</a
              >
            </li>
            <li>
              <a href="/pages-timeline" data-key="t-timeline"
                >Contact Support</a
              >
            </li>
            <li>
              <a href="/pages-timeline" data-key="t-timeline">Community</a>
            </li>
            <li><a href="/pages-timeline" data-key="t-timeline">Forum</a></li>
          </ul>
        </li>
        <!-- MixCertificate menu Ends Here -->
      </ul>

      <div class="card sidebar-alert border-0 text-center mx-4 mb-0 mt-5">
        <div class="card-body">
          <img src="/assets/images/giftbox.png" alt="" />
          <div class="mt-4">
            <h5 class="alertcard-title font-size-16">Unlimited Support</h5>
            <p class="font-size-13">Send an <NAME_EMAIL></p>
            <a
              href="mailto:<EMAIL>?subject%20mixcertificate%20support"
              class="btn btn-primary mt-2 text-white"
              >Mail Now</a
            >
          </div>
        </div>
      </div>
    </div>
    <!-- Sidebar -->
  </div>
</div>
<!-- Left Sidebar End -->

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script>
  $(document).ready(function () {
    $.ajax({
      url: "/api/users/profile",
      method: "GET",
      success: function (response) {
        if (response) {
          fetchBusinessData(response.businessId);
        }
      },
      error: function (xhr, status, error) {
        console.error("Error fetching businessId:", error);
      },
    });

    function fetchBusinessData(businessId) {
      $.ajax({
        method: "GET",
        url: `/api/business/${businessId}`,
        success: function (result) {
          console.log(result, "result");
          if (result) {
            $("#publicBadgeLink").attr(
              "href",
              `/${result.nameSpaceCode}/badge/66c75a1870895e1d745cd5db`
            );
            $("#publicCertificateLink").attr(
              "href",
              `/${result.nameSpaceCode}/certificate/66c50226fa4ba885b46f124f`
            );
            $("#publicSearchLink").attr(
              "href",
              `/${result.nameSpaceCode}/p/home`
            );
            $("#publicBadgeSearchLink").attr(
              "href",
              `/${result.nameSpaceCode}/p/home`
            );
          }
        },
        error: function (error) {
          console.error(error);
        },
      });
    }
  });
</script>
