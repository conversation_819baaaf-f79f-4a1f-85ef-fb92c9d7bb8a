<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta",{"title":"Application Logs" }) %>
<link
  href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css"
  rel="stylesheet"
/>
<link href="/assets/libs/flatpickr/flatpickr.min.css" rel="stylesheet" />
<link
  href="/assets/libs/choices.js/public/assets/styles/choices.min.css"
  rel="stylesheet"
/>

<style>
  .filter-row .choices__inner {
    min-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .filter-row .form-control,
  .filter-row .form-select {
    min-width: 100%;
  }

  @media (max-width: 576px) {
    #logs-filter-form .btn {
      font-size: 0.9rem;
      padding: 0.5rem 0.75rem;
    }
  }

  .table-responsive {
    overflow-x: auto;
  }

  #logs-table th,
  #logs-table td {
    white-space: nowrap;
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title",{"title":"Application Logs", "pagetitle": "Settings" }) %>
<div class="container-fluid">
  <div class="card">
    <div class="card-body">
      <!-- <form id="logs-filter-form" class="row filter-row align-items-end">
        <div class="col-md-2">
          <label>User</label>
          <select id="filter-user" class="form-select" name="user">
            <option value="">All</option>
          </select>
        </div>
        <div class="col-md-2">
          <label>Role</label>
          <select id="filter-role" class="form-select" name="role">
            <option value="">All</option>
           
          </select>
        </div>
        <div class="col-md-2">
          <label>Event Type</label>
          <input
            type="text"
            id="filter-eventType"
            class="form-control"
            name="eventType"
            placeholder="Event Type"
          />
        </div>
        <div class="col-md-2">
          <label>Action</label>
          <input
            type="text"
            id="filter-action"
            class="form-control"
            name="action"
            placeholder="Action"
          />
        </div>
        <div class="col-md-2">
          <label>Target</label>
          <input
            type="text"
            id="filter-target"
            class="form-control"
            name="target"
            placeholder="Target"
          />
        </div>
        <div class="col-md-2">
          <label>IP Address</label>
          <input
            type="text"
            id="filter-ip"
            class="form-control"
            name="ipAddress"
            placeholder="IP Address"
          />
        </div>
        <div class="col-md-2">
          <label>User Agent</label>
          <input
            type="text"
            id="filter-userAgent"
            class="form-control"
            name="userAgent"
            placeholder="User Agent"
          />
        </div>
        <div class="col-md-3">
          <label>Date Range</label>
          <input
            type="text"
            id="filter-date-range"
            class="form-control"
            name="dateRange"
            placeholder="Select date range"
          />
        </div>
        <div class="col-md-2">
          <label>Keyword</label>
          <input
            type="text"
            id="filter-keyword"
            class="form-control"
            name="keyword"
            placeholder="Keyword"
          />
        </div>
        <div class="col-md-1">
          <button type="submit" class="btn btn-primary w-100">Filter</button>
        </div>
        <div class="col-md-1">
          <button
            type="button"
            id="reset-filters"
            class="btn btn-secondary w-100"
          >
            Reset
          </button>
        </div>
      </form> -->
      <form id="logs-filter-form" class="row g-3 align-items-end">
        <div class="col-12 col-md-6 col-lg-3">
          <label>User</label>
          <select id="filter-user" class="form-select w-100" name="user">
            <option value="">All</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>Role</label>
          <select id="filter-role" class="form-select w-100" name="role">
            <option value="">All</option>
          </select>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>Event Type</label>
          <select id="filter-eventType" class="form-select" name="eventType">
            <option value="">All</option>
          </select>
        </div>

        <div class="col-12 col-md-6 col-lg-3">
          <label>Action</label>
          <select id="filter-action" class="form-select" name="action">
            <option value="">All</option>
          </select>
        </div>

        <div class="col-12 col-md-6 col-lg-3">
          <label>Target</label>
          <input
            type="text"
            id="filter-target"
            class="form-control"
            name="target"
            placeholder="Target"
          />
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>IP Address</label>
          <input
            type="text"
            id="filter-ip"
            class="form-control"
            name="ipAddress"
            placeholder="IP Address"
          />
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>User Agent</label>
          <input
            type="text"
            id="filter-userAgent"
            class="form-control"
            name="userAgent"
            placeholder="User Agent"
          />
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>Date Range</label>
          <input
            type="text"
            id="filter-date-range"
            class="form-control"
            name="dateRange"
            placeholder="Select date range"
          />
        </div>
        <div class="col-12 col-md-6 col-lg-3">
          <label>Keyword</label>
          <input
            type="text"
            id="filter-keyword"
            class="form-control"
            name="keyword"
            placeholder="Keyword"
          />
        </div>
        <div class="col-6 col-md-3 col-lg-2 d-flex">
          <button type="submit" class="btn btn-primary w-100">Filter</button>
        </div>
        <div class="col-6 col-md-3 col-lg-2 d-flex">
          <button
            type="button"
            id="reset-filters"
            class="btn btn-secondary w-100"
          >
            Reset
          </button>
        </div>
      </form>

      <hr />
      <div class="table-responsive">
        <table
          id="logs-table"
          class="table table-striped table-bordered"
          style="width: 100%"
        >
          <thead>
            <tr>
              <th>Date</th>
              <th>User</th>
              <th>Role</th>
              <th>Event Type</th>
              <th>Action</th>
              <th>Target</th>
              <th>IP Address</th>
              <th>User Agent</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>

<script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
<script src="/assets/libs/flatpickr/flatpickr.min.js"></script>
<script src="/assets/libs/choices.js/public/assets/scripts/choices.min.js"></script>

<script>
  $(document).ready(function () {
    const roles = [
      { value: "", label: "All" },
      { value: "Admin", label: "Admin" },
      { value: "Manager", label: "Manager" },
      { value: "Editor", label: "Editor" },
      { value: "Viewer", label: "Viewer" },
      { value: "Marketer", label: "Marketer" },
      { value: "Sales", label: "Sales" },
      { value: "Client", label: "Client" },
      { value: "Support", label: "Support" },
    ];

    const actionOptions = [
      { value: "", label: "All" },
      { value: "Login", label: "Login" },
      { value: "Logout", label: "Logout" },
      { value: "Invite User", label: "Invite User" },
      { value: "Role Update", label: "Role Update" },
      { value: "Remove User", label: "Remove User" },
      { value: "Create", label: "Create" },
      { value: "Update", label: "Update" },
      { value: "Delete", label: "Delete" },
      { value: "Generate", label: "Generate" },
      { value: "Export", label: "Export" },
      { value: "Download", label: "Download" },
      { value: "Assign", label: "Assign" },
      { value: "Revoke", label: "Revoke" },
      { value: "Modify", label: "Modify" },
      { value: "Add Contacts", label: "Add Contacts" },
      { value: "Remove Contacts", label: "Remove Contacts" },
      { value: "Submission Received", label: "Submission Received" },
      { value: "Triggered Manual", label: "Triggered Manual" },
      { value: "Triggered Auto", label: "Triggered Auto" },
      { value: "Logo", label: "Logo" },
      { value: "Color", label: "Color" },
      { value: "Favicon", label: "Favicon" },
      { value: "Generated", label: "Generated" },
      { value: "Updated", label: "Updated" },
      { value: "Created", label: "Created" },
      { value: "Revoked", label: "Revoked" },
      { value: "Upload", label: "Upload" },
      { value: "Error", label: "Error" },
      { value: "Exception", label: "Exception" },
    ];
    const targetOptions = [
      { value: "", label: "All" },
      { value: "Authentication", label: "Authentication" },
      { value: "User Management", label: "User Management" },
      { value: "Design", label: "Design" },
      { value: "Certificate", label: "Certificate" },
      { value: "Badge", label: "Badge" },
      { value: "Contact List", label: "Contact List" },
      { value: "Form", label: "Form" },
      { value: "Event", label: "Event" },
      { value: "Branding", label: "Branding" },
      { value: "Script", label: "Script" },
      { value: "Embed Code", label: "Embed Code" },
      { value: "API Key", label: "API Key" },
      { value: "File Upload", label: "File Upload" },
      { value: "System", label: "System" },
    ];

    const roleChoices = new Choices("#filter-role", {
      searchEnabled: true,
      itemSelectText: "",
      classNames: {
        containerInner: "choices__inner w-100",
      },
    });

    const userChoices = new Choices("#filter-user", {
      searchEnabled: true,
      itemSelectText: "",
      classNames: {
        containerInner: "choices__inner w-100",
      },
    });

    const actionChoices = new Choices("#filter-action", {
      searchEnabled: true,
      itemSelectText: "",
      classNames: {
        containerInner: "choices__inner w-100",
      },
    });

    const eventChoices = new Choices("#filter-eventType", {
      choices: targetOptions,
      shouldSort: false,
      searchEnabled: true,
      itemSelectText: "",
      classNames: {
        containerInner: "choices__inner w-100",
      },
    });

    const datePicker = flatpickr("#filter-date-range", {
      mode: "range",
      dateFormat: "Y-m-d",
    });

    let originalUserChoices = [{ value: "", label: "All" }];

    // Load user choices from API
    $.ajax({
      url: "/api/users",
      method: "GET",
      success: function (users) {
        const dynamicChoices = users.map((u) => ({
          value: u._id,
          label: u.displayName || `${u.firstName} ${u.lastName}`,
        }));
        originalUserChoices = [...originalUserChoices, ...dynamicChoices];
        userChoices.setChoices(originalUserChoices, "value", "label", true);
        roleChoices.setChoices(roles, "value", "label", true);
        actionChoices.setChoices(actionOptions, "value", "label", true);
        eventChoices.setChoices(targetOptions, "value", "label", true);
      },
    });

    // DataTable config
    const table = $("#logs-table").DataTable({
      serverSide: true,
      searching: false,
      lengthChange: true,
      pageLength: 10,
      lengthMenu: [
        [10, 50, 100, 1000],
        [10, 50, 100, 1000],
      ],
      ajax: function (data, callback) {
        const params = getFilters();
        params.page = Math.floor(data.start / data.length) + 1;
        params.limit = data.length;

        if (data.order && data.order.length) {
          const orderCol =
            data.columns[data.order[0].column].data ||
            data.columns[data.order[0].column].name;
          const orderDir = data.order[0].dir;
          params.sortField = orderCol;
          params.sortOrder = orderDir;
        }

        $.ajax({
          url: "/api/logs/paginated",
          data: params,
          success: function (res) {
            callback({
              data: res.logs.map((log) => ({
                timestamp: log.timestamp
                  ? new Date(log.timestamp).toLocaleString()
                  : "",
                user: log.user?.name || log.user?.email || "",
                role: log.role || "",
                eventType: log.eventType || "",
                action: log.action || "",
                target:
                  log.target.length >= 28
                    ? log.target.slice(0, 28) + "..."
                    : log.target,
                ipAddress: log.ipAddress || "",
                userAgent: log.userAgent || "",
              })),
              recordsTotal: res.totalLogs,
              recordsFiltered: res.totalLogs,
            });
          },
        });
      },
      columns: [
        { data: "timestamp", title: "Date" },
        { data: "user", title: "User" },
        { data: "role", title: "Role" },
        { data: "eventType", title: "Event Type" },
        { data: "action", title: "Action" },
        { data: "target", title: "Target" },
        { data: "ipAddress", title: "IP Address" },
        { data: "userAgent", title: "User Agent" },
      ],
    });

    // Filter submit
    $("#logs-filter-form").on("submit", function (e) {
      e.preventDefault();
      table.ajax.reload();
    });

    // Reset filters
    $("#reset-filters").on("click", function () {
      $("#logs-filter-form")[0].reset();

      // Reset Choices.js by re-setting the original list and selecting "All"
      userChoices.clearChoices();
      roleChoices.clearChoices();
      actionChoices.clearChoices();
      eventChoices.clearChoices();
      userChoices.setChoices(originalUserChoices, "value", "label", true);
      roleChoices.setChoices(roles, "value", "label", true);
      actionChoices.setChoices(actionOptions, "value", "label", true);
      eventChoices.setChoices(targetOptions, "value", "label", true);
      userChoices.setChoiceByValue("");
      roleChoices.setChoiceByValue("");
      actionChoices.setChoiceByValue("");
      eventChoices.setChoiceByValue("");
      datePicker.clear();

      table.ajax.reload();
    });

    function getFilters() {
      const form = $("#logs-filter-form").serializeArray();
      const filters = {};
      form.forEach((f) => (filters[f.name] = f.value));
      if (filters.dateRange) {
        const [start, end] = filters.dateRange.split(" to ");
        filters.startDate = start;
        filters.endDate = end || start;
      }
      return filters;
    }
  });
</script>
