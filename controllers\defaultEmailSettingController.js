const { ajaxSettings } = require("jquery");
const DefaultEmailSetting = require("../models/DefaultEmailSetting");

// Create or Update Default Email and Template
exports.setDefaultEmailSetting = async (req, res) => {
  try {
    const { email, template, default: isDefault } = req.body;

    if (!email || !template) {
      return res
        .status(400)
        .json({ message: "Email and template are required" });
    }

    let settings = await DefaultEmailSetting.find({
      default: true,
      businessId: req.user.businessId,
    });
    const shoudlBeDefault = settings.length === 0 ? true : isDefault;
    if (shoudlBeDefault) {
      await DefaultEmailSetting.updateMany(
        { businessId: req.user.businessId },
        { $set: { default: false } }
      );
    }
    const setting = await DefaultEmailSetting.create({
      email,
      template,
      businessId: req.user.businessId,
      createdBy: req.user._id,
      default: shoudlBeDefault,
    });

    res
      .status(200)
      .json({ message: "Default email setting saved", data: setting });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Get the Default Email and Template
exports.getDefaultEmailSetting = async (req, res) => {
  try {
    const setting = await DefaultEmailSetting.findOne({
      default: true,
      businessId: req.user.businessId,
    });
    res.status(200).json({ data: setting });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to fetch settings", error: error.message });
  }
};

// Delete the Setting
exports.deleteDefaultEmailSetting = async (req, res) => {
  try {
    const deleted = await DefaultEmailSetting.findOneAndDelete({
      default: true,
      businessId: req.user.businessId,
    });
    if (!deleted) return res.status(404).json({ message: "No setting found" });
    res.status(200).json({ message: "Default email setting deleted" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to delete setting", error: error.message });
  }
};

exports.toggleDefault = async (req, res) => {
  try {
    const { id } = req.params;
    const setting = await DefaultEmailSetting.findById(id);
    if (!setting) return res.status(404).json({ message: "Setting not found" });

    await DefaultEmailSetting.updateMany(
      { businessId: req.user.businessId },
      { $set: { default: false } }
    );
    setting.default = !setting.default;
    await setting.save();
    res.status(200).json({ message: "Setting toggled", data: setting });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to toggle setting", error: error.message });
  }
};


exports.updateDefaultEmailSetting = async (req, res) => {
  try {
    const { id } = req.params;
    const { email, template } = req.body;
    const setting = await DefaultEmailSetting.findByIdAndUpdate(
      id,
      { email, template },
      { new: true }
    );
    if (!setting) return res.status(404).json({ message: "Setting not found" });

    res.status(200).json({ message: "Setting updated", data: setting });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Failed to update setting", error: error.message });
  }
};
