const { v4: uuidv4 } = require('uuid');
const path = require('path');
const Certificate = require('../models/Certificate');

// Create a new certificate and redirect to the editor
exports.createCertificate = async (req, res) => {
    try {
        // Create a new Certificate instance with req.body and additional fields
        const certificate = new Certificate({ 
            ...req.body, // Spread the properties from req.body directly into the Certificate
            createdBy: req.user._id, // Add createdBy field from req.user._id
            elements: req.body.elements || [], // Set elements with a default empty array if not provided
            variables: req.body.variables || [] // Set variables with a default empty array if not provided
        });

        // Save the certificate to the database
        await certificate.save();
        
        // Respond with success
        res.status(201).json({ success: true, certificate });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Server Error', error });
    }
};


// Save (or update) certificate data
exports.saveCertificate = async (req, res) => {
    try {
        const { id, elements } = req.body;
        const certificate = await Certificate.findById(id);
        if (certificate) {
            if (!certificate.certificateId) {
                certificate.certificateId = uuidv4(); // Generate a UUID if not present
            }
            certificate.elements = elements;
            certificate.updatedAt = Date.now();
            await certificate.save();
            res.status(200).json({ success: true, certificate });
        } else {
            res.status(404).json({ success: false, message: 'Certificate not found' });
        }
    } catch (error) {
        res.status(500).json({ success: false, message: 'Server Error', error });
    }
};

exports.getCertificates = async (req, res) => {
    try {
        let { page = 1, limit = 50, search = "", orderColumn = "_id", orderDir = "asc" } = req.query;
        page = parseInt(page);
        limit = parseInt(limit);
    
        const query = {}; // Base query
    
         if (search) {
         query.certificateName = { $regex: search, $options: "i" };
        }

        const sort = {};
        if (orderColumn && orderDir) {
          if (orderColumn === 'certificateName') {
           
            sort["certificateName"] = orderDir === "asc" ? 1 : -1;
          } else if (orderColumn === '_id') {
            // Sort by _id field
            sort["_id"] = orderDir === "asc" ? 1 : -1;
          } else if(orderColumn === 'updatedAt') {
            sort["updatedAt"] = orderDir === "asc" ? 1 : -1;
          }
        }

        const certificates = await Certificate.find(query)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit)
      //  Get total count (before pagination)
    const totalCertificates = await Certificate.countDocuments(query);
       res.status(200).json({
        recordsTotal: totalCertificates,
        recordsFiltered: totalCertificates,
        totalPages: Math.ceil(totalCertificates / limit),
        currentPage: page,
        certificates,
      });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Error fetching certificates.' });
    }
};

exports.getCertificateById = async (req, res) => {
    try {
        const certificate = await Certificate.findById(req.params.id);
        if (certificate) {
            res.status(200).json({ success: true, certificate });
        } else {
            res.status(404).json({ success: false, message: 'Certificate not found.' });
        }
    } catch (err) {
        res.status(500).json({ success: false, message: 'Error fetching certificate.' });
    }
};

//open public contac API
exports.getPublicCertificateById = async (req, res) => {
    
    try {
        
        const certificate = await Certificate.findById(req.params.id).populate({
            path: 'contactId',
           populate: [
            {
              path: 'certificates', 
              model: 'Certificate',
            //   select: ['certificateName', 'recipientName', 'issueDate', 'expiryDate'] 
            },
            {
              path: 'badges', // Populate the badges field within the contactId
              model: 'Badge',
            //   select: ['title', 'badgeURL', 'dateOfIssue'] 
            }
          ]
        });
        if (certificate) {
            res.status(200).json({ success: true, certificate });
        } else {
            res.status(404).json({ success: false, message: 'Certificate not found.' });
        }
    } catch (err) {
        res.status(500).json({ success: false, message: 'Error fetching certificate.' });
    }
};


// Get Public Page Certificate by ID
exports.getPublicPageCertificateById = async (req, res) => {
    try {
        // Extract certificateId from the URL
        const certificateId = req.params.id;

        //console.log("certificateId in pub",certificateId)
        // Find the certificate by ID and populate contactId to get all badges and certificates listed on profile
        //const certificate = await Certificate.findById(contactId).populate('contactId');


        const certificate = await Certificate.findById(certificateId)
        .populate({
          path: 'contactId',
          populate: [
            {
              path: 'certificates', // Populate the certificates field within the contactId
              model: 'Certificate',
              select: ['certificateName', 'recipientName', 'issueDate', 'expiryDate'] // Adjust fields as needed
            },
            {
              path: 'badges', // Populate the badges field within the contactId
              model: 'Badge',
              select: ['title', 'badgeURL', 'dateOfIssue'] // Adjust fields as needed
            }
          ]
        })
        .exec();
        
      
      console.log(certificate);
        if (certificate) {
            // If the certificate exists, serve the certificate HTML page
            //res.sendFile(path.join(__dirname, '../views', 'certificate.html'));
            res.render('public-certificate', { title: 'Certificate', layout: 'layouts/publicPages' });
        } else {
            // If the certificate does not exist, return a 404 response with a JSON message
            //res.status(404).json({ success: false, message: 'Certificate not found.' });

            res.sendFile(path.join(__dirname, '../views', 'certificate404.html'));
        }
    } catch (err) {
        
        // Handle errors and return a 500 response with a JSON message
        res.status(500).json({ success: false, message: err.message });
    }
};


exports.updateCertificate = async (req, res) => {
    try {
        const { name, elements, variables } = req.body;
        const certificate = await Certificate.findByIdAndUpdate(req.params.id, { name, elements, variables, updatedAt: Date.now() }, { new: true });

        if (certificate) {
            res.status(200).json({ success: true, message: 'Certificate updated successfully!', certificate });
        } else {
            res.status(404).json({ success: false, message: 'Certificate not found.' });
        }
    } catch (err) {
        res.status(500).json({ success: false, message: 'Error updating certificate.' });
    }
};

exports.deleteCertificate = async (req, res) => {
    try {
        const certificate = await Certificate.findByIdAndDelete(req.params.id);
        if (certificate) {
            res.status(200).json({ success: true, message: 'Certificate deleted successfully!' });
        } else {
            res.status(404).json({ success: false, message: 'Certificate not found.' });
        }
    } catch (err) {
        res.status(500).json({ success: false, message: 'Error deleting certificate.' });
    }
};

// Get total count of Certificate documents
exports.getCertificateCount = async (req, res) => {
    try {
        const totalCount = await Certificate.countDocuments();
        res.json({ totalCount });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Get estimated count of Certificate documents
exports.getEstimatedCertificateCount = async (req, res) => {
    try {
        const estimatedCount = await Certificate.estimatedDocumentCount();
        res.json({ estimatedCount });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Helper function to format the response
const formatResponse = (results, monthsBack = 0) => {
    const data = [];
    const categories = [];

    const monthlyCounts = {};

    // Populate the map with results from the database
    results.forEach(result => {
        const { year, month } = result._id;
        const monthName = new Date(year, month - 1).toLocaleString('default', { month: 'short' });
        const key = `${monthName} ${year}`;
        monthlyCounts[key] = result.count;
    });

    // Generate the full list of months for the specified range
    const currentDate = new Date();
    for (let i = 0; i <= monthsBack; i++) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i);
        const year = date.getFullYear();
        const month = date.getMonth();
        const monthName = date.toLocaleString('default', { month: 'short' });
        const key = `${monthName} ${year}`;

        categories.unshift(key);  // Unshift to add at the beginning since we're iterating backwards
        data.unshift(monthlyCounts[key] || 0); // Use 0 if there's no data
    }

    return { data, categories };
};

// Controller to get certificates count for this year
exports.getCertificatesThisYear = async (req, res) => {
    try {
        const now = new Date();
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);

        const results = await Certificate.aggregate([
            { $match: { createdAt: { $gte: startOfYear, $lte: endOfYear } } },
            { $group: { _id: { year: { $year: "$createdAt" }, month: { $month: "$createdAt" } }, count: { $sum: 1 } } },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
        ]);

        const response = formatResponse(results, now.getMonth());
        res.json(response);
    } catch (err) {
        console.error('Error fetching certificate count:', err);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Controller to get certificates count for last year
exports.getCertificatesLastYear = async (req, res) => {
    try {
        const now = new Date();
        const startOfLastYear = new Date(now.getFullYear() - 1, 0, 1);
        const endOfLastYear = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59, 999);

        const results = await Certificate.aggregate([
            { $match: { createdAt: { $gte: startOfLastYear, $lte: endOfLastYear } } },
            { $group: { _id: { year: { $year: "$createdAt" }, month: { $month: "$createdAt" } }, count: { $sum: 1 } } },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
        ]);

        const response = formatResponse(results, 11); // Full year range
        res.json(response);
    } catch (err) {
        console.error('Error fetching certificate count:', err);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Controller to get certificates count for last 5 years
exports.getCertificatesLast5Years = async (req, res) => {
    try {
        const now = new Date();
        const fiveYearsAgo = new Date(now.getFullYear() - 5, now.getMonth(), 1);

        const results = await Certificate.aggregate([
            { $match: { createdAt: { $gte: fiveYearsAgo } } },
            { $group: { _id: { year: { $year: "$createdAt" }, month: { $month: "$createdAt" } }, count: { $sum: 1 } } },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
        ]);

        const monthsBack = (now.getFullYear() - fiveYearsAgo.getFullYear()) * 12 + now.getMonth();
        const response = formatResponse(results, monthsBack); // Last 5 years
        res.json(response);
    } catch (err) {
        console.error('Error fetching certificate count:', err);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Controller to get certificates count for last 12 months
exports.getCertificatesLast12Months = async (req, res) => {
    try {
        const now = new Date();
        const twelveMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 11, 1);

        const results = await Certificate.aggregate([
            { $match: { createdAt: { $gte: twelveMonthsAgo } } },
            { $group: { _id: { year: { $year: "$createdAt" }, month: { $month: "$createdAt" } }, count: { $sum: 1 } } },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
        ]);

        const response = formatResponse(results, 11); // Last 12 months
        res.json(response);
    } catch (err) {
        console.error('Error fetching certificate count:', err);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};

// Controller to get certificates count for a custom date range
exports.getCertificatesCustomDateRange = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;

        if (!startDate || !endDate) {
            return res.status(400).json({ message: 'Start date and end date are required' });
        }

        const start = new Date(startDate);
        const end = new Date(endDate);

        const results = await Certificate.aggregate([
            { $match: { createdAt: { $gte: start, $lte: end } } },
            { $group: { _id: { year: { $year: "$createdAt" }, month: { $month: "$createdAt" } }, count: { $sum: 1 } } },
            { $sort: { "_id.year": 1, "_id.month": 1 } }
        ]);

        // Calculate months between start and end dates
        const monthsBack = Math.floor((end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth());
        const response = formatResponse(results, monthsBack);
        res.json(response);
    } catch (err) {
        console.error('Error fetching certificate count:', err);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};


