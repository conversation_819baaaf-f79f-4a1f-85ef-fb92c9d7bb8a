const mongoose = require("mongoose");

const formSchema = new mongoose.Schema({
  name: { type: String, required: true },
  isEnabled: { type: Boolean, default: true },
  isDefault: { type: Boolean, default: false },
  emailNotifications: {
    sendToCustom: { type: Boolean, default: false },
    customEmail: { type: String },
    sendToCandidate: { type: Boolean, default: false },
  },
  webhookUrl: { type: String },
  createdAt: { type: Date, default: Date.now },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  businessId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Business",
  },
});

module.exports = mongoose.model("Form", formSchema);
