const LocalStrategy = require("passport-local").Strategy;
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const MicrosoftStrategy = require("passport-microsoft").Strategy;
const User = require("../models/User");
const bcrypt = require("bcryptjs");

module.exports = function (passport) {
  passport.use(
    new LocalStrategy(
      { usernameField: "email" },
      async (email, password, done) => {
        try {
          // Match user
          const user = await User.findOne({ email });
          if (!user) {
            return done(null, false, {
              message: "That email is not registered",
            });
          }

          // Match password
          const isMatch = await bcrypt.compare(password, user.password);
          if (!isMatch) {
            return done(null, false, { message: "Password incorrect" });
          }

          return done(null, user);
        } catch (err) {
          return done(err);
        }
      }
    )
  );

  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL:
          process.env.NODE_ENV === "production"
            ? `${process.env.APP_HOST}auth/callback/google`
            : `http://localhost:8500/auth/callback/google`,
        scope: ["profile", "email"],
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          // Safe access with fallback
          const email = profile.emails?.[0]?.value;
          if (!email) {
            return done(new Error("Email not provided by Google profile."));
          }

          const user = await User.findOne({ email });
          if (user) {
            user.isAccountDeleted = false;
            await user.save();
            return done(null, user);
          } else {
            const newUser = new User({
              firstName: profile.name.givenName,
              lastName: profile.name.familyName,
              displayName: profile.displayName || "",
              email,
              password: profile.id,

              profilePicture:
                profile.photos?.[0]?.value ||
                "/assets/images/users/avatar-1.jpg",
              verificationToken: null,
              isVerified: true,
            });
            await newUser.save();
            return done(null, newUser);
          }
        } catch (err) {
          return done(err);
        }
      }
    )
  );

  passport.use(
    new MicrosoftStrategy(
      {
        clientID: process.env.MICROSOFT_CLIENT_ID,
        clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
        callbackURL:
          process.env.NODE_ENV === "production"
            ? `${process.env.APP_HOST}auth/callback/microsoft`
            : `http://localhost:8500/auth/callback/microsoft`,
        scope: ["user.read"],
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          const email = profile.emails?.[0]?.value;
          console.log("profile", profile);
          if (!email) {
            return done(new Error("Email not provided by Microsoft profile."));
          }

          const user = await User.findOne({ email });
          if (user) {
            user.isAccountDeleted = false;
            await user.save();
            return done(null, user);
          } else {
            const newUser = new User({
              firstName: profile.name.givenName,
              lastName: profile.name.familyName,
              displayName: profile.displayName || "",
              email,
              password: profile.id,

              profilePicture:
                profile.photos?.[0]?.value ||
                "/assets/images/users/avatar-1.jpg",
              verificationToken: null,
              isVerified: true,
            });
            await newUser.save();
            return done(null, newUser);
          }
        } catch (err) {
          return done(err);
        }
      }
    )
  );

  passport.serializeUser((user, done) => {
    done(null, user.id);
  });

  passport.deserializeUser((id, done) => {
    User.findById(id, (err, user) => {
      done(err, user);
    });
  });
};
