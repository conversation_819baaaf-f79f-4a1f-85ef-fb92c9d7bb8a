<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", {"title": "All Certificates" }) %>

<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>

<%- contentFor('body') %>
<%-include("partials/page-title",{"title":"Certificates" , "pagetitle": "All" })%>

<!-- All certificates UI Starts -->
<div class="">
  <span class="text-muted fw-normal mb-3" id="totalCertificateCount"></span>
  <div class="mb-3 d-flex gap-2 justify-content-end">
    <a href="/designs/?type=certificate" target="_blank" class="btn btn-light"
      >Create New Certificate</a
    >
  </div>
  <table
    class="table table-responsive table-striped table-bordered"
    style="width: 100%"
    id="certificatesTable"
  >
    <thead>
      <tr>
        <th>Name</th>
        <th>ID</th>
        <th>Last Modified</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- Certificate rows will be dynamically added here -->
    </tbody>
  </table>
  <div
    id="loadingIndicator"
    style="display: none; text-align: center; margin: 20px"
  >
    <span class="spinner-border text-primary"></span>
  </div>
</div>

<!-- All certificates UI Ends -->

<%- contentFor('FooterJs') %>

<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>

<script>
  $(document).ready(function () {
    var certificatesLength = 0;
    let nameSpaceCode = "";

    function updateCertificatesLength(certificateLength) {
      certificatesLength = certificateLength;
      $("#totalCertificateCount").text(`Total : (${certificatesLength || 0})`);
    }

    
    window.deleteCertificate = function (id) {
      if (confirm("Are you sure you want to delete this certificate?")) {
        $.ajax({
          url: "/api/certificates/" + id,
          method: "DELETE",
          success: function (response) {
            if (response.success) {
              fetchCertificates();
            } else {
              showToast("Error deleting certificate.", "danger");
            }
          },
          error: function () {
            showToast("Error deleting certificate.", "danger");
          },
        });
      }
    };

    // Load user profile and business namespace
    $.ajax({
      url: "/api/users/profile",
      method: "GET",
      success: function (response) {
        if (response && response.businessId) {
          fetchNameSpaceFromBusinessData(response.businessId);
        }
      },
      error: function (error) {
        console.log(error);
      },
    });

    function fetchNameSpaceFromBusinessData(businessId) {
      $.ajax({
        url: "/api/business/" + businessId,
        method: "GET",
        success: function (response) {
          if (response && response.nameSpaceCode) {
            nameSpaceCode = response.nameSpaceCode;
            initializeDataTable(); // Only after nameSpaceCode is set
          }
        },
        error: function (error) {
          console.log(error);
        },
      });
    }

    function initializeDataTable() {
      const table = $("#certificatesTable").DataTable({
        responsive: true,
        serverSide: true,
        searching: true,
        ordering: true,
        pageLength: 50,
        lengthMenu: [[10, 50, 100, 500, 1000], [10, 50, 100, 500, 1000]],
        order: [[1, "asc"]],
        columnDefs: [{ orderable: false, targets: -1 }],
        ajax: {
          url: "/api/certificates",
          method: "GET",
          data: function (d) {
            const orderColumn =
              d.columns && d.order && d.order.length > 0
                ? d.columns[d.order[0].column].data
                : "_id";
            const orderDir =
              d.order && d.order.length > 0 ? d.order[0].dir : "desc";

            return {
              draw: d.draw,
              page: Math.floor(d.start / d.length) + 1,
              limit: d.length,
              search: d.search.value,
              orderColumn,
              orderDir,
            };
          },
          dataSrc: function (json) {
            showToast("Certificate fetched successfully!", "primary");
            updateCertificatesLength(json.recordsTotal);
            return json.certificates;
          },
          beforeSend: function () {
            $("#loadingIndicator").show();
          },
          complete: function () {
            $("#loadingIndicator").hide();
          },
        },
        columns: [
          { data: "certificateName" },
          { data: "_id" },
          {
            data: "updatedAt",
            render: function (data, type, row, meta) {
              return new Date(row.updatedAt).toLocaleString();
            },
          },
          {
            data: null,
            render: function (data, type, row, meta) {
              return `
                <a href="/${nameSpaceCode}/certificate/${row._id}" target="_blank" class="btn btn-sm btn-primary">View</a>
                <a href="/design/${row._id}" class="btn btn-sm btn-primary">Edit</a>
                <button class="btn btn-sm btn-danger" onclick="deleteCertificate('${row._id}')">Delete</button>
              `;
            },
          },
        ],
      });

      window.fetchCertificates = function () {
        table.ajax.reload(null, false);
      };
    }
  });
</script>
