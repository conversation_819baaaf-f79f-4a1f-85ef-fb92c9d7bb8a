<header id="page-topbar">
  <div class="navbar-header">
    <div class="d-flex">
      <!-- LOGO -->
      <div class="navbar-brand-box">
        <a href="/" class="logo logo-dark">
          <span class="logo-sm">
            <img src="/assets/images/logo-sm.svg" alt="" height="24" />
          </span>
          <span class="logo-lg">
            <img src="/assets/images/logo-sm.svg" alt="" height="24" />
            <span class="logo-txt">MixCertificate</span>
          </span>
        </a>

        <a href="/" class="logo logo-light">
          <span class="logo-sm">
            <img src="/assets/images/logo-sm.svg" alt="" height="24" />
          </span>
          <span class="logo-lg">
            <img src="/assets/images/logo-sm.svg" alt="" height="24" />
            <span class="logo-txt">MixCertificate</span>
          </span>
        </a>
      </div>

      <button
        type="button"
        class="btn btn-sm px-3 font-size-16 header-item"
        id="vertical-menu-btn"
      >
        <i class="fa fa-fw fa-bars"></i>
      </button>

      <!-- App Search-->
      <form class="app-search d-lg-block">
        <div class="position-relative">
          <input type="text" class="form-control" placeholder="Search..." />
          <button class="btn btn-primary" type="button">
            <i class="bx bx-search-alt align-middle"></i>
          </button>
        </div>
      </form>
    </div>

    <div class="d-flex">
      <div class="dropdown d-inline-block d-lg-none ms-2">
        <!-- <button
          type="button"
          class="btn header-item"
          id="page-header-search-dropdown"
          data-bs-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <i data-feather="search" class="icon-lg"></i>
        </button> -->
        <div
          class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0"
          aria-labelledby="page-header-search-dropdown"
        >
          <form class="p-3">
            <div class="form-group m-0 d-none">
              <div class="input-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Search ..."
                  aria-label="Search Result"
                />

                <button class="btn btn-primary" type="submit">
                  <i class="mdi mdi-magnify"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="dropdown d-none d-sm-inline-block">
        <button
          id="publicHomeButton"
          class="btn header-item font-size-18"
          style="margin-top: 4px;"
        >
          <i style="font-size: 1.2rem" class="bx bx-home"></i>
        </button>

        <!-- Full Screen Button -->
        <button
          id="fullscreenButton"
          class="btn header-item font-size-18"
          style="margin-top: 2px"
        >
          <i class="fas fa-expand"></i>
        </button>

        <button
          type="button"
          class="btn header-item d-none"
          data-bs-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <% if(lang=="en" ) { %>
          <img
            id="header-lang-img"
            src="/assets/images/flags/us.jpg"
            alt="Header Language"
            height="16"
          />
          <!-- <span class="align-middle">English</span> -->
          <% } else if(lang=="gr" ) { %>
          <img
            src="/assets/images/flags/germany.jpg"
            id="header-lang-img"
            alt="Header Language"
            height="20"
          />
          <!-- <span class="align-middle">German</span> -->
          <% } else if(lang=="it" ) { %>
          <img
            src="/assets/images/flags/italy.jpg"
            id="header-lang-img"
            alt="Header Language"
            height="20"
          />
          <!-- <span class="align-middle">Italian</span> -->
          <% } else if(lang=="ru" ) { %>
          <img
            src="/assets/images/flags/russia.jpg"
            id="header-lang-img"
            alt="Header Language"
            height="20"
          />
          <!-- <span class="align-middle">Russian</span> -->
          <% } else if(lang=="sp" ) { %>
          <img
            src="/assets/images/flags/spain.jpg"
            id="header-lang-img"
            alt="Header Language"
            height="20"
          />
          <!-- <span class="align-middle">Spanish</span> -->
          <% } else { %>
          <img
            src="/assets/images/flags/us.jpg"
            id="header-lang-img"
            alt="Header Language"
            height="20"
          />
          <!-- <span class="align-middle">English</span> -->
          <% } %>
        </button>
        <div class="dropdown-menu dropdown-menu-end">
          <!-- item-->
          <a
            href="?clang=en"
            class="dropdown-item notify-item language"
            data-lang="en"
          >
            <img
              src="/assets/images/flags/us.jpg"
              alt="user-image"
              class="me-1"
              height="12"
            />
            <span class="align-middle">English</span>
          </a>
          <!-- item-->
          <a
            href="?clang=sp"
            class="dropdown-item notify-item language"
            data-lang="sp"
          >
            <img
              src="/assets/images/flags/spain.jpg"
              alt="user-image"
              class="me-1"
              height="12"
            />
            <span class="align-middle">Spanish</span>
          </a>

          <!-- item-->
          <a
            href="?clang=gr"
            class="dropdown-item notify-item language"
            data-lang="gr"
          >
            <img
              src="/assets/images/flags/germany.jpg"
              alt="user-image"
              class="me-1"
              height="12"
            />
            <span class="align-middle">German</span>
          </a>

          <!-- item-->
          <a
            href="?clang=it"
            class="dropdown-item notify-item language"
            data-lang="it"
          >
            <img
              src="/assets/images/flags/italy.jpg"
              alt="user-image"
              class="me-1"
              height="12"
            />
            <span class="align-middle">Italian</span>
          </a>

          <!-- item-->
          <a
            href="?clang=ru"
            class="dropdown-item notify-item language"
            data-lang="ru"
          >
            <img
              src="/assets/images/flags/russia.jpg"
              alt="user-image"
              class="me-1"
              height="12"
            />
            <span class="align-middle">Russian</span>
          </a>
        </div>
      </div>

      <div class="dropdown d-none d-sm-inline-block">
        <button type="button" class="btn header-item" id="mode-setting-btn">
          <i data-feather="moon" class="icon-lg layout-mode-dark"></i>
          <i data-feather="sun" class="icon-lg layout-mode-light"></i>
        </button>
      </div>

      <div class="dropdown d-none d-lg-inline-block ms-1">
        <button
          type="button"
          class="d-none btn header-item"
          data-bs-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <i data-feather="grid" class="icon-lg"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
          <div class="p-2">
            <div class="row g-0">
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img src="/assets/images/brands/github.png" alt="Github" />
                  <span>GitHub</span>
                </a>
              </div>
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img
                    src="/assets/images/brands/bitbucket.png"
                    alt="bitbucket"
                  />
                  <span>Bitbucket</span>
                </a>
              </div>
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img
                    src="/assets/images/brands/dribbble.png"
                    alt="dribbble"
                  />
                  <span>Dribbble</span>
                </a>
              </div>
            </div>

            <div class="row g-0">
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img src="/assets/images/brands/dropbox.png" alt="dropbox" />
                  <span>Dropbox</span>
                </a>
              </div>
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img
                    src="/assets/images/brands/mail_chimp.png"
                    alt="mail_chimp"
                  />
                  <span>Mail Chimp</span>
                </a>
              </div>
              <div class="col">
                <a class="dropdown-icon-item" href="#">
                  <img src="/assets/images/brands/slack.png" alt="slack" />
                  <span>Slack</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dropdown d-inline-block">
        <button
          type="button"
          class="btn header-item noti-icon position-relative"
          id="page-header-notifications-dropdown"
          data-bs-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <i data-feather="bell" class="icon-lg"></i>
          <span class="badge bg-danger rounded-pill bellCount">0</span>
        </button>
        <div
          class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0"
          aria-labelledby="page-header-notifications-dropdown"
        >
          <div class="p-3">
            <div class="row align-items-center">
              <div class="col">
                <h6 class="m-0">Notifications</h6>
              </div>
              <div class="col-auto unreadNotifications d-none">
                <a href="#" class="small text-reset text-decoration-underline">
                  Unread (3)</a
                >
              </div>
            </div>
          </div>
          <div
            id="top-notification-bar"
            data-simplebar
            class="notification"
            style="max-height: 230px; overflow-y: auto"
          ></div>
          <div class="p-2 border-top">
            <div class="row">
              <div class="col-6">
                <div class="d-grid viewMoreBtn d-none">
                  <a
                    class="btn btn-sm btn-link font-size-14 text-center"
                    href="javascript:void(0)"
                  >
                    <i class="mdi mdi-arrow-right-circle me-1"></i>
                    <span>Load More</span>
                  </a>
                </div>
              </div>
              <div class="col-6">
                <div class="d-grid">
                  <a
                    class="btn btn-sm btn-primary font-size-14 text-center"
                    href="/notifications/"
                    style="background-color: #5156be; border-color: #5156be"
                  >
                    <i class="mdi mdi-bell-outline me-1"></i> View All
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dropdown d-inline-block d-none">
        <button type="button" class="btn header-item right-bar-toggle me-2">
          <i data-feather="settings" class="icon-lg"></i>
        </button>
      </div>

      <div id="menu-help" class="dropdown d-inline-block">
        <button
          type="button"
          class="btn header-item bg-light-subtle border-start border-end"
          id="page-header-user-dropdown"
          data-bs-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <img
            id="profile-picture"
            class="rounded-circle header-profile-user"
            src="/assets/images/users/avatar-1.jpg"
            alt="Header Avatar"
          />
          <span
            class="d-none d-xl-inline-block ms-1 fw-medium"
            id="display-name"
            >User</span
          >
          <i class="mdi mdi-chevron-down d-none d-xl-inline-block"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-end">
          <!-- item-->
          <a class="dropdown-item" href="/settings-profile/"
            ><i class="mdi mdi mdi-face-man font-size-16 align-middle me-1"></i>
            Profile</a
          >
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="/settings">
            <i class="mdi mdi-cog font-size-16 align-middle me-1"></i>
            Settings
          </a>
          <div class="dropdown-divider"></div>

          <a
            class="dropdown-item"
            href="https://help.mixcommerce.co/mixcertificate/"
            target="_blank"
          >
            <i class="mdi mdi-help-circle font-size-16 align-middle me-1"></i>
            Help
          </a>
          <a class="dropdown-item d-none" href="/auth-lock-screen"
            ><i class="mdi mdi-lock font-size-16 align-middle me-1"></i> Lock
            Screen</a
          >
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="/logout"
            ><i class="mdi mdi-logout font-size-16 align-middle me-1"></i>
            Logout</a
          >
        </div>
      </div>
    </div>
  </div>
</header>
