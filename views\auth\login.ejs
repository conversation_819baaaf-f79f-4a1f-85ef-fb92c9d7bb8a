<%- contentFor('HeaderCss') %> <%-include("../partials/title-meta", { "title": "Login" }) %> 
<%- contentFor('body') %>
<div class="auth-page min-vh-100">
  <div class="container-fluid h-100 p-0">
    <div class="row g-0 h-100">
      <div class="col-xxl-3 col-lg-4 col-md-5 h-100">
        <div class="auth-full-page-content d-flex p-sm-5 p-4 h-100">
          <div class="w-100 d-flex flex-column">
            <div class="d-flex flex-column mt-5">
              <div class="mb-4 mb-md-5 text-center">
                <a href="/" class="d-block auth-logo">
                  <img src="/assets/images/logo-sm.svg" alt="" height="28" />
                  <span class="logo-txt">MixCertificate</span>
                </a>
              </div>
              <div class="auth-content my-auto">
                <div class="text-center">
                  <h5 class="mb-0">Welcome Back!</h5>
                  <p class="text-muted mt-2">
                    Sign in to continue to MixCertificate.
                  </p>
                </div>
                <% if(message.length> 0) { %>
                <div
                  class="alert alert-success text-center mb-4 flash"
                  role="alert"
                >
                  <%= message %>
                </div>
                <% } %> <% if(error.length> 0) { %>
                <div
                  class="alert alert-danger text-center mb-4 flash"
                  role="alert"
                >
                  <%= error %>
                </div>
                <% } %>
                <form
                  class="mt-4 pt-2 siginForm"
                  action="/auth-validate"
                  method="post"
                >
                  <a href=""></a>

                  <input
                    type="hidden"
                    id="redirect_uri"
                    name="redirect_uri"
                    value=""
                  />

                  <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input
                      type="text"
                      class="form-control"
                      id="email"
                      name="email"
                      placeholder="Enter email"
                    />
                  </div>
                  <div class="mb-3">
                    <div class="d-flex align-items-start">
                      <div class="flex-grow-1">
                        <label class="form-label">Password</label>
                      </div>
                      <div class="flex-shrink-0">
                        <div class="">
                          <a href="/forgotpassword" class="text-muted"
                            >Forgot password?</a
                          >
                        </div>
                      </div>
                    </div>

                    <div class="input-group auth-pass-inputgroup">
                      <input
                        type="password"
                        class="form-control"
                        placeholder="Enter password"
                        minlenght="5"
                        name="password"
                        aria-label="Password"
                        aria-describedby="password-addon"
                      />
                      <button
                        class="btn btn-light shadow-none ms-0"
                        type="button"
                        id="password-addon"
                      >
                        <i class="mdi mdi-eye-outline"></i>
                      </button>
                    </div>
                  </div>
                  <div class="row mb-4">
                    <!-- <div class="col">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember-check">
                        <label class="form-check-label" for="remember-check">
                          Remember me
                        </label>
                      </div>
                    </div> -->
                  </div>
                  <div class="mb-3">
                    <button
                      class="btn btn-primary w-100 waves-effect waves-light loginBtn"
                      type="submit"
                    >
                      Log In
                    </button>
                  </div>
                  <div class="mb-3 text-center">
                    <a
                      href="/auth/google"
                      class="btn btn-light w-100 d-flex align-items-center justify-content-center border"
                    >
                      <img
                        src="https://developers.google.com/identity/images/g-logo.png"
                        alt="Google"
                        style="width: 20px; height: 20px; margin-right: 10px"
                      />
                      <span>Sign in with Google</span>
                    </a>
                  </div>
                  <div class="mb-3 text-center">
                    <a
                      href="/auth/microsoft"
                      class="btn btn-light w-100 d-flex align-items-center justify-content-center border"
                    >
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/4/44/Microsoft_logo.svg"
                        alt="Microsoft"
                        style="width: 20px; height: 20px; margin-right: 10px"
                      />

                      <span>Sign in with Microsoft</span>
                    </a>
                  </div>
                </form>

                <div class="mt-4 pt-2 text-center d-none">
                  <div class="signin-other-title">
                    <h5 class="font-size-14 mb-3 text-muted fw-medium">
                      - Sign in with -
                    </h5>
                  </div>

                  <ul class="list-inline mb-0">
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-primary text-white border-primary"
                      >
                        <i class="mdi mdi-facebook"></i>
                      </a>
                    </li>
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-info text-white border-info"
                      >
                        <i class="mdi mdi-twitter"></i>
                      </a>
                    </li>
                    <li class="list-inline-item">
                      <a
                        href="javascript:void()"
                        class="social-list-item bg-danger text-white border-danger"
                      >
                        <i class="mdi mdi-google"></i>
                      </a>
                    </li>
                  </ul>
                </div>

                <div class="mt-5 text-center">
                  <p class="text-muted mb-0">
                    Don't have an account ?
                    <a href="/register" class="text-primary fw-semibold">
                      Signup now
                    </a>
                  </p>
                </div>
              </div>
              <div class="mt-auto text-center py-3">
                <p class="mb-0 mt-2">
                  ©
                  <script>
                    document.write(new Date().getFullYear());
                  </script>
                  MixCertificate . Crafted with
                  <i alt="love" class="mdi mdi-heart text-danger"></i> by
                  <a target="_blank" href="https://www.mixcommerce.co/"
                    >MixCommerce</a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- end auth full page content -->
      </div>
      <!-- end col -->
      <div class="col-xxl-9 col-lg-8 col-md-7 d-none d-md-block">
        <div class="auth-bg h-100 pt-md-5 p-4 d-flex">
          <div class="bg-overlay bg-primary"></div>
          <ul class="bg-bubbles">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
          </ul>
          <!-- end bubble effect -->
          <div class="row justify-content-center align-items-center">
            <div class="col-xl-7">
              <div class="p-0 p-sm-4 px-xl-0">
                <div
                  id="reviewcarouselIndicators"
                  class="carousel slide"
                  data-bs-ride="carousel"
                >
                  <div
                    class="carousel-indicators carousel-indicators-rounded justify-content-start ms-0 mb-0"
                  >
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="0"
                      class="active"
                      aria-current="true"
                      aria-label="Slide 1"
                    ></button>
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="1"
                      aria-label="Slide 2"
                    ></button>
                    <button
                      type="button"
                      data-bs-target="#reviewcarouselIndicators"
                      data-bs-slide-to="2"
                      aria-label="Slide 3"
                    ></button>
                  </div>
                  <!-- end carouselIndicators -->
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “I feel confident imposing change on myself. It's a
                          lot more progressing fun than looking back. That's why
                          I ultricies enim at malesuada nibh diam on tortor
                          neaded to throw curve balls.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                              <img
                                src="/assets/images/users/avatar-1.jpg"
                                class="avatar-md img-fluid rounded-circle"
                                alt="..."
                              />
                            </div>
                            <div class="flex-grow-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Richard Drews
                              </h5>
                              <p class="mb-0 text-white-50">Web Designer</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="carousel-item">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “Our task must be to free ourselves by widening our
                          circle of compassion to embrace all living creatures
                          and the whole of quis consectetur nunc sit amet semper
                          justo. nature and its beauty.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                              <img
                                src="/assets/images/users/avatar-2.jpg"
                                class="avatar-md img-fluid rounded-circle"
                                alt="..."
                              />
                            </div>
                            <div class="flex-grow-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Rosanna French
                              </h5>
                              <p class="mb-0 text-white-50">Web Developer</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="carousel-item">
                      <div class="testi-contain text-white">
                        <i
                          class="bx bxs-quote-alt-left text-success display-6"
                        ></i>

                        <h4 class="mt-4 fw-medium lh-base text-white">
                          “I've learned that people will forget what you said,
                          people will forget what you did, but people will never
                          forget how donec in efficitur lectus, nec lobortis
                          metus you made them feel.”
                        </h4>
                        <div class="mt-4 pt-3 pb-5">
                          <div class="d-flex align-items-start">
                            <img
                              src="/assets/images/users/avatar-3.jpg"
                              class="avatar-md img-fluid rounded-circle"
                              alt="..."
                            />
                            <div class="flex-1 ms-3 mb-4">
                              <h5 class="font-size-18 text-white">
                                Ilse R. Eaton
                              </h5>
                              <p class="mb-0 text-white-50">Manager</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- end carousel-inner -->
                </div>
                <!-- end review carousel -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- end col -->
    </div>
    <!-- end row -->
  </div>
  <!-- end container fluid -->
</div>

<!-- code to get the redirect_uri from url and set on redirect_uri in hidden element of loginform to redirect the user to page he came from -->

<script>
  (function () {
    // Function to get URL parameters
    function getQueryParams() {
      var params = {};
      var queryString = window.location.search.substring(1);
      var regex = /([^&=]+)=([^&]*)/g;
      var m;
      while ((m = regex.exec(queryString))) {
        params[decodeURIComponent(m[1])] = decodeURIComponent(m[2]);
      }
      return params;
    }

    // Get the redirect_uri from URL parameters
    var params = getQueryParams();
    var redirectUri = params["redirect_uri"];

    // Set the value of the element with id "redirect_uri"
    if (redirectUri) {
      document.getElementById("redirect_uri").value = redirectUri;
    }
  })();
</script>

<%- contentFor('FooterJs') %>
<script src="/assets/js/pages/pass-addon.init.js"></script>
<script>
  document.querySelector(".siginForm").addEventListener("submit", function () {
    const registerBtn = document.querySelector(".loginBtn");
    registerBtn.disabled = true;
    registerBtn.innerHTML = "Processing...";
  });
</script>
