const axios = require("axios");
const dns = require("dns").promises;
const BASE_URL = process.env.CLOUDFLARE_API_BASE;
const ZONE_ID = process.env.CLOUDFLARE_ZONE_ID;
const API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;

const axiosInstance = axios.create({
  baseURL: `${BASE_URL}zones/${ZONE_ID}/dns_records`,
  headers: {
    Authorization: `Bearer ${API_TOKEN}`,
    "Content-Type": "application/json",
  },
});

const zoneAxios = axios.create({
  baseURL: `${BASE_URL}zones/${ZONE_ID}`,
  headers: {
    Authorization: `Bearer ${API_TOKEN}`,
    "Content-Type": "application/json",
  },
});

module.exports = {
  // Create subdomain record
  async createSubdomain({
    name,
    content,
    type = "CNAME",
    ttl = 3600,
    proxied = false,
  }) {
    const cnameRecord = await axiosInstance.post("", {
      type,
      name,
      content,
      ttl: Number(ttl),
      proxied: <PERSON><PERSON><PERSON>(proxied),
    });
    const AnameRecord = await axiosInstance.post("", {
      type: "A",
      name: content,
      content: "144.24.149.116",
      ttl: Number(ttl),
      proxied: Boolean(proxied),
    });
    return { cnameRecord: cnameRecord.data, AnameRecord: AnameRecord.data };
  },

  // Get all DNS records
  async listSubdomains() {
    const response = await axiosInstance.get("");
    return response.data;
  },

  // Get specific DNS record by ID
  async getSubdomain(recordId) {
    const response = await axiosInstance.get(`/${recordId}`);
    return response.data;
  },

  // Update specific DNS record
  async updateSubdomain(
    recordId,
    { name, content, type = "CNAME", ttl = 3600 }
  ) {
    const response = await axiosInstance.put(`/${recordId}`, {
      type,
      name,
      content,
      ttl,
      proxied: false,
    });
    return response.data;
  },

  // Delete DNS record
  async deleteSubdomain(recordId) {
    const response = await axiosInstance.delete(`/${recordId}`);
    return response.data;
  },

  verifyDomain: async (domain) => {
    try {
      const res = await axiosInstance.get("");
      console.log(res.data.result, "res");
      
      const cname = res.data.result.find(
        (record) => record.type === "CNAME" && record.name === domain
      );
      return { cnameFound: !!cname };
    } catch (err) {
      console.error(
        "Cloudflare DNS verification error:",
        err.response?.data || err.message
      );
      return {
        cnameFound: false,
      };
    }
  },

  // 3. Setup SSL
  setupSSL: async (domain) => {
    try {
      const res = await zoneAxios.post("/custom_hostnames",
        {
          hostname: domain,
          ssl: {
            method: "http", // Using HTTP-based Domain Validation
            type: "dv", // DV = Domain-Validated SSL
          },
        },
        
      );

      return {
        success: true,
        hostname: res.data.result.hostname,
        sslStatus: res.data.result.ssl.status,
        data: res.data.result,
      };
    } catch (err) {
      console.error("SSL setup failed:", err.response?.data || err.message);
      return {
        success: false,
        error: err.response?.data || err.message,
      };
    }
  },

  checkDomain: async (domain) => {
    // 1. Try Domainr API
    const domainrResult = await checkDomainAvailability(domain);
    if (domainrResult) return domainrResult;

    // 2. Fallback: DNS Resolution
    return await checkWithDNS(domain);
  },
};

async function checkWithDNS(domain) {
  try {
    await dns.lookup(domain);
    return {
      domain,
      source: "dns",
      isValid: true,
      isAvailable: false,
    };
  } catch (err) {
    return {
      domain,
      source: "dns",
      isValid: true,
      isAvailable: true,
    };
  }
}

async function checkDomainAvailability(domain) {
  const options = {
    method: "GET",
    url: `https://domaination.p.rapidapi.com/domains/${domain}`,
    headers: {
      "x-rapidapi-key": "**************************************************",
      "x-rapidapi-host": "domaination.p.rapidapi.com",
    },
  };

  try {
    const response = await axios.request(options);
    const { domain } = response.data;
    console.log(response, "response");
    return {
      name: domain.name,
      source: "domaination",
      isValid: true,
      isAvailable: domain.isAvailable,
    };
  } catch (error) {
    console.error(
      "Error checking domain:",
      error.response?.data || error.message
    );
    return null;
  }
}
