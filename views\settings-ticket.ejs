<%- contentFor('HeaderCss') %>
 <%- include("partials/title-meta", {"title":"Ticket Settings" }) %>

<!-- Select2 CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<!-- Dropzone CSS removed - using custom drag & drop -->
<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>
<style>
  body {
    background: #f8f9fa;
    color: #333;
  }
  .btn-primary {
    background: #5156be;
    border-color: #5156be;
  }
  .btn-primary:hover {
    background: #3f43a7;
    border-color: #3f43a7;
  }
  .table thead {
    background: #5156be;
    color: #fff;
  }
  .modal-header {
    background: #5156be;
    color: #fff;
  }
  .btn-close-white {
    filter: invert(100%);
  }
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  .badge-tag {
    background-color: #6c757d;
    color: #fff;
    font-size: 0.85rem;
    padding: 0.4em 0.75em;
    margin: 2px;
    border-radius: 12px;
    display: inline-block;
  }

  .comment-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 3px solid #5156be;
  }

  .comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .comment-user {
    font-weight: 600;
    color: #5156be;
    font-size: 14px;
  }

  .comment-date {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
  }

  .comment-message {
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
  }

  .no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
  }

  .comment-new {
    border-left-color: #28a745 !important;
    background: #f8fff9 !important;
    animation: fadeIn 0.3s ease-in;
  }
  #ticketTable thead th,
  #ticketTable tbody td {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  /* Optional: row height */
  #ticketTable tbody td {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  /* Optional: border around card */
  .custom-card-bordered {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
  }
   .column-narrow {
    text-align: center;
    vertical-align: middle;
    width: 10px;
    white-space: nowrap;
  }

  /* Optional: tighter padding */
  #ticketTable td.column-narrow,
  #ticketTable th.column-narrow {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Bulk Import Styles */
  #dropZone {
    transition: all 0.3s ease;
    cursor: pointer;
  }

  #dropZone:hover {
    background-color: #f8f9fa;
    border-color: #5156be !important;
  }

  #dropZone.border-success {
    border-color: #28a745 !important;
    background-color: #d4edda;
  }

  .progress {
    height: 8px;
  }

  .progress-bar {
    transition: width 0.3s ease;
  }

  .modal-lg {
    max-width: 800px;
  }

  .alert-info {
    border-left: 4px solid #5156be;
  }
</style>

<%- contentFor('body') %> 
<%- include("partials/page-title", { title: "Ticket Settings", pagetitle:"Ticket" }) %>
<div class="row align-items-center">
  <div class="col-md-6">
    <div class="mb-3">
      <h5 class="card-title">
        Total :<span class="text-muted fw-normal ms-2"
          >(<span id="totalTicketsCount">0</span>)</span
        >
      </h5>
    </div>
  </div>

  <div class="col-md-6">
    <div
      class="d-flex flex-wrap align-items-center justify-content-end gap-2 mb-3"
    >
      <div>
        <a href="/ticket-new/" class="btn btn-light" title="Add New Contact"
          ><i class="bx bx-plus me-1"></i>New</a
        >
      </div>
      <div>
        <a
          href="#"
          id="bulkImportBtn"
          class="btn btn-light"
          title="Bulk Import Contact"
          ><i class="bx bx-download me-1"></i>Bulk Import</a
        >
      </div>
      <div>
        <a
          href="#"
          id="export-button"
          title="Export all Contacts"
          class="btn btn-light"
          ><i class="bx bx-upload me-1"></i>Export</a
        >
      </div>

     
      
    </div>
  </div>
</div>
<!-- Header Navigation Ends -->

<hr class="py-1" />


<div class="container-fluid px-2 py-">
  <div class="card mb-4 shadow-sm custom-card-bordered">
    <div class="card-body d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Public Page Visibility</h5>
      <div class="form-check form-switch">
        <input
          class="form-check-input"
          type="checkbox"
          id="toggleAllVisibility"
        />
        <label class="form-check-label" for="toggleAllVisibility">Show All</label>
      </div>
    </div>
  </div>

  <div class="card shadow-sm custom-card-bordered">
    <div class="card-body">
      <div class="table-responsive">
        <table
          id="ticketTable"
          class="table table-bordered table-hover align-middle w-100 mb-0"
        >
          <thead class="table-light">
            <tr>
              <th>Title</th>
              <th>Description</th>
               <th class="column-narrow">Status</th>
    <th class="column-narrow">Priority</th>
    <th class="column-narrow">Type</th>
              <th>Created</th>
              <th>Assignee</th>
              <th>Attachments</th>
              <th width="80">Visible</th>
              <th width="100">Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editTicketModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form id="editTicketForm" enctype="multipart/form-data">
        <div class="modal-header">
          <h5 style="color: #ffffff;" class="modal-title">Edit Ticket</h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
          ></button>
        </div>
        <div class="modal-body">
          <input type="hidden" id="editTicketId" />
          <div class="mb-3">
            <label>Title</label>
            <input
              type="text"
              id="editTitle"
              class="form-control"
              required
            />
          </div>
          <div class="mb-3">
            <label>Description</label>
            <textarea
              id="editDescription"
              class="form-control"
              rows="4"
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Type</label>
            <select id="editType" class="form-select" required>
              <option value="">Choose One</option>
              <option value="BUG">Bug</option>
              <option value="FEATURE REQUEST">Feature Request</option>
              <option value="SUPPORT">Support</option>
              <option value="TASK">Task</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Status</label>
            <select id="editStatus" class="form-select">
              <option value="">Choose One</option>
              <option value="OPEN">Open</option>
              <option value="IN PROGRESS">In Progress</option>
              <option value="RESOLVED">Resolved</option>
              <option value="CLOSED">Closed</option>
            </select>
          </div>
          <div class="mb-3" id="resolutionNoteDiv" style="display: none;">
            <label>Resolution Note</label>
            <textarea
              id="editResolutionNote"
              class="form-control"
              rows="3"
              placeholder="Enter resolution details..."
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Priority</label>
            <select id="editPriority" class="form-select">
              <option value="">Choose One</option>
              <option value="LOW">Low</option>
              <option value="MEDIUM">Medium</option>
              <option value="HIGH">High</option>
              <option value="URGENT">Urgent</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Assignee</label>
            <select id="editAssignee" class="form-control">
              <option value="">Select Assignee</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Due Date</label>
            <input type="date" id="editDueDate" class="form-control" />
          </div>

          <div class="mb-3">
            <label>Tags</label>
            <div id="existingTags" class="mb-2"></div>
            <div class="input-group mb-3">
              <input
                type="text"
                id="newTag"
                class="form-control"
                placeholder="Type and press Enter to add tags..."
              />
              
            </div>
          </div>

          <div class="mb-3">
            <label>Attachments</label>
            <div id="existingAttachments" class="mb-2"></div>
            <input
              type="file"
              id="newAttachments"
              class="form-control"
              multiple
            />
          </div>

          <!-- Comments Section -->
          <div class="mb-3">
            <label>Comments</label>
            <div id="existingComments" class="mb-3" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px;">
              <!-- Existing comments will be displayed here -->
            </div>
            <div class="input-group">
              <textarea
                id="newComment"
                class="form-control"
                rows="2"
                placeholder="Add a comment..."
              ></textarea>
              <button type="button" class="btn btn-outline-primary" id="addCommentBtn">
                <i class="bx bx-send"></i> Add
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">
            <span
              class="spinner-border spinner-border-sm d-none"
              id="editSpinner"
            ></span>
            Save Changes
          </button>
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 style="color: #ffffff;" class="modal-title">Confirm Deletion</h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this ticket?
        <input type="hidden" id="deleteTicketId" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span
            class="spinner-border spinner-border-sm d-none"
            id="deleteSpinner"
          ></span>
          Delete
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 style="color: #ffffff;" class="modal-title">
          <i class="fas fa-upload me-2"></i>
          Bulk Import Tickets from CSV
        </h5>
        <button style="color: #ffffff !important;" type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <!-- Instructions -->
        <div class="alert alert-info">
          <h6 class="alert-heading">
            <i class="fas fa-info-circle me-2"></i>
            CSV Format Requirements
          </h6>
          <p class="mb-2">Your CSV file must include the following columns:</p>
          <ul class="mb-2">
            <li><strong>title</strong> - Ticket title (required)</li>
            <li><strong>description</strong> - Ticket description (optional)</li>
            <li><strong>priority</strong> - Priority level: LOW, MEDIUM, HIGH, URGENT (optional)</li>
            <li><strong>type</strong> - Ticket type: BUG, FEATURE REQUEST, SUPPORT, TASK (optional)</li>
            <li><strong>tags</strong> - Comma-separated tags (optional)</li>
          </ul>
          <p class="mb-0">
            <small class="text-muted">
              <i class="fas fa-exclamation-triangle me-1"></i>
              Priority and Type values are case-insensitive but must match the allowed values above.
            </small>
          </p>
        </div>

        <!-- Sample CSV Download -->
        <div class="mb-4">
          <h6>Sample CSV Template</h6>
          <button type="button" class="btn btn-outline-secondary btn-sm" id="downloadSampleBtn">
            <i class="fas fa-download me-1"></i>
            Download Sample CSV
          </button>
        </div>

        <!-- File Upload -->
        <div class="mb-4">
          <h6>Upload CSV File</h6>
          <div class="border-2 border-dashed border-primary rounded p-4 text-center" id="dropZone">
            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
            <p class="mb-2">Drag and drop your CSV file here, or click to browse</p>
            <input type="file" id="csvFileInput" accept=".csv" class="d-none">
            <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFileInput').click()">
              Choose File
            </button>
          </div>
          <div id="fileInfo" class="mt-3 d-none">
            <div class="alert alert-success">
              <i class="fas fa-check-circle me-2"></i>
              <span id="fileName"></span>
              <button type="button" class="btn btn-sm btn-outline-danger ms-2" id="removeFileBtn">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Progress Bar -->
        <div id="uploadProgress" class="mb-3 d-none">
          <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
          </div>
          <small class="text-muted mt-1 d-block" id="progressText">Uploading...</small>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="importBtn" disabled>
          <span id="importSpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
          <i class="fas fa-upload me-1"></i>
          Import Tickets
        </button>
      </div>
    </div>
  </div>
</div>

<%- contentFor('FooterJs') %>

<!-- Select2 JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<!-- DataTables JS -->
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>


<script>
  const baseUrl = "/api/tickets";
  let currentTags = [],
    currentAttachments = [],
    comments = [],
    newComments = [];
   let table;
  let toggleButtonState = undefined;
   // Removed unused CSV variables - using new bulk import modal


  $(document).ready(function () {
    initializeDataTable();
    initializeForm();
    loadUsers();
    initializeBulkImport();

    $("#toggleAllVisibility").on("change", function () {
      if (toggleButtonState === undefined || !toggleButtonState) {
        showToast("Please create a ticket first", "primary");
        return;
      }
      const visible = $(this).is(":checked");
      $.ajax({
        url: `${baseUrl}/toggle/visibility`,
        type: "PUT",
        contentType: "application/json",
        data: JSON.stringify({ visible }),
        success: () => {
          reloadTable();
          showToast("Visibility toggled successfully", "success");
        },
        error: () => showToast("Error toggling all visibility"),
      });
    });

    // Show/hide resolution note based on status
    $("#editStatus").on("change", function() {
      const status = $(this).val();
      if (status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
        $("#editResolutionNote").val("");
      }
    });

    // Add comment functionality
    $("#addCommentBtn").on("click", function() {
      addComment();
    });

    // Add comment on Enter key (Ctrl+Enter for new line)
    $("#newComment").on("keypress", function(e) {
      if (e.which === 13 && !e.ctrlKey) {
        e.preventDefault();
        addComment();
      }
    });
  });

  // Initialize form components
  function initializeForm() {
    // Initialize Select2 for assignee dropdown
    $('#editAssignee').select2({
      placeholder: 'Select a user to assign',
      allowClear: false,
      width: '100%',
      dropdownParent: $('#editTicketModal')
    });
  }

  $(document).ready(function() {
    $("#newTag").on("keypress", function (e) {
      if (e.which === 13) {
        e.preventDefault();
         const tag = $("#newTag").val().trim();
      if (tag && !currentTags.includes(tag)) {
        currentTags.push(tag);
         $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
        $("#newTag").val("");
      }
        $(this).val("");
      }
    });

   
    $("#existingTags").on("click", ".remove-tag", function (e) {
      e.preventDefault();
      const t = $(this).data("tag");
      currentTags = currentTags.filter((x) => x !== t);
      $(this).parent().remove();
    });
    $("#editTicketForm").submit(function (e) {
      e.preventDefault();
      const id = $("#editTicketId").val();
      const fd = new FormData();
      fd.append("title", $("#editTitle").val());
      fd.append("description", $("#editDescription").val());
      fd.append("type", $("#editType").val());
      fd.append("status", $("#editStatus").val());
      fd.append("priority", $("#editPriority").val());
      fd.append("assignee", $("#editAssignee").val());
      fd.append("dueDate", $("#editDueDate").val());
      fd.append("resolutionNote", $("#editResolutionNote").val());
      fd.append("tags", JSON.stringify(currentTags));

      // Add pending comments to form data
      if (newComments.length > 0) {
        fd.append("newComments", JSON.stringify(newComments));
      }
      $("#newAttachments")[0].files.length &&
        $.each($("#newAttachments")[0].files, (i, f) =>
          fd.append("newAttachments", f)
        );
      fd.append("existingAttachments", JSON.stringify(currentAttachments));

      $("#editSpinner").removeClass("d-none");
      $('#editTicketForm button[type="submit"]').attr("disabled", true);

      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "PUT",
        processData: false,
        contentType: false,
        data: fd,
        success: () => {
          newComments = [];
          comments = [];
          $("#editTicketModal").modal("hide");
          reloadTable();
          showToast("Ticket updated successfully", "success");
        },
        error: () => alert("Error updating ticket"),
        complete: () => {
          $("#editSpinner").addClass("d-none");
          $('#editTicketForm button[type="submit"]').attr("disabled", false);
        },
      });
    });

    $("#confirmDeleteBtn").click(() => {
      const id = $("#deleteTicketId").val();
      $("#deleteSpinner").removeClass("d-none");
      $("#confirmDeleteBtn").attr("disabled", true);
      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "DELETE",
        success: () => {
          $("#deleteConfirmModal").modal("hide");
          showToast("Ticket deleted successfully", "success");
          reloadTable();
        },
        error: () => {
          showToast("Error deleting ticket", "danger");
        },
        complete: () => {
          $("#deleteSpinner").addClass("d-none");
          $("#confirmDeleteBtn").attr("disabled", false);
        },
      });
    });
  });


  // Initialize DataTable
  function initializeDataTable() {
    // Show loading indicator
    // showTableLoading(true);

    table = $("#ticketTable").DataTable({
      responsive: true,
      searching: true,
      ordering: true,
      pageLength: 10,
      autoWidth: false,
      lengthMenu: [
        [10, 25, 50, 100],
        [10, 25, 50, 100],
      ],
      order: [[1, "desc"]],
      columnDefs: [
        { orderable: false, targets: [-1] }, // Disable ordering for checkbox and actions columns
      ],
      language: {
        processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>',
        emptyTable: '<div class="text-center py-4"><i class="fas fa-inbox fa-3x text-muted mb-3"></i><h5 class="text-muted">No tickets found</h5><p class="text-muted">Create your first ticket to get started.</p></div>',
      
      },
      ajax: {
        url: baseUrl,
        method: "GET",
        dataSrc: function (json) {
          console.log('Tickets loaded:', json);
          $("#totalTicketsCount").html(json.tickets.length);
          toggleButtonState = json.tickets.length > 0;
          return json.tickets;
        },
       
      },
      columns: [
        // {
        //   data: null,
        //   render: function (data, type, row) {
        //     return `
        //       <div class="form-check">
        //         <input class="form-check-input ticket-checkbox" type="checkbox" value="${row._id}" id="ticket_${row._id}">
        //         <label class="form-check-label" for="ticket_${row._id}"></label>
        //       </div>
        //     `;
        //   },
        //   orderable: false,
        //   searchable: false,
        //   width: "50px"
        // },
        {
          data: "title",
          render: function(data, type, row) {
            return `<span class="fw-bold text-primary">${data}</span>`;
          }
        },
        {
          data: "description",
          render: function(data) {
            if (!data) return '<span class="text-muted">No description</span>';
            return data.length > 50 ? data.substring(0, 50) + '...' : data;
          }
        },
        {
          data: "status",
          render: function(data) {
            const statusClass = getStatusBadgeClass(data);
            return `<span class="badge ${statusClass}">${data || 'Open'}</span>`;
          }
        },
        {
          data: "priority",
          render: function(data) {
            if (!data) return '<span class="text-muted">-</span>';
            const priorityClass = getPriorityBadgeClass(data);
            return `<span class="badge ${priorityClass}">${data}</span>`;
          }
        },
        {
          data: "type",
          render: function(data) {
            if (!data) return '<span class="text-muted">-</span>';
            const typeClass = getTypeBadgeClass(data);
            return `<span class="badge ${typeClass}">${data}</span>`;
          }
        },
        {
          data: "createdDate",
          render: function(data) {
            return `<small class="text-muted">${new Date(data).toLocaleDateString()}</small>`;
          }
        },
        {
          data: "assignee",
          render: function(data) {
            if (!data) return '<span class="text-muted">Unassigned</span>';
            return `<small>${data.firstName} ${data.lastName}</small>`;
          }
        },
        {
          data: "attachments",
          render: function(data) {
            if (!data || data.length === 0) return '<div class="">0</div>';
            return `<div class="text-center">${data.length}</div>`;
          }
        },
        {
          data: "showOnPublicPage",
          render: function(data, type, row) {
            
            const checked = data ? "checked" : "";
            return `
              <div class="form-check form-switch">
                <input class="form-check-input visibility-toggle" type="checkbox" ${checked} data-id="${row._id}">
              </div>
            `;
          },
          orderable: false,
          width: "80px"
        },
        {
          data: null,
          render: function(data, type, row) {
            return `
              <div class="btn-group gap-2" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="openEditModal('${row._id}')" title="Edit">
                  <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteTicket('${row._id}')" title="Delete">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            `;
          },
          orderable: false,
          searchable: false,
          width: "100px"
        }
      ],
      drawCallback: function() {
        // Re-bind event handlers after table redraw
        bindTableEventHandlers();
      }
    });
  }

  // Reload DataTable
  function reloadTable() {
    if (table) {
      table.ajax.reload(null, false); // false = keep current page
    }
  }

  // Show/hide table loading indicator
  function showTableLoading(show) {
    if (show) {
      if (!$("#tableLoadingOverlay").length) {
        $("#ticketTable_wrapper").prepend(`
          <div id="tableLoadingOverlay" class="position-absolute w-100 h-100 d-flex justify-content-center align-items-center" style="background: rgba(255,255,255,0.8); z-index: 1000;">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 text-muted">Loading tickets...</p>
            </div>
          </div>
        `);
      }
      $("#tableLoadingOverlay").show();
    } else {
      $("#tableLoadingOverlay").hide();
    }
  }

  // Bind event handlers after table redraw
  function bindTableEventHandlers() {
    // Bind visibility toggle events
    $(".visibility-toggle").off("change").on("change", function () {
      const id = $(this).data("id");
      const $toggle = $(this);

      $toggle.prop('disabled', true);

      $.ajax({
        url: `${baseUrl}/${id}/toggle/visibility`,
        type: "PUT",
        success: () => {
          showToast("Visibility toggled successfully", "success");
        },
        error: () => {
          // Revert toggle state on error
          $toggle.prop('checked', !$toggle.prop('checked'));
          showToast("Failed to toggle visibility", "danger");
        },
        complete: () => {
          $toggle.prop('disabled', false);
        }
      });
    });

    // Select all functionality
    $("#selectAll").off("change").on("change", function() {
      const isChecked = $(this).prop('checked');
      $(".ticket-checkbox").prop('checked', isChecked);
    });

    // Individual checkbox change
    $(".ticket-checkbox").off("change").on("change", function() {
      const totalCheckboxes = $(".ticket-checkbox").length;
      const checkedCheckboxes = $(".ticket-checkbox:checked").length;

      if (checkedCheckboxes === totalCheckboxes) {
        $("#selectAll").prop('checked', true).prop('indeterminate', false);
      } else if (checkedCheckboxes === 0) {
        $("#selectAll").prop('checked', false).prop('indeterminate', false);
      } else {
        $("#selectAll").prop('checked', false).prop('indeterminate', true);
      }
    });
  }
  function getStatusBadgeClass(status) {
    switch (status) {
      case "OPEN":
        return "bg-primary";
      case "IN PROGRESS":
        return "bg-warning";
      case "RESOLVED":
        return "bg-success";
      case "CLOSED":
        return "bg-dark";
      default:
        return "bg-secondary";
    }
  }

  function getPriorityBadgeClass(priority) {
    switch (priority) {
      case "LOW":
        return "bg-info";
      case "MEDIUM":
        return "bg-primary";
      case "HIGH":
        return "bg-warning text-dark";
      case "URGENT":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }

  function getTypeBadgeClass(type) {
    switch (type) {
      case "BUG":
        return "bg-danger";
      case "FEATURE REQUEST":
        return "bg-success";
      case "SUPPORT":
        return "bg-info";
      case "TASK":
        return "bg-warning text-dark";
      default:
        return "bg-secondary";
    }
  }

  // Load users for assignment dropdown
  function loadUsers() {
    $.ajax({
      url: "/api/users",
      method: "GET",
      success: function(response) {
        console.log('Users loaded:', response);
        populateAssignToDropdown(response.data || response);
      },
      error: function(xhr) {
        console.error('Error loading users:', xhr);
        showToast('Error loading users for assignment', 'danger');
      }
    });
  }

  // Populate assignee dropdown
  function populateAssignToDropdown(users) {
    const $assignee = $('#editAssignee');
    $assignee.empty();
    $assignee.append('<option value="">Select a user to assign</option>');

    if (users && users.length > 0) {
      users.forEach(user => {
        const displayName = `${user.firstName} ${user.lastName} (${user.email})`;
        $assignee.append(`<option value="${user._id}">${displayName}</option>`);
      });
    }
  }

  
  function addComment() {
    const commentText = $("#newComment").val().trim();
    if (!commentText) {
      showToast("Please enter a comment", "warning");
      return;
    }

    
    const newComment = {
      message: commentText,
      user: { firstName: "You", lastName: "" },
      createdAt: new Date(),
      isNew: true 
    };

   

    
    newComments.push(newComment);
    comments=[...comments,...newComments]
    displayComments();

    // Clear input and show success
    $("#newComment").val("");
    showToast("Comment added (will be saved when you update the ticket)", "success");
  }

// Display comments in the modal
  function displayComments() {
    console.log('Comments to display:', comments);
    const $commentsContainer = $("#existingComments");
    $commentsContainer.empty();

    if (!comments || comments.length === 0) {
      $commentsContainer.html('<div class="no-comments">No comments yet</div>');
      return;
    }

    comments.forEach(comment => {
      const commentDate = new Date(comment.createdAt).toLocaleString();
      const isNewComment = comment.isNew ? ' comment-new' : '';
      const newBadge = comment.isNew ? '<span class="badge bg-success ms-2">New</span>' : '';

     
      let userName = "Unknown User";
      if (comment.user) {
        if (comment.user.firstName === "You") {
          userName = "You";
        } else {
          userName = `${comment.user.firstName} ${comment.user.lastName}`.trim();
        }
      }

      const commentHtml = `
        <div class="comment-item${isNewComment}">
          <div class="comment-header">
            <span class="comment-user">${userName}${newBadge}</span>
            <span class="comment-date">${commentDate}</span>
          </div>
          <p class="comment-message">${comment.message}</p>
        </div>
      `;
      $commentsContainer.append(commentHtml);
    });

    // Scroll to bottom to show latest comments
    $commentsContainer.scrollTop($commentsContainer[0].scrollHeight);
  }

  function openEditModal(id) {
    $.get(`${baseUrl}/${id}`, (t) => {
      $("#editTicketId").val(t._id);
      $("#editTitle").val(t.title);
      $("#editDescription").val(t.description || "");
      $("#editType").val(t.type || "");
      $("#editStatus").val(t.status || "");
      $("#editPriority").val(t.priority || "");

      // Set assignee value for Select2
      const assigneeId = t.assignee?._id || "";
      $("#editAssignee").val(assigneeId).trigger('change');

      $("#editDueDate").val(t.dueDate ? new Date(t.dueDate).toISOString().split('T')[0] : "");
      $("#editResolutionNote").val(t.resolutionNote || "");

      // Show/hide resolution note based on status
      if (t.status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
      }

      currentTags = [...(t.tags || [])];
      $("#existingTags").empty();
      currentTags.forEach((tag) => {
        $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
      });

      currentAttachments = [...(t.attachments || [])];
      $("#existingAttachments").empty();
      currentAttachments.forEach((att) =>
        $("#existingAttachments").append(
          `<div><a href="${att}" target="_blank">${att}</a></div>`
        )
      );
      $("#newAttachments").val("");

      comments = t.comments || [];
 
      displayComments();

      
      $("#newComment").val("");

      $("#editTicketModal").modal("show");
    });
  }

  // Delete ticket
  function deleteTicket(id) {
    $("#deleteTicketId").val(id);
    $("#deleteConfirmModal").modal("show");
    
   }

  $("#confirmDeleteBtn").on("click", function () {
    const id = $("#deleteTicketId").val();
    $("#deleteSpinner").removeClass("d-none");
    $("#confirmDeleteBtn").attr("disabled", true);
    $.ajax({
      url: `${baseUrl}/${id}`,
      type: "DELETE",
      success: () => {
        $("#deleteConfirmModal").modal("hide");
        showToast("Ticket deleted successfully", "success");
        table.ajax.reload(null, false); // false = keep current page
      },
      error: () => {
        showToast("Error deleting ticket", "danger");
      },
      complete: () => {
        $("#deleteSpinner").addClass("d-none");
        $("#confirmDeleteBtn").attr("disabled", false);
      },
    });
  });

  
  $("#export-button").on("click", function () {
    window.location.href = "/api/tickets/export/all";
  });

  






  // Initialize bulk import functionality
  function initializeBulkImport() {
    let selectedFile = null;
    let uploadedFileUrl = null;

    // Bulk import button click
    $("#bulkImportBtn").on("click", function() {
      $("#bulkImportModal").modal("show");
    });

    // Download sample CSV
    $("#downloadSampleBtn").on("click", function() {
      downloadSampleCSV();
    });

    // File input change
    $("#csvFileInput").on("change", function(e) {
      const file = e.target.files[0];
      if (file) {
        handleFileSelection(file);
      }
    });

    // Remove file button
    $("#removeFileBtn").on("click", function() {
      clearFileSelection();
    });

    // Drag and drop functionality
    const dropZone = document.getElementById('dropZone');

    dropZone.addEventListener('dragover', function(e) {
      e.preventDefault();
      dropZone.classList.add('border-success');
    });

    dropZone.addEventListener('dragleave', function(e) {
      e.preventDefault();
      dropZone.classList.remove('border-success');
    });

    dropZone.addEventListener('drop', function(e) {
      e.preventDefault();
      dropZone.classList.remove('border-success');

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        const file = files[0];
        if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
          handleFileSelection(file);
        } else {
          showToast('Please select a CSV file', 'warning');
        }
      }
    });

    // Import button click
    $("#importBtn").on("click", function() {
      if (uploadedFileUrl) {
        importTicketsFromCSV();
      }
    });

    // Handle file selection
    function handleFileSelection(file) {
      if (!file.name.endsWith('.csv')) {
        showToast('Please select a CSV file', 'warning');
        return;
      }

      selectedFile = file;
      $("#fileName").text(file.name);
      $("#fileInfo").removeClass("d-none");

      // Upload file immediately
      uploadCSVFile(file);
    }

    // Clear file selection
    function clearFileSelection() {
      selectedFile = null;
      uploadedFileUrl = null;
      $("#csvFileInput").val('');
      $("#fileInfo").addClass("d-none");
      $("#importBtn").prop('disabled', true);
      $("#uploadProgress").addClass("d-none");
    }

    // Upload CSV file
    function uploadCSVFile(file) {
      const formData = new FormData();
      formData.append('file', file);

      $("#uploadProgress").removeClass("d-none");
      $("#progressText").text("Uploading file...");

      $.ajax({
        url: '/api/tickets/import/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
          const xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener("progress", function(evt) {
            if (evt.lengthComputable) {
              const percentComplete = (evt.loaded / evt.total) * 100;
              $(".progress-bar").css("width", percentComplete + "%");
            }
          }, false);
          return xhr;
        },
        success: function(response) {
          if (response.success) {
            uploadedFileUrl = response.file.url;
            $("#importBtn").prop('disabled', false);
            $("#uploadProgress").addClass("d-none");
            showToast('File uploaded successfully', 'success');
          } else {
            showToast('Upload failed: ' + response.message, 'error');
            clearFileSelection();
          }
        },
        error: function(xhr) {
          console.error('Upload error:', xhr);
          const errorMessage = xhr.responseJSON?.error || 'Upload failed';
          showToast(errorMessage, 'error');
          clearFileSelection();
        }
      });
    }

    // Import tickets from CSV
    function importTicketsFromCSV() {
      if (!uploadedFileUrl) {
        showToast('Please upload a CSV file first', 'warning');
        return;
      }

      $("#importSpinner").removeClass("d-none");
      $("#importBtn").prop('disabled', true);

      $.ajax({
        url: '/api/tickets/import/all',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
          csvFileUrl: uploadedFileUrl
        }),
        success: function(response) {
          $("#importSpinner").addClass("d-none");
          $("#bulkImportModal").modal("hide");

          if (response.success) {
            showToast(`Successfully imported ${response.totalImported} tickets`, 'success');
            reloadTable();
            clearFileSelection();
          } else {
            showToast('Import failed: ' + response.message, 'error');
          }
        },
        error: function(xhr) {
          $("#importSpinner").addClass("d-none");
          $("#importBtn").prop('disabled', false);

          console.error('Import error:', xhr);
          const errorMessage = xhr.responseJSON?.message || 'Import failed';
          showToast(errorMessage, 'error');
        }
      });
    }

    // Download sample CSV
    function downloadSampleCSV() {
      const sampleData = [
        ["title", "description", "priority", "type", "tags"],
        ["Sample Bug Report", "This is a sample bug description", "HIGH", "BUG", "bug,urgent,frontend"],
        ["Feature Request Example", "This is a sample feature request", "MEDIUM", "FEATURE REQUEST", "enhancement,ui"],
        ["Support Ticket Sample", "This is a sample support ticket", "LOW", "SUPPORT", "help,question"]
      ];

      // Convert array to CSV string
      const csvContent = sampleData.map(row =>
        row.map(field => `"${field.replace(/"/g, '""')}"`).join(',')
      ).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sample_tickets.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }
  }
</script>
