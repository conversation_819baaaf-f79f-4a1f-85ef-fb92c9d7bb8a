<%- contentFor('HeaderCss') %> <%- include("partials/title-meta", {"title":"Ticket Settings" }) %>
<style>
  body {
    background: #f8f9fa;
    color: #333;
  }
  .btn-primary {
    background: #5156be;
    border-color: #5156be;
  }
  .btn-primary:hover {
    background: #3f43a7;
    border-color: #3f43a7;
  }
  .table thead {
    background: #5156be;
    color: #fff;
  }
  .modal-header {
    background: #5156be;
    color: #fff;
  }
  .btn-close-white {
    filter: invert(100%);
  }
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  .badge-tag {
    background-color: #6c757d;
    color: #fff;
    font-size: 0.85rem;
    padding: 0.4em 0.75em;
    margin: 2px;
    border-radius: 12px;
    display: inline-block;
  }

  .comment-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 3px solid #5156be;
  }

  .comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .comment-user {
    font-weight: 600;
    color: #5156be;
    font-size: 14px;
  }

  .comment-date {
    font-size: 12px;
    color: #6c757d;
  }

  .comment-message {
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
  }

  .no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
  }


</style>

<%- contentFor('body') %> <%- include("partials/page-title", { title: "Ticket Settings", pagetitle:"Ticket" }) %>

<div class="container mt-4">
  <div class="card mb-4 shadow-sm">
    <div class="card-body d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Public Page Visibility</h5>
      <div class="form-check form-switch">
        <input
          class="form-check-input"
          type="checkbox"
          id="toggleAllVisibility"
        />
        <label class="form-check-label" for="toggleAllVisibility"
          >Show All</label
        >
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Tickets</h5>
      <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle">
          <thead>
            <tr>
              <th>Title</th>
              <th>Type</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Assignee</th>
              <th>Due Date</th>
              <th>Tags</th>
              <th>Attachments</th>
              <th>Visible</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="ticketList"></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editTicketModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form id="editTicketForm" enctype="multipart/form-data">
        <div class="modal-header">
          <h5 style="color: #ffffff;" class="modal-title">Edit Ticket</h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
          ></button>
        </div>
        <div class="modal-body">
          <input type="hidden" id="editTicketId" />
          <div class="mb-3">
            <label>Title</label>
            <input
              type="text"
              id="editTitle"
              class="form-control"
              required
            />
          </div>
          <div class="mb-3">
            <label>Description</label>
            <textarea
              id="editDescription"
              class="form-control"
              rows="4"
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Type</label>
            <select id="editType" class="form-select" required>
              <option value="">Choose One</option>
              <option value="Bug">Bug</option>
              <option value="Feature Request">Feature Request</option>
              <option value="Support">Support</option>
              <option value="Task">Task</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Status</label>
            <select id="editStatus" class="form-select">
              <option value="">Choose One</option>
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
            </select>
          </div>
          <div class="mb-3" id="resolutionNoteDiv" style="display: none;">
            <label>Resolution Note</label>
            <textarea
              id="editResolutionNote"
              class="form-control"
              rows="3"
              placeholder="Enter resolution details..."
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Priority</label>
            <select id="editPriority" class="form-select">
              <option value="">Choose One</option>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
              <option value="Urgent">Urgent</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Assignee</label>
            <select id="editAssignee" class="form-select">
              <option value="">Select Assignee</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Due Date</label>
            <input type="date" id="editDueDate" class="form-control" />
          </div>

          <div class="mb-3">
            <label>Tags</label>
            <div id="existingTags" class="mb-2"></div>
            <div class="input-group mb-3">
              <input
                type="text"
                id="newTag"
                class="form-control"
                placeholder="Type and press Enter to add tags..."
              />
              
            </div>
          </div>

          <div class="mb-3">
            <label>Attachments</label>
            <div id="existingAttachments" class="mb-2"></div>
            <input
              type="file"
              id="newAttachments"
              class="form-control"
              multiple
            />
          </div>

          <!-- Comments Section -->
          <div class="mb-3">
            <label>Comments</label>
            <div id="existingComments" class="mb-3" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px;">
              <!-- Existing comments will be displayed here -->
            </div>
            <div class="input-group">
              <textarea
                id="newComment"
                class="form-control"
                rows="2"
                placeholder="Add a comment..."
              ></textarea>
              <button type="button" class="btn btn-outline-primary" id="addCommentBtn">
                <i class="bx bx-send"></i> Add
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">
            <span
              class="spinner-border spinner-border-sm d-none"
              id="editSpinner"
            ></span>
            Save Changes
          </button>
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 style="color: #ffffff;" class="modal-title">Confirm Deletion</h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this ticket?
        <input type="hidden" id="deleteTicketId" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span
            class="spinner-border spinner-border-sm d-none"
            id="deleteSpinner"
          ></span>
          Delete
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
<%- contentFor('FooterJs') %>

<script>
  const baseUrl = "/api/tickets";
  let currentTags = [],
    currentAttachments = [];
  let toggleButtonState = undefined;

  $(document).ready(function () {
    fetchTickets();
    loadUsers();

    $("#toggleAllVisibility").on("change", function () {
      if (toggleButtonState === undefined) {
        showToast("Please create a ticket first", "primary");
        return;
      }
      const visible = $(this).is(":checked");
      $.ajax({
        url: `${baseUrl}/toggle/visibility`,
        type: "PUT",
        contentType: "application/json",
        data: JSON.stringify({ visible }),
        success: () => fetchTickets(),
        error: () => showToast("Error toggling all visibility"),
      });
    });

    // Show/hide resolution note based on status
    $("#editStatus").on("change", function() {
      const status = $(this).val();
      if (status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
        $("#editResolutionNote").val("");
      }
    });

    // Add comment functionality
    $("#addCommentBtn").on("click", function() {
      addComment();
    });

    // Add comment on Enter key (Ctrl+Enter for new line)
    $("#newComment").on("keypress", function(e) {
      if (e.which === 13 && !e.ctrlKey) {
        e.preventDefault();
        addComment();
      }
    });



    $("#newTag").on("keypress", function (e) {
      if (e.which === 13) {
        e.preventDefault();
         const tag = $("#newTag").val().trim();
      if (tag && !currentTags.includes(tag)) {
        currentTags.push(tag);
         $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
        $("#newTag").val("");
      }
        $(this).val("");
      }
    });

   
    $("#existingTags").on("click", ".remove-tag", function (e) {
      e.preventDefault();
      const t = $(this).data("tag");
      currentTags = currentTags.filter((x) => x !== t);
      $(this).parent().remove();
    });
    $("#editTicketForm").submit(function (e) {
      e.preventDefault();
      const id = $("#editTicketId").val();
      const fd = new FormData();
      fd.append("title", $("#editTitle").val());
      fd.append("description", $("#editDescription").val());
      fd.append("type", $("#editType").val());
      fd.append("status", $("#editStatus").val());
      fd.append("priority", $("#editPriority").val());
      fd.append("assignee", $("#editAssignee").val());
      fd.append("dueDate", $("#editDueDate").val());
      fd.append("resolutionNote", $("#editResolutionNote").val());
      fd.append("tags", JSON.stringify(currentTags));
      $("#newAttachments")[0].files.length &&
        $.each($("#newAttachments")[0].files, (i, f) =>
          fd.append("newAttachments", f)
        );
      fd.append("existingAttachments", JSON.stringify(currentAttachments));

      $("#editSpinner").removeClass("d-none");
      $('#editTicketForm button[type="submit"]').attr("disabled", true);

      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "PUT",
        processData: false,
        contentType: false,
        data: fd,
        success: () => {
          $("#editTicketModal").modal("hide");
          fetchTickets();
        },
        error: () => alert("Error updating ticket"),
        complete: () => {
          $("#editSpinner").addClass("d-none");
          $('#editTicketForm button[type="submit"]').attr("disabled", false);
        },
      });
    });

    $("#confirmDeleteBtn").click(() => {
      const id = $("#deleteTicketId").val();
      $("#deleteSpinner").removeClass("d-none");
      $("#confirmDeleteBtn").attr("disabled", true);
      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "DELETE",
        success: () => {
          $("#deleteConfirmModal").modal("hide");
          fetchTickets();
        },
        error: () => alert("Error deleting"),
        complete: () => {
          $("#deleteSpinner").addClass("d-none");
          $("#confirmDeleteBtn").attr("disabled", false);
        },
      });
    });
  });

  function fetchTickets() {
    $.get(baseUrl, function (tickets) {
      const $tbody = $("#ticketList").empty();
      if (tickets.length === 0) {
        tbody.append(`
          <tr>
            <td colspan="6">No tickets found</td>
          </tr>
        `);
        return;
      }
      toggleButtonState = true;
      tickets.forEach((t) => {
        const tags = t.tags?.length
          ? t.tags
              .map((tag) => `<span class="badge badge-tag">${tag}</span>`)
              .join(" ")
          : "N/A";
        const atts = t.attachments?.length ? t.attachments?.length : 0;
        const statusClass = getStatusBadgeClass(t.status);
        const priorityClass = getPriorityBadgeClass(t.priority);

      const assigneeName = t.assignee ? `${t.assignee.firstName} ${t.assignee.lastName}` : "Unassigned";
      const dueDate = t.dueDate ? new Date(t.dueDate).toLocaleDateString() : "N/A";
      const typeClass = getTypeBadgeClass(t.type);

      $tbody.append(`
  <tr>
    <td>${t.title}</td>
    <td><span class="badge ${typeClass}">${t.type || "N/A"}</span></td>
    <td><span class="badge ${statusClass}">${t.status || "Open"}</span></td>
    <td><span class="badge ${priorityClass}">${t.priority || "Medium"}</span></td>
    <td>${assigneeName}</td>
    <td>${dueDate}</td>
    <td>${tags}</td>
    <td style="text-align: center;">${atts}</td>
    <td>
      <div class="form-check form-switch">
        <input class="form-check-input toggle-single" type="checkbox" data-id="${t._id}" ${t.showOnPublicPage ? "checked" : ""}>
      </div>
    </td>
    <td style="border: none;">
      <div class="d-flex gap-2">
      <button class="btn btn-sm btn-primary" onclick="openEditModal('${t._id}')">Edit</button>
      <button class="btn btn-sm btn-danger" onclick="openDeleteModal('${t._id}')">Delete</button>
    </td>
  </tr>
`);

      });

      $(".toggle-single").on("change", function () {
        const id = $(this).data("id");
        $.ajax({
          url: `${baseUrl}/${id}/toggle/visibility`,
          type: "PUT",
          success: fetchTickets,
          error: () => showToast("Failed to toggle visibility"),
        });
      });
    });
  }
  function getStatusBadgeClass(status) {
    switch (status) {
      case "Open":
        return "bg-primary";
      case "In Progress":
        return "bg-warning";
      case "Resolved":
        return "bg-success";
      case "Closed":
        return "bg-dark";
      default:
        return "bg-secondary";
    }
  }

  function getPriorityBadgeClass(priority) {
    switch (priority) {
      case "Low":
        return "bg-info";
      case "Medium":
        return "bg-primary";
      case "High":
        return "bg-warning text-dark";
      case "Urgent":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }

  function getTypeBadgeClass(type) {
    switch (type) {
      case "Bug":
        return "bg-danger";
      case "Feature Request":
        return "bg-success";
      case "Support":
        return "bg-info";
      case "Task":
        return "bg-warning text-dark";
      default:
        return "bg-secondary";
    }
  }

  function loadUsers() {
    $.ajax({
      url: "/api/users",
      method: "GET",
      success: function(response) {
        const users = response.data || response;
        const $assignee = $('#editAssignee');
        $assignee.empty();
        $assignee.append('<option value="">Select Assignee</option>');

        if (users && users.length > 0) {
          users.forEach(user => {
            const displayName = `${user.firstName} ${user.lastName} (${user.email})`;
            $assignee.append(`<option value="${user._id}">${displayName}</option>`);
          });
        }
      },
      error: function(xhr) {
        console.error('Error loading users:', xhr);
      }
    });
  }

  // Add comment function
  function addComment() {
    const commentText = $("#newComment").val().trim();
    if (!commentText) {
      alert("Please enter a comment");
      return;
    }

    const ticketId = $("#editTicketId").val();

    $.ajax({
      url: `${baseUrl}/${ticketId}/comment`,
      method: "POST",
      contentType: "application/json",
      data: JSON.stringify({
        message: commentText
      }),
      success: function(response) {
        $("#newComment").val("");
        // Refresh the ticket data to show new comment
        loadTicketComments(ticketId);
        showToast("Comment added successfully", "success");
      },
      error: function(xhr) {
        console.error('Error adding comment:', xhr);
        alert("Error adding comment. Please try again.");
      }
    });
  }

  // Load and display comments for a ticket
  function loadTicketComments(ticketId) {
    $.get(`${baseUrl}/${ticketId}`, function(ticket) {
      displayComments(ticket.comments || []);
    });
  }

  // Display comments in the modal
  function displayComments(comments) {
    const $commentsContainer = $("#existingComments");
    $commentsContainer.empty();

    if (!comments || comments.length === 0) {
      $commentsContainer.html('<div class="no-comments">No comments yet</div>');
      return;
    }

    comments.forEach(comment => {
      const commentDate = new Date(comment.createdAt).toLocaleString();
      const commentHtml = `
        <div class="comment-item">
          <div class="comment-header">
            <span class="comment-user">${comment.user}</span>
            <span class="comment-date">${commentDate}</span>
          </div>
          <p class="comment-message">${comment.message}</p>
        </div>
      `;
      $commentsContainer.append(commentHtml);
    });
  }

  function openEditModal(id) {
    $.get(`${baseUrl}/${id}`, (t) => {
      $("#editTicketId").val(t._id);
      $("#editTitle").val(t.title);
      $("#editDescription").val(t.description || "");
      $("#editType").val(t.type || "");
      $("#editStatus").val(t.status || "");
      $("#editPriority").val(t.priority || "");
      $("#editAssignee").val(t.assignee?._id || "");
      $("#editDueDate").val(t.dueDate ? new Date(t.dueDate).toISOString().split('T')[0] : "");
      $("#editResolutionNote").val(t.resolutionNote || "");

      // Show/hide resolution note based on status
      if (t.status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
      }

      currentTags = [...(t.tags || [])];
      $("#existingTags").empty();
      currentTags.forEach((tag) => {
        $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
      });

      currentAttachments = [...(t.attachments || [])];
      $("#existingAttachments").empty();
      currentAttachments.forEach((att) =>
        $("#existingAttachments").append(
          `<div><a href="${att}" target="_blank">${att}</a></div>`
        )
      );
      $("#newAttachments").val("");

      // Load and display comments
      displayComments(t.comments || []);

      // Clear new comment field
      $("#newComment").val("");

      $("#editTicketModal").modal("show");
    });
  }

  function openDeleteModal(id) {
    $("#deleteTicketId").val(id);
    $("#deleteConfirmModal").modal("show");
  }

  // Show toast notification
  function showToast(message, type = "info") {
    // Create toast element
    const toastHtml = `
      <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'danger' ? 'danger' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;

    // Add toast container if it doesn't exist
    if (!$('#toastContainer').length) {
      $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    // Add toast to container
    const $toast = $(toastHtml);
    $('#toastContainer').append($toast);

    // Initialize and show toast
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();

    // Remove toast element after it's hidden
    $toast.on('hidden.bs.toast', function() {
      $(this).remove();
    });
  }
</script>
