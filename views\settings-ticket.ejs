<%- contentFor('HeaderCss') %>
 <%- include("partials/title-meta", {"title":"Ticket Settings" }) %>

<!-- Select2 CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>
<style>
  body {
    background: #f8f9fa;
    color: #333;
  }
  .btn-primary {
    background: #5156be;
    border-color: #5156be;
  }
  .btn-primary:hover {
    background: #3f43a7;
    border-color: #3f43a7;
  }
  .table thead {
    background: #5156be;
    color: #fff;
  }
  .modal-header {
    background: #5156be;
    color: #fff;
  }
  .btn-close-white {
    filter: invert(100%);
  }
  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  .badge-tag {
    background-color: #6c757d;
    color: #fff;
    font-size: 0.85rem;
    padding: 0.4em 0.75em;
    margin: 2px;
    border-radius: 12px;
    display: inline-block;
  }

  .comment-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 3px solid #5156be;
  }

  .comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .comment-user {
    font-weight: 600;
    color: #5156be;
    font-size: 14px;
  }

  .comment-date {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
  }

  .comment-message {
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
  }

  .no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
  }

  .comment-new {
    border-left-color: #28a745 !important;
    background: #f8fff9 !important;
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }


</style>

<%- contentFor('body') %> 
<%- include("partials/page-title", { title: "Ticket Settings", pagetitle:"Ticket" }) %>
<div class="row align-items-center">
  <div class="col-md-6">
    <div class="mb-3">
      <h5 class="card-title">
        Total :<span class="text-muted fw-normal ms-2"
          >(<span id="totalTicketsCount">0</span>)</span
        >
      </h5>
    </div>
  </div>

  <div class="col-md-6">
    <div
      class="d-flex flex-wrap align-items-center justify-content-end gap-2 mb-3"
    >
      <div>
        <a href="/ticket-new/" class="btn btn-light" title="Add New Contact"
          ><i class="bx bx-plus me-1"></i>New</a
        >
      </div>
      <div>
        <a
          href="#"
          class="btn btn-light"
          title="Bulk Import Contact"
          ><i class="bx bx-download me-1"></i>Bulk Import</a
        >
      </div>
      <div>
        <a
          href="#"
          title="Export all Contacts"
          class="btn btn-light"
          ><i class="bx bx-upload me-1"></i>Export</a
        >
      </div>

     
      
    </div>
  </div>
</div>
<!-- Header Navigation Ends -->

<hr class="py-1" />


<div class="container mt-4">
  <div class="card mb-4 shadow-sm">
    <div class="card-body d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Public Page Visibility</h5>
      <div class="form-check form-switch">
        <input
          class="form-check-input"
          type="checkbox"
          id="toggleAllVisibility"
        />
        <label class="form-check-label" for="toggleAllVisibility"
          >Show All</label
        >
      </div>
    </div>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Tickets</h5>
      <div class="table-responsive">
        <table id="ticketTable" class="table table-bordered table-hover align-middle">
          <thead>
            <tr>
              <th>Title</th>
              <th>Type</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Assignee</th>
              <th>Due Date</th>
              <th>Tags</th>
              <th>Attachments</th>
              <th>Visible</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="ticketList"></tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editTicketModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form id="editTicketForm" enctype="multipart/form-data">
        <div class="modal-header">
          <h5 style="color: #ffffff;" class="modal-title">Edit Ticket</h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
          ></button>
        </div>
        <div class="modal-body">
          <input type="hidden" id="editTicketId" />
          <div class="mb-3">
            <label>Title</label>
            <input
              type="text"
              id="editTitle"
              class="form-control"
              required
            />
          </div>
          <div class="mb-3">
            <label>Description</label>
            <textarea
              id="editDescription"
              class="form-control"
              rows="4"
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Type</label>
            <select id="editType" class="form-select" required>
              <option value="">Choose One</option>
              <option value="Bug">Bug</option>
              <option value="Feature Request">Feature Request</option>
              <option value="Support">Support</option>
              <option value="Task">Task</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Status</label>
            <select id="editStatus" class="form-select">
              <option value="">Choose One</option>
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
            </select>
          </div>
          <div class="mb-3" id="resolutionNoteDiv" style="display: none;">
            <label>Resolution Note</label>
            <textarea
              id="editResolutionNote"
              class="form-control"
              rows="3"
              placeholder="Enter resolution details..."
            ></textarea>
          </div>
          <div class="mb-3">
            <label>Priority</label>
            <select id="editPriority" class="form-select">
              <option value="">Choose One</option>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
              <option value="Urgent">Urgent</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Assignee</label>
            <select id="editAssignee" class="form-control">
              <option value="">Select Assignee</option>
            </select>
          </div>
          <div class="mb-3">
            <label>Due Date</label>
            <input type="date" id="editDueDate" class="form-control" />
          </div>

          <div class="mb-3">
            <label>Tags</label>
            <div id="existingTags" class="mb-2"></div>
            <div class="input-group mb-3">
              <input
                type="text"
                id="newTag"
                class="form-control"
                placeholder="Type and press Enter to add tags..."
              />
              
            </div>
          </div>

          <div class="mb-3">
            <label>Attachments</label>
            <div id="existingAttachments" class="mb-2"></div>
            <input
              type="file"
              id="newAttachments"
              class="form-control"
              multiple
            />
          </div>

          <!-- Comments Section -->
          <div class="mb-3">
            <label>Comments</label>
            <div id="existingComments" class="mb-3" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px;">
              <!-- Existing comments will be displayed here -->
            </div>
            <div class="input-group">
              <textarea
                id="newComment"
                class="form-control"
                rows="2"
                placeholder="Add a comment..."
              ></textarea>
              <button type="button" class="btn btn-outline-primary" id="addCommentBtn">
                <i class="bx bx-send"></i> Add
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">
            <span
              class="spinner-border spinner-border-sm d-none"
              id="editSpinner"
            ></span>
            Save Changes
          </button>
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 style="color: #ffffff;" class="modal-title">Confirm Deletion</h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this ticket?
        <input type="hidden" id="deleteTicketId" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
          <span
            class="spinner-border spinner-border-sm d-none"
            id="deleteSpinner"
          ></span>
          Delete
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
<%- contentFor('FooterJs') %>

<!-- Select2 JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<!-- DataTables JS -->
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>


<script>
  const baseUrl = "/api/tickets";
  let currentTags = [],
    currentAttachments = [],
    comments = [],
    newComments = [];
   let table;
  let toggleButtonState = undefined;


  $(document).ready(function () {
    fetchTickets();
    initializeForm();
    loadUsers();

    $("#toggleAllVisibility").on("change", function () {
      if (toggleButtonState === undefined) {
        showToast("Please create a ticket first", "primary");
        return;
      }
      const visible = $(this).is(":checked");
      $.ajax({
        url: `${baseUrl}/toggle/visibility`,
        type: "PUT",
        contentType: "application/json",
        data: JSON.stringify({ visible }),
        success: () => {
          fetchTickets();
          showToast("Visibility toggled successfully", "success");
        },
        error: () => showToast("Error toggling all visibility"),
      });
    });

    // Show/hide resolution note based on status
    $("#editStatus").on("change", function() {
      const status = $(this).val();
      if (status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
        $("#editResolutionNote").val("");
      }
    });

    // Add comment functionality
    $("#addCommentBtn").on("click", function() {
      addComment();
    });

    // Add comment on Enter key (Ctrl+Enter for new line)
    $("#newComment").on("keypress", function(e) {
      if (e.which === 13 && !e.ctrlKey) {
        e.preventDefault();
        addComment();
      }
    });
  });

  // Initialize form components
  function initializeForm() {
    // Initialize Select2 for assignee dropdown
    $('#editAssignee').select2({
      placeholder: 'Select a user to assign',
      allowClear: false,
      width: '100%',
      dropdownParent: $('#editTicketModal')
    });
  }

  $(document).ready(function() {
    $("#newTag").on("keypress", function (e) {
      if (e.which === 13) {
        e.preventDefault();
         const tag = $("#newTag").val().trim();
      if (tag && !currentTags.includes(tag)) {
        currentTags.push(tag);
         $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
        $("#newTag").val("");
      }
        $(this).val("");
      }
    });

   
    $("#existingTags").on("click", ".remove-tag", function (e) {
      e.preventDefault();
      const t = $(this).data("tag");
      currentTags = currentTags.filter((x) => x !== t);
      $(this).parent().remove();
    });
    $("#editTicketForm").submit(function (e) {
      e.preventDefault();
      const id = $("#editTicketId").val();
      const fd = new FormData();
      fd.append("title", $("#editTitle").val());
      fd.append("description", $("#editDescription").val());
      fd.append("type", $("#editType").val());
      fd.append("status", $("#editStatus").val());
      fd.append("priority", $("#editPriority").val());
      fd.append("assignee", $("#editAssignee").val());
      fd.append("dueDate", $("#editDueDate").val());
      fd.append("resolutionNote", $("#editResolutionNote").val());
      fd.append("tags", JSON.stringify(currentTags));

      // Add pending comments to form data
      if (newComments.length > 0) {
        fd.append("newComments", JSON.stringify(newComments));
      }
      $("#newAttachments")[0].files.length &&
        $.each($("#newAttachments")[0].files, (i, f) =>
          fd.append("newAttachments", f)
        );
      fd.append("existingAttachments", JSON.stringify(currentAttachments));

      $("#editSpinner").removeClass("d-none");
      $('#editTicketForm button[type="submit"]').attr("disabled", true);

      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "PUT",
        processData: false,
        contentType: false,
        data: fd,
        success: () => {
          newComments = [];
          comments = [];
          $("#editTicketModal").modal("hide");
          fetchTickets();
          showToast("Ticket updated successfully", "success");
        },
        error: () => alert("Error updating ticket"),
        complete: () => {
          $("#editSpinner").addClass("d-none");
          $('#editTicketForm button[type="submit"]').attr("disabled", false);
        },
      });
    });

    $("#confirmDeleteBtn").click(() => {
      const id = $("#deleteTicketId").val();
      $("#deleteSpinner").removeClass("d-none");
      $("#confirmDeleteBtn").attr("disabled", true);
      $.ajax({
        url: `${baseUrl}/${id}`,
        type: "DELETE",
        success: () => {
          $("#deleteConfirmModal").modal("hide");
          showToast("Ticket deleted successfully", "success");
          fetchTickets();
        },
        error: () => {
          showToast("Error deleting ticket", "danger");
        },
        complete: () => {
          $("#deleteSpinner").addClass("d-none");
          $("#confirmDeleteBtn").attr("disabled", false);
        },
      });
    });
  });


    table=("#ticketTable").DataTable({
    responsive: true,
    searching: true,
    ordering: true,
    pageLength: 10,
     autoWidth: true,
    lengthMenu: [
      [10, 50, 100, 500, 1000],
      [10, 50, 100, 500, 1000],
    ],
    order: [[1, "desc"]],
     columnDefs: [
          
          { orderable: false, targets: -1 },
        ],

    ajax:{
      url:baseUrl,
      method:"GET",
       dataSrc: function (json) {
            
            return json.contacts;
          },
          beforeSend: function () {
            $("#loadingIndicator").show(); 
          },
          complete: function () {
            $("#loadingIndicator").hide();
          },
           columns: [
          {
            data: null,
            render: function (data, type, row, meta) {
              return `<input type="checkbox" class="contact-checkbox form-check-input" data-id="${row._id}">`;
            },
          },
          {
            data: "fullName",
            render: function (data, type, row, meta) {
              return `<a target="_blank" href="/profile/${row._id}">${data}</a>`;
            },
          },
          { data: "businessEmail" },
        ]
    }

    })

  function fetchTickets() {
    $.get(baseUrl, function (tickets) {
    
      const $tbody = $("#ticketList").empty();
      if (tickets.tickets.length === 0) {
        $tbody.append(`
          <tr>
            <td colspan="6">No tickets found</td>
          </tr>
        `);
        return;
      }
      toggleButtonState = true;
    
      tickets.tickets.forEach((t) => {
        const tags = t.tags?.length
          ? t.tags
              .map((tag) => `<span class="badge badge-tag">${tag}</span>`)
              .join(" ")
          : "N/A";
        const atts = t.attachments?.length ? t.attachments?.length : 0;
        const statusClass = getStatusBadgeClass(t.status);
        const priorityClass = getPriorityBadgeClass(t.priority);

      const assigneeName = t.assignee ? `${t.assignee.firstName} ${t.assignee.lastName}` : "Unassigned";
      const dueDate = t.dueDate ? new Date(t.dueDate).toLocaleDateString() : "N/A";
      const typeClass = getTypeBadgeClass(t.type);

      $tbody.append(`
  <tr>
    <td><a href="/ticket-single/${t._id}" target="_blank">${t.title}</a></td>
    <td><span class="badge ${typeClass}">${t.type || "N/A"}</span></td>
    <td><span class="badge ${statusClass}">${t.status || "Open"}</span></td>
    <td><span class="badge ${priorityClass}">${t.priority || "Medium"}</span></td>
    <td>${assigneeName}</td>
    <td>${dueDate}</td>
    <td>${tags}</td>
    <td style="text-align: center;">${atts}</td>
    <td>
      <div class="form-check form-switch">
        <input class="form-check-input toggle-single" type="checkbox" data-id="${t._id}" ${t.showOnPublicPage ? "checked" : ""}>
      </div>
    </td>
    <td style="border: none;">
      <div class="d-flex gap-2">
      <button class="btn btn-sm btn-primary" onclick="openEditModal('${t._id}')">Edit</button>
      <button class="btn btn-sm btn-danger" onclick="openDeleteModal('${t._id}')">Delete</button>
    </td>
  </tr>
`);

      });

      $(".toggle-single").on("change", function () {
        const id = $(this).data("id");
        $.ajax({
          url: `${baseUrl}/${id}/toggle/visibility`,
          type: "PUT",
          success: ()=>{
            fetchTickets();
            showToast("Visibility toggled successfully", "success");
          },
          error: () => showToast("Failed to toggle visibility"),
        });
      });
    });
  }
  function getStatusBadgeClass(status) {
    switch (status) {
      case "Open":
        return "bg-primary";
      case "In Progress":
        return "bg-warning";
      case "Resolved":
        return "bg-success";
      case "Closed":
        return "bg-dark";
      default:
        return "bg-secondary";
    }
  }

  function getPriorityBadgeClass(priority) {
    switch (priority) {
      case "Low":
        return "bg-info";
      case "Medium":
        return "bg-primary";
      case "High":
        return "bg-warning text-dark";
      case "Urgent":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }

  function getTypeBadgeClass(type) {
    switch (type) {
      case "Bug":
        return "bg-danger";
      case "Feature Request":
        return "bg-success";
      case "Support":
        return "bg-info";
      case "Task":
        return "bg-warning text-dark";
      default:
        return "bg-secondary";
    }
  }

  // Load users for assignment dropdown
  function loadUsers() {
    $.ajax({
      url: "/api/users",
      method: "GET",
      success: function(response) {
        console.log('Users loaded:', response);
        populateAssignToDropdown(response.data || response);
      },
      error: function(xhr) {
        console.error('Error loading users:', xhr);
        showToast('Error loading users for assignment', 'danger');
      }
    });
  }

  // Populate assignee dropdown
  function populateAssignToDropdown(users) {
    const $assignee = $('#editAssignee');
    $assignee.empty();
    $assignee.append('<option value="">Select a user to assign</option>');

    if (users && users.length > 0) {
      users.forEach(user => {
        const displayName = `${user.firstName} ${user.lastName} (${user.email})`;
        $assignee.append(`<option value="${user._id}">${displayName}</option>`);
      });
    }
  }

  
  function addComment() {
    const commentText = $("#newComment").val().trim();
    if (!commentText) {
      showToast("Please enter a comment", "warning");
      return;
    }

    
    const newComment = {
      message: commentText,
      user: { firstName: "You", lastName: "" },
      createdAt: new Date(),
      isNew: true 
    };

   

    
    newComments.push(newComment);
    comments=[...comments,...newComments]
    displayComments();

    // Clear input and show success
    $("#newComment").val("");
    showToast("Comment added (will be saved when you update the ticket)", "success");
  }





  // Display comments in the modal
  function displayComments() {
    console.log('Comments to display:', comments);
    const $commentsContainer = $("#existingComments");
    $commentsContainer.empty();

    if (!comments || comments.length === 0) {
      $commentsContainer.html('<div class="no-comments">No comments yet</div>');
      return;
    }

    comments.forEach(comment => {
      const commentDate = new Date(comment.createdAt).toLocaleString();
      const isNewComment = comment.isNew ? ' comment-new' : '';
      const newBadge = comment.isNew ? '<span class="badge bg-success ms-2">New</span>' : '';

     
      let userName = "Unknown User";
      if (comment.user) {
        if (comment.user.firstName === "You") {
          userName = "You";
        } else {
          userName = `${comment.user.firstName} ${comment.user.lastName}`.trim();
        }
      }

      const commentHtml = `
        <div class="comment-item${isNewComment}">
          <div class="comment-header">
            <span class="comment-user">${userName}${newBadge}</span>
            <span class="comment-date">${commentDate}</span>
          </div>
          <p class="comment-message">${comment.message}</p>
        </div>
      `;
      $commentsContainer.append(commentHtml);
    });

    // Scroll to bottom to show latest comments
    $commentsContainer.scrollTop($commentsContainer[0].scrollHeight);
  }

  function openEditModal(id) {
    $.get(`${baseUrl}/${id}`, (t) => {
      $("#editTicketId").val(t._id);
      $("#editTitle").val(t.title);
      $("#editDescription").val(t.description || "");
      $("#editType").val(t.type || "");
      $("#editStatus").val(t.status || "");
      $("#editPriority").val(t.priority || "");

      // Set assignee value for Select2
      const assigneeId = t.assignee?._id || "";
      $("#editAssignee").val(assigneeId).trigger('change');

      $("#editDueDate").val(t.dueDate ? new Date(t.dueDate).toISOString().split('T')[0] : "");
      $("#editResolutionNote").val(t.resolutionNote || "");

      // Show/hide resolution note based on status
      if (t.status === "Resolved") {
        $("#resolutionNoteDiv").show();
      } else {
        $("#resolutionNoteDiv").hide();
      }

      currentTags = [...(t.tags || [])];
      $("#existingTags").empty();
      currentTags.forEach((tag) => {
        $("#existingTags").append(`
    <span class="badge badge-tag fs-6 px-3 py-2 me-2 mb-2">
      ${tag}
      <a href="#" class="remove-tag text-white ms-2 fw-bold" data-tag="${tag}" style="text-decoration: none; font-size: 1.2rem;">&times;</a>
    </span>
  `);
      });

      currentAttachments = [...(t.attachments || [])];
      $("#existingAttachments").empty();
      currentAttachments.forEach((att) =>
        $("#existingAttachments").append(
          `<div><a href="${att}" target="_blank">${att}</a></div>`
        )
      );
      $("#newAttachments").val("");

      comments = t.comments || [];
 
      displayComments();

      
      $("#newComment").val("");

      $("#editTicketModal").modal("show");
    });
  }

  function openDeleteModal(id) {
    $("#deleteTicketId").val(id);
    $("#deleteConfirmModal").modal("show");
  }


  
</script>
