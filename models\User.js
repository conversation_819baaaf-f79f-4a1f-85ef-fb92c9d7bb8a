const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");

// Define the User schema
const userSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
      required: true,
    },
    displayName: {
      type: String,
      required: false,
      default: "Mixer",
    },
    profilePicture: {
      type: String,
      default: "/assets/images/users/avatar-1.jpg",
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    phone: {
      type: String,
    },
    password: {
      type: String,
      required: true,
    },
    resetPasswordToken: {
      type: String,
    },
    resetPasswordExpires: {
      type: Date,
    },
    status: {
      type: String,
      enum: ["active", "inactive", "suspended"], // Add other statuses as needed
      default: "active",
      required: true,
    },
    notifications: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Notification",
    },

    businessId: { type: mongoose.Schema.Types.ObjectId, ref: "Business" },

    role: {
      type: String,
      enum: [
        "Admin",
        "Manager",
        "Editor",
        "Viewer",
        "Marketer",
        "Sales",
        "Client",
        "Support",
      ],
      default: "Admin",
    },

    createdAt: {
      type: Date,
      default: Date.now,
    },
    lastLogin: {
      type: Date,
      default: Date.now,
    },
    isVerified: { type: Boolean, default: false },
    verificationToken: { type: String },
    isAccountDeleted: { type: Boolean, default: false },
    accountDeletionDate: { type: Date },
    otp: { type: String },
  },
  {
    timestamps: true,
  }
);

// Pre-save hook to hash the password before saving
userSchema.pre("save", async function (next) {
  if (this.isModified("password")) {
    try {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    } catch (err) {
      return next(err);
    }
  }
  next();
});

userSchema.methods.createPasswordResetToken = function () {
  const resetToken = crypto.randomBytes(32).toString("hex");

  this.resetPasswordToken = crypto
    .createHash("sha256")
    .update(resetToken)
    .digest("hex");

  this.resetPasswordExpires = Date.now() + 60 * 60 * 1000; // 60 minutes

  console.log("resetToken form user schema", resetToken);
  return resetToken;
};

// Method to compare entered password with the hashed password in the database
userSchema.methods.comparePassword = function (enteredPassword) {
  return bcrypt.compare(enteredPassword, this.password);
};

const User = mongoose.model("User", userSchema);

module.exports = User;
