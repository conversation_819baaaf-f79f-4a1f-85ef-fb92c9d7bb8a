<%- contentFor('HeaderCss') %> <%-include("partials/title-meta", {"title":"Contacts" }) %>

<link
  rel="stylesheet"
  type="text/css"
  href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css"
/>

<style>
  /* Fix for dropdown menu visibility */
  .table-responsive {
    overflow-x: scroll !important;
  }

  /* Ensure actions column has enough width */
  .actions-column {
    min-width: 100px;
    width: 100px;
  }

  /* Ensure dropdown menu is always visible */
  .action-dropdown {
    position: relative;
  }

  .action-dropdown .dropdown-menu {
    min-width: 120px;
    z-index: 1050;
    border: 1px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
  }

  .action-dropdown .dropdown-menu.show {
    display: block;
  }

  .action-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .action-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
  }

  /* Ensure table container doesn't clip dropdowns */
  .table-responsive {
    overflow: visible;
  }

  /* For DataTable wrapper */
  .dataTables_wrapper {
    overflow: visible;
  }

  /* Ensure last row dropdown is visible */
  .actions-column {
    position: relative;
    overflow: visible;
  }

  /* Specific fix for table body overflow */
  #contactsTable tbody {
    overflow: visible;
  }

  /* Ensure table rows don't clip dropdowns */
  #contactsTable tr {
    overflow: visible;
  }

  /* Fix for last row in table */
  #contactsTable tbody tr:last-child .action-dropdown .dropdown-menu {
    bottom: 100%;
    top: auto;
  }

  /* Adjust table layout for better responsiveness */
  #contactsTable {
    table-layout: fixed;
  }

  /* Adjust column widths */
  #contactsTable th:first-child,
  #contactsTable td:first-child {
    width: 5%;
  }

  #contactsTable th:last-child,
  #contactsTable td:last-child {
    width: 10%;
  }

  /* Ensure dropdown toggle button is always visible */
  .action-dropdown .dropdown-toggle {
    white-space: nowrap;
  }
</style>

<%- contentFor('body') %> <%-include("partials/page-title", {"title":"Contacts","pagetitle": "Blog" }) %>

<!-- Header Navigation Starts -->
<div class="row align-items-center">
  <div class="col-md-6">
    <div class="mb-3">
      <h5 class="card-title">
        Total :<span class="text-muted fw-normal ms-2"
          >(<span id="totalContactCount">0</span>)</span
        >
      </h5>
    </div>
  </div>

  <div class="col-md-6">
    <div
      class="d-flex flex-wrap align-items-center justify-content-end gap-2 mb-3"
    >
      <div>
        <a href="/contact-add/" class="btn btn-light" title="Add New Contact"
          ><i class="bx bx-plus me-1"></i>New</a
        >
      </div>
      <div>
        <a
          href="/contact-import-stages/"
          class="btn btn-light"
          title="Bulk Import Contact"
          ><i class="bx bx-download me-1"></i>Bulk Import</a
        >
      </div>
      <div>
        <a
          href="/api/contacts/export-csv"
          title="Export all Contacts"
          class="btn btn-light"
          ><i class="bx bx-upload me-1"></i>Export</a
        >
      </div>

      <!-- Bulk Action starts -->
      <div class="btn-group" role="group">
        <button
          id="bulkselectbuttons"
          type="button"
          class="btn btn-light dropdown-toggle"
          data-bs-toggle="dropdown"
          aria-expanded="false"
        >
          Bulk Actions <i class="mdi mdi-chevron-down"></i>
        </button>
        <ul class="dropdown-menu" aria-labelledby="bulkselectbuttons">
          <!-- <li><a class="dropdown-item" id="bulkEditBtn" href="#">Bulk Edit</a></li> -->
          <li>
            <button class="dropdown-item" id="bulkDeleteBtn" href="#">
              Bulk Delete
            </button>
          </li>
          <!-- <li><a class="dropdown-item" id="addToContactListBtn" href="#">Add to List</a></li> -->
          <!-- <li><a class="dropdown-item" id="addToCampaignBtn" href="#">Add Campaign</a></li> -->
          <!-- <li><a class="dropdown-item" id="addTaskBtn" href="#">Add Task</a></li> -->
          <!-- <li><a class="dropdown-item" id="addDealBtn" href="#">Add Deal</a></li> -->
          <!-- <li><a class="dropdown-item" id="addListBtn" href="#">Add Tags</a></li> -->
        </ul>
      </div>
      <!-- Bulk Action ends -->
    </div>
  </div>
</div>
<!-- Header Navigation Ends -->

<hr class="py-1" />

<div class="table-responsive">
  <table
    id="contactsTable"
    class="table table-responsive table-striped table-hover mt-4"
    style="width: 100%"
  >
    <thead>
      <tr>
        <th class="no-sort">
          <div class="form-check font-size-16">
            <input type="checkbox" class="form-check-input" id="selectAll" />
            <label class="form-check-label" for="selectAll"></label>
          </div>
        </th>
        <th>Name</th>
        <th>Created At</th>

        <th>Business Email</th>
        <th>Certificates</th>
        <th>Badges</th>
        <th>Variables Count</th>
        <th>Actions</th>
      </tr>
    </thead>

    <tbody>
      <!-- Rows will be dynamically populated by DataTables -->
    </tbody>
  </table>
  <div
    id="loadingIndicator"
    style="display: none; text-align: center; margin: 20px"
  >
    <span class="spinner-border text-primary"></span>
  </div>
</div>

<%- contentFor('FooterJs') %>

<!-- DataTables JS -->
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"
></script>
<script
  type="text/javascript"
  src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"
></script>

<!-- get all contact starts -->

<script>
  let table;
  var contactsLength = 0;
  function updateContactsLength(newContactLength) {
    contactsLength = newContactLength;
    $("#totalContactCount").html(contactsLength);

    if (contactsLength === 0) {
      $(".contact-checkbox").prop("checked", false);
    }
  }

  $(document).ready(function () {
    $(document).ready(function () {
      table = $("#contactsTable").DataTable({
        responsive: true,
        serverSide: true,
        searching: true,
        ordering: true,
        pageLength: 50, // Default records per page
        scrollX: false, // Disable horizontal scrolling
        autoWidth: true, // Enable auto width calculation
        lengthMenu: [
          [10, 50, 100, 500, 1000],
          [10, 50, 100, 500, 1000],
        ], // Dropdown options
        order: [[2, "desc"]], // Default sort by createdAt (index 2) in descending order
        columnDefs: [
          { orderable: false, targets: 0, width: "5%" }, // Explicitly disable sorting on the first column (checkbox)
          { orderable: false, targets: -1, width: "10%" }, // Explicitly disable sorting on the last column (actions)
        ],
        ajax: {
          url: "/api/contacts",
          method: "GET",
          data: function (d) {
            // Handle the order array and check if it's populated
            
            const orderColumn =
              d.columns && d.order && d.order.length > 0
                ? d.columns[d.order[0].column].data
                : "createdAt"; // Default sorting column: 'fullName'

            const orderDir =
              d.order && d.order.length > 0 ? d.order[0].dir : "desc"; // Default sorting order: Ascending
             
            return {
              draw: d.draw,
              page: Math.floor(d.start / d.length) + 1, // Calculate the page number correctly
              limit: d.length,
              search: d.search.value, // Pass the search term
              orderColumn: orderColumn, // Send the correct column name here
              orderDir: orderDir, // Send the correct order direction
            };
          },
          dataSrc: function (json) {
            updateContactsLength(json.recordsTotal);
            return json.contacts;
          },
          beforeSend: function () {
            $("#loadingIndicator").show(); // Show custom loading message
          },
          complete: function () {
            $("#loadingIndicator").hide(); // Hide loading message after data loads
          },
        },
        columns: [
          {
            data: null,
            render: function (data, type, row, meta) {
              return `<input type="checkbox" class="contact-checkbox form-check-input" data-id="${row._id}">`;
            },
          },
          {
            data: "fullName",
            render: function (data, type, row, meta) {
              return `<a target="_blank" href="/profile/${row._id}">${data}</a>`;
            },
          },
          { data: "createdAt",
            render: function (data, type, row, meta) {
              return new Date(data).toLocaleDateString();
            },
           },
          { data: "businessEmail" },
          {
            data: "certificates",
            render: function (data) {
              return data ? data.length : 0;
            },
          },
          {
            data: "badges",
            render: function (data) {
              return data ? data.length : 0;
            },
          },
          // {
          //   data: "variables",
          //   render: function (data, type, row, meta) {
          //     const namedVariables = Object.entries(data);
          //     if(!namedVariables.length) return `<span>N/A</span>`
          //     const maxCount = 2;
          //     let remaining = 0;

          //     if (namedVariables.length > maxCount) {
          //       remaining = namedVariables.length - maxCount;
          //     }

          //     const displayed = namedVariables
          //       .slice(0, maxCount)
          //       .map(([key, value]) => `<span>${key}: ${value}</span>`)
          //       .join(", ");

          //     return `${displayed}${
          //       remaining > 0 ? `, <span>+${remaining} more</span>` : ""
          //     }`;
          //   },
          // },

          {
            data: "variables",
            render: function (data, type, row, meta) {
              const totalVariables = Object.keys(data).length;
              return totalVariables;
            },
          },
         {
  data: null,
  className: "actions-column",
  render: function (data, type, row, meta) {
    return `
      <div class="dropdown action-dropdown">
        <button class="btn btn-link font-size-16 shadow-none py-0 text-muted dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
          <i class="bx bx-dots-horizontal-rounded"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
          <li><a class="dropdown-item" target="_blank" href="/profile/${row._id}">
            <i class="bx bx-show me-2"></i>View
          </a></li>
          <li><a class="dropdown-item edit-contact-btn" data-id="${row._id}" href="#">
            <i class="bx bx-edit me-2"></i>Edit
          </a></li>
          <li><a class="dropdown-item delete-contact-btn" data-id="${row._id}" href="#">
            <i class="bx bx-trash me-2 text-danger"></i>Delete
          </a></li>
        </ul>
      </div>`;
  },
}

        ],
      });
    });

    // Handle select all checkbox click
    $("#selectAll").change(function () {
      const isChecked = $(this).is(":checked");
      $(".contact-checkbox").prop("checked", isChecked).trigger("change");
      $("#bulkselectbuttons").toggleClass("shadow-lg btn-primary btn-light"); // Highlight the bulk button when multiple contacts are selected
    });

    let selectedContacts = [];

    // Handle individual checkbox click using event delegation
    $(document).on("change", ".contact-checkbox", function () {
      const contactId = $(this).data("id");
      if ($(this).is(":checked")) {
        selectedContacts.push(contactId);
      } else {
        selectedContacts = selectedContacts.filter((id) => id !== contactId);
      }
    });

    // Close all dropdowns when clicking outside
    $(document).on('click', function(e) {
      if (!$(e.target).closest('.action-dropdown').length) {
        $('.action-dropdown .dropdown-menu').removeClass('show');
        $('.action-dropdown .dropdown-toggle').attr('aria-expanded', 'false');
      }
    });

    // Handle dropdown toggle
    $(document).on('click', '.action-dropdown .dropdown-toggle', function(e) {
      e.preventDefault();
      e.stopPropagation();

      // Close all other dropdowns first
      $('.action-dropdown .dropdown-menu').not($(this).siblings('.dropdown-menu')).removeClass('show');
      $('.action-dropdown .dropdown-toggle').not(this).attr('aria-expanded', 'false');

      // Toggle current dropdown
      const $menu = $(this).siblings('.dropdown-menu');
      const isOpen = $menu.hasClass('show');

      if (isOpen) {
        $menu.removeClass('show');
        $(this).attr('aria-expanded', 'false');
      } else {
        $menu.addClass('show');
        $(this).attr('aria-expanded', 'true');

        // Position the dropdown properly
        const $dropdown = $(this).closest('.action-dropdown');
        const dropdownRect = $dropdown[0].getBoundingClientRect();
        const menuRect = $menu[0].getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Reset positioning
        $menu.css({
          position: 'absolute',
          top: '100%',
          left: 'auto',
          right: '0',
          transform: 'none'
        });

        // Check if dropdown goes off screen and adjust
        setTimeout(() => {
          const newMenuRect = $menu[0].getBoundingClientRect();

          // If dropdown goes off the right edge
          if (newMenuRect.right > viewportWidth) {
            $menu.css('right', '0');
          }

          // If dropdown goes off the bottom edge
          if (newMenuRect.bottom > viewportHeight) {
            $menu.css({
              top: 'auto',
              bottom: '100%'
            });
          }
        }, 10);
      }
    });

    // Prevent dropdown from closing when clicking inside
    $(document).on('click', '.action-dropdown .dropdown-menu', function(e) {
      e.stopPropagation();
    });
  });
</script>

<!--  get all contact ends-->

<!-- single contact delete starts -->

<!-- Delete Confirmation Modal -->
<div
  class="modal fade"
  id="deleteContactModal"
  tabindex="-1"
  aria-labelledby="deleteContactModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteContactModalLabel">Delete Contact</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this contact?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDeleteContact">
          Delete
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    let contactIdToDelete = null;

    // Open delete confirmation modal
    $(document).on("click", ".delete-contact-btn", function () {
      //alert("delete-contact-btn clicked")
      contactIdToDelete = $(this).data("id");
      $("#deleteContactModal").modal("show");
    });

    // Confirm deletion
    $("#confirmDeleteContact").on("click", function () {
      if (contactIdToDelete) {
        contactIdToDelete = DOMPurify.sanitize(contactIdToDelete);
        $.ajax({
          url: `/api/contacts/${contactIdToDelete}`,
          method: "DELETE",
          success: function (response) {
            console.log("response after bulk delete", response);

            $("#deleteContactModal").modal("hide");
            table.ajax.reload(); // Reload DataTable to reflect the deletion

            //$('#totalContactCount').html(json.contacts.length-contactIdToDelete.length);
            showToast(response.message, "primary"); // Optional: Show success message

            //console.log("after delte contacts", contactsLength, contactIdToDelete.length, contactsLength-contactIdToDelete.length)
            //update the contactLength
            updateContactsLength(contactsLength - 1);
          },
          error: function (xhr) {
            $("#deleteContactModal").modal("hide");
            showToast("Failed to delete contact. Please try again.", "danger"); // Optional: Show error message
          },
        });
      }
    });
  });
</script>

<!-- single contact delete ends -->

<!-- bulk delete modal starts -->

<!-- Bulk Delete Modal -->
<div
  class="modal fade"
  id="bulkDeleteModal"
  tabindex="-1"
  aria-labelledby="bulkDeleteModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="bulkDeleteModalLabel">
          Bulk Delete Contacts
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete the selected
        <span id="selectedContactsLength"></span> contacts?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmBulkDelete">
          Delete
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Show modal on bulk delete button click
  $("#bulkDeleteBtn").on("click", function () {
    const selectedContacts = $(".contact-checkbox:checked");
    if (selectedContacts.length > 0) {
      $("#bulkDeleteModal").modal("show");
      $("#selectedContactsLength").html(selectedContacts.length);
      //alert(selectedContacts.length);
    } else {
      showToast("Please select at least one contact to delete.", "danger");
    }
  });

  // Handle bulk delete confirmation
  $("#confirmBulkDelete").on("click", function () {
    const selectedIds = $(".contact-checkbox:checked")
      .map(function () {
        return $(this).data("id");
      })
      .get();

    //alert("deleting conatcts",selectedIds)

    $.ajax({
      url: "/api/contacts/delete-multiple",
      method: "DELETE",
      contentType: "application/json",
      data: JSON.stringify({ contactIds: selectedIds }),
      success: function () {
        $("#bulkDeleteModal").modal("hide");
        table.ajax.reload();

        console.log(
          "after delte contacts",
          contactsLength,
          selectedIds.length,
          contactsLength - selectedIds.length
        );

        //update the contactLength
        updateContactsLength(contactsLength - selectedIds.length);
      },
      error: function () {
        showToast("Failed to delete contacts. Please try again.", "danger");
      },
    });
  });
</script>
<!-- bulk delete modal ends -->

<!-- Camapign Starts -->

<!-- Add to Campaign Modal -->
<div
  class="modal fade"
  id="addToCampaignModal"
  tabindex="-1"
  aria-labelledby="addToCampaignModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addToCampaignModalLabel">
          Add to Campaign
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="addToCampaignForm">
          <div class="mb-3">
            <label for="campaignSelect" class="form-label"
              >Select Campaign</label
            >
            <select class="form-select" id="campaignSelect" name="campaignId">
              <!-- Options will be populated by jQuery -->
            </select>
          </div>
          <div class="d-flex justify-content-between">
            <a
              href="https://contacts.mixcommerce.co/campaigns-new"
              class="btn btn-link"
              target="_blank"
            >
              <i class="fa fa-plus"></i> Add New Campaign
            </a>
            <button type="button" class="btn btn-link" id="syncCampaignsBtn">
              <i class="fa fa-sync"></i> Sync Campaigns
            </button>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" id="confirmAddToCampaign">
          Add to Campaign
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    // Function to populate the campaign dropdown
    function populateCampaigns() {
      $.ajax({
        url: "/api/campaigns",
        type: "GET",
        success: function (campaigns) {
          const campaignSelect = $("#campaignSelect");
          campaignSelect.empty(); // Clear existing options
          campaigns.forEach((campaign) => {
            campaignSelect.append(new Option(campaign.name, campaign.id));
          });
        },
        error: function (error) {
          console.error("Error fetching campaigns:", error);
        },
      });
    }

    // Initial population of campaigns on page load
    populateCampaigns();

    // Handle Add to Campaign button click
    $("#addToCampaignBtn").on("click", function () {
      const selectedContacts = $(".contact-checkbox:checked");
      if (selectedContacts.length > 0) {
        $("#addToCampaignModal").modal("show");
      } else {
        showToast(
          "Please select at least one contact to add to a campaign.",
          "danger"
        );
      }
    });

    // Handle campaign sync button click
    $("#syncCampaignsBtn").on("click", function () {
      populateCampaigns(); // Re-populate the campaigns
    });

    // Handle Add to Campaign confirmation
    $("#confirmAddToCampaign").on("click", function () {
      const selectedIds = $(".contact-checkbox:checked")
        .map(function () {
          return this.value;
        })
        .get();
      const campaignId = $("#campaignSelect").val();

      $.ajax({
        url: "/api/addtocampaign",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({ ids: selectedIds, campaignId: campaignId }),
        success: function () {
          $("#addToCampaignModal").modal("hide");
          table.ajax.reload();
          showToast("Contacts added to the campaign.", "danger");
        },
        error: function () {
          showToast(
            "Failed to add contacts to the campaign. Please try again.",
            "danger"
          );
        },
      });
    });
  });
</script>

<!-- Camapign Ends -->

<!-- Add to contactList starts -->
<!-- Add to Contact List Modal -->

<div
  class="modal fade"
  id="addToContactListModal"
  tabindex="-1"
  aria-labelledby="addToContactListModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addToContactListModalLabel">
          Add to Contact List
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="addToContactListForm">
          <div class="mb-3">
            <label for="contactListSelect" class="form-label"
              >Select Contact List</label
            >
            <select
              class="form-select"
              id="contactListSelect"
              name="contactListId"
            >
              <!-- Options will be populated by jQuery -->
            </select>
          </div>
          <div class="d-flex justify-content-between">
            <a href="/contact-lists/" class="btn btn-link" target="_blank">
              <i class="fa fa-plus"></i> Add New Contact List
            </a>
            <button type="button" class="btn btn-link" id="syncContactListsBtn">
              <i class="fa fa-sync"></i> Sync Contact Lists
            </button>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-primary"
          id="confirmAddToContactList"
        >
          Add to Contact List
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    // Function to populate the contact list dropdown
    function populateContactLists() {
      $.ajax({
        url: "/api/contactlists",
        type: "GET",
        success: function (contactLists) {
          const contactListSelect = $("#contactListSelect");
          contactListSelect.empty(); // Clear existing options

          contactLists.forEach((list) => {
            let selectname = list.name + " (" + list.contacts.length + ")";
            //alert(selectname, list.contacts.length);

            contactListSelect.append(new Option(selectname, list.id));
          });
        },
        error: function (error) {
          console.error("Error fetching contact lists:", error);
        },
      });
    }

    // Initial population of contact lists on page load
    populateContactLists();

    // Show the "Add to Contact List" modal when the button is clicked
    $("#addToContactListBtn").on("click", function () {
      const selectedContacts = $(".contact-checkbox:checked");
      if (selectedContacts.length > 0) {
        $("#addToContactListModal").modal("show");
      } else {
        showToast(
          "Please select at least one contact to add to a contact list.",
          "danger"
        );
      }
    });

    // Show sync button animation and re-populate contact lists
    $("#syncContactListsBtn").on("click", function () {
      const syncButton = $(this).find("i");
      syncButton.addClass("fa-spin"); // Add spin animation

      populateContactLists();

      // Stop animation after sync completes
      setTimeout(() => {
        syncButton.removeClass("fa-spin");
      }, 1000); // Adjust timeout based on expected sync time
    });

    // Handle Add to Contact List button click
    $("#confirmAddToContactList").on("click", function () {
      const selectedIds = $(".contact-checkbox:checked")
        .map(function () {
          return this.value;
        })
        .get();
      const contactListId = $("#contactListSelect").val();
      const message =
        "adding contacts " + selectedIds + " to the list " + contactListId;
      showToast(message, "primary");

      $.ajax({
        url: "/api/addtocontactlist",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({
          ids: selectedIds,
          contactListId: contactListId,
        }),
        success: function () {
          $("#addToContactListModal").modal("hide");
          table.ajax.reload();
          showToast("Contacts added to the contact list.", "primary");
        },
        error: function () {
          showToast(
            "Failed to add contacts to the contact list. Please try again.",
            "danger"
          );
        },
      });
    });
  });
</script>
<!-- Add to contactList ends -->

<!-- Single contact Edit stats -->
<!-- Single Contact Edit Modal -->
<div
  class="modal fade"
  id="singleContactEditModal"
  tabindex="-1"
  aria-labelledby="singleContactEditModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="singleContactEditModalLabel">
          Edit Contact
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="singleContactEditForm">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editFullName" class="form-label">Full Name</label>
              <input
                type="text"
                class="form-control"
                id="editFullName"
                name="fullName"
              />
            </div>

            <div class="col-md-6 mb-3">
              <label for="editBusinessEmail" class="form-label"
                >Business Email</label
              >
              <input
                type="email"
                class="form-control"
                id="editBusinessEmail"
                name="businessEmail"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editFirstName" class="form-label">First Name</label>
              <input
                type="text"
                class="form-control"
                id="editFirstName"
                name="firstName"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editLastName" class="form-label">Last Name</label>
              <input
                type="text"
                class="form-control"
                id="editLastName"
                name="lastName"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editJobTitle" class="form-label">Job Title</label>
              <input
                type="text"
                class="form-control"
                id="editJobTitle"
                name="jobTitle"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editCompanyName" class="form-label"
                >Company Name</label
              >
              <input
                type="text"
                class="form-control"
                id="editCompanyName"
                name="companyName"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editPhoneNumber" class="form-label"
                >Phone Number</label
              >
              <input
                type="text"
                class="form-control"
                id="editPhoneNumber"
                name="phoneNumber"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editCompanySize" class="form-label"
                >Company Size</label
              >
              <select
                class="form-control"
                id="editCompanySize"
                name="companySize"
              >
                <option value="0-1">0-1 employees</option>
                <option value="2-10">2-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-100 employees</option>
                <option value="51-200">101-200 employees</option>
                <option value="201-500">201-500 employees</option>
                <option value="501-1000">501-1000 employees</option>
                <option value="1001-5000">1001-5000 employees</option>
                <option value="5001-10000">5001-10000 employees</option>
                <option value="10000+">10000+ employees</option>
              </select>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editServiceInterest" class="form-label"
                >Service Interest</label
              >
              <input
                type="text"
                class="form-control"
                id="editServiceInterest"
                name="serviceInterest"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editIndustry" class="form-label">Industry</label>
              <input
                type="text"
                class="form-control"
                id="editIndustry"
                name="industry"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editWebsite" class="form-label">Website</label>
              <input
                type="url"
                class="form-control"
                id="editWebsite"
                name="website"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editLeadSource" class="form-label">Lead Source</label>
              <select
                class="form-control"
                id="editLeadSource"
                name="leadSource"
              >
                <option value="referral" default>Referral</option>
                <option value="coldCall">Cold Call</option>
                <option value="partner">Partner</option>
                <option value="event">Event</option>
                <option value="emailCampaign">Email Campaign</option>
                <option value="advertisement">Advertisement</option>
                <option value="socialMedia">Social Media</option>
                <option value="website">Website</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editContactStatus" class="form-label"
                >Contact Status</label
              >
              <select
                class="form-control"
                id="editContactStatus"
                name="contactStatus"
              >
                <option value="New">New</option>
                <option value="Contacted">Contacted</option>
                <option value="Qualified">Qualified</option>
                <option value="Converted">Converted</option>
                <option value="InProgress">InProgress</option>
                <option value="Closed">Closed</option>
                <option value="Qualified">Qualified</option>
              </select>
            </div>

            <div class="col-lg-6 mb-3">
              <label for="editCompanyRevenue" class="form-label"
                >Company Revenue</label
              >
              <input
                type="text"
                class="form-control"
                id="editCompanyRevenue"
                name="companyRevenue"
              />
            </div>

            <div class="col-lg-6 mb-3">
              <label for="editAccountManager" class="form-label"
                >Account Manager</label
              >
              <input
                type="text"
                class="form-control"
                id="editAccountManager"
                name="accountManager"
              />
            </div>

            <div class="col-lg-6 mb-3">
              <label for="editCompanyAddress" class="form-label"
                >Company Address</label
              >
              <input
                type="text"
                class="form-control"
                id="editCompanyAddress"
                name="companyAddress"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editLinkedinProfile" class="form-label"
                >LinkedIn Profile</label
              >
              <input
                type="url"
                class="form-control"
                id="editLinkedinProfile"
                name="linkedinProfile"
              />
            </div>
            <div class="col-md-6 mb-3">
              <label for="editTags" class="form-label">Tags</label>
              <input
                type="text"
                class="form-control"
                id="editTags"
                name="tags"
              />
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="editPreferredContactMethod" class="form-label"
                >Preferred Contact Method</label
              >
              <select
                class="form-control"
                id="editPreferredContactMethod"
                name="preferredContactMethod"
              >
                <option value="Email">Email</option>
                <option value="Phone">Phone</option>
                <option value="SMS">SMS</option>
                <option value="LinkedIn">LinkedIn</option>
                <option value="WhatsApp">WhatsApp</option>
              </select>
            </div>

            <div class="col-md-6 mb-3">
              <label for="marketing-opt-out" class="form-label"
                >Marketing Opt-Out</label
              >

              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="editOptOutEmail"
                  name="opt-out-email"
                />
                <label class="form-check-label" for="opt-out-email"
                  >Email</label
                >
              </div>

              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="editOptOutSms"
                  name="opt-out-sms"
                />
                <label class="form-check-label" for="opt-out-sms">SMS</label>
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="editOptOutPhone"
                  name="opt-out-phone"
                />
                <label class="form-check-label" for="opt-out-phone"
                  >Phone</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="editOptOutLinkedin"
                  name="opt-out-linkedin"
                />
                <label class="form-check-label" for="opt-out-linkedin"
                  >LinkedIn</label
                >
              </div>
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="editOptOutWhatsapp"
                  name="opt-out-whatsapp"
                />
                <label class="form-check-label" for="opt-out-whatsapp"
                  >WhatsApp</label
                >
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-12 mb-3">
              <label for="editNotes" class="form-label">Notes</label>
              <textarea
                class="form-control"
                id="editNotes"
                name="notes"
                rows="5"
                style="width: 100%"
              ></textarea>
            </div>

            <div class="col-12 mb-3">
              <div
                class="d-flex justify-content-between align-items-center mb-2"
              >
                <label class="form-label mb-0">Variables</label>
                <button
                  type="button"
                  id="addVariableBtn"
                  class="btn btn-sm"
                  style="background-color: #5156be; color: #ffffff"
                >
                  <i class="fas fa-plus me-1"></i> Add Variables
                </button>
              </div>
              <div
                id="variablesContainer"
                class="border rounded p-3"
                style="
                  min-height: 150px;
                  max-height: 250px;
                  overflow-y: auto;
                  width: 100%;
                "
              >
                <div
                  class="text-muted text-center py-3"
                  id="noVariablesMessage"
                >
                  No variables added yet
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button
          type="button"
          class="btn"
          id="saveContactChanges"
          style="background-color: #5156be; color: #ffffff"
        >
          Save Changes
        </button>
      </div>
    </div>
  </div>
</div>
<!-- Add Variable Modal -->
<div
  class="modal fade"
  id="variableModal"
  tabindex="-1"
  aria-labelledby="variableModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div
        class="modal-header"
        style="background-color: #5156be; color: #ffffff"
      >
        <h5 style="color: #ffffff" class="modal-title" id="variableModalLabel">
          Add Variable
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="variableForm">
          <div class="mb-3">
            <label for="variableName" class="form-label">Variable Name</label>
            <input
              type="text"
              class="form-control"
              id="variableName"
              required
            />
          </div>
          <div class="mb-3">
            <label for="variableValue" class="form-label">Value</label>
            <input
              type="text"
              class="form-control"
              id="variableValue"
              required
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button
          type="button"
          class="btn"
          id="saveVariableBtn"
          style="background-color: #5156be; color: #ffffff"
        >
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Variable Modal -->
<div
  class="modal fade"
  id="editVariableModal"
  tabindex="-1"
  aria-labelledby="editVariableModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div
        class="modal-header"
        style="background-color: #5156be; color: #ffffff"
      >
        <h5
          style="color: #ffffff"
          class="modal-title"
          id="editVariableModalLabel"
        >
          Edit Variable
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <form id="editVariableForm">
          <div class="mb-3">
            <label for="editVariableName" class="form-label"
              >Variable Name</label
            >
            <input
              type="text"
              class="form-control"
              id="editVariableName"
              required
            />
            <input type="hidden" id="originalVariableName" />
          </div>
          <div class="mb-3">
            <label for="editVariableValue" class="form-label">Value</label>
            <input
              type="text"
              class="form-control"
              id="editVariableValue"
              required
            />
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Cancel
        </button>
        <button
          type="button"
          class="btn"
          id="updateVariableBtn"
          style="background-color: #5156be; color: #ffffff"
        >
          Update
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    let variables = {};

    function renderVariables() {
      const $container = $("#variablesContainer");

      // Clear container
      $container.empty();

      // Check if there are any variables
      if (Object.keys(variables).length === 0) {
        $container.html(
          '<div class="text-muted text-center py-3" id="noVariablesMessage">No variables added yet</div>'
        );
        return;
      }

      // Create table structure
      const tableHtml = `
          <table class="table table-bordered mb-0" id="variablesTable">
            <thead class="table-light">
              <tr>
                <th>Name</th>
                <th>Value</th>
                <th style="width: 120px;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <!-- Variables will be added here dynamically -->
            </tbody>
          </table>
        `;
      $container.html(tableHtml);

      // Add each variable to the table
      const $tableBody = $("#variablesTable tbody");
      for (const [name, value] of Object.entries(variables)) {
        const row = `
            <tr>
              <td>${name}</td>
              <td>${value}</td>
              <td class="text-center">
                <button type="button" class="btn btn-sm edit-variable p-1 me-1" data-name="${name}" data-value="${value}" style="background-color: white; border: none;">
                  <i class="fas fa-edit" style="color: #5156be;"></i>
                </button>
                <button type="button" class="btn btn-sm delete-variable p-1" data-name="${name}" style="background-color: white; border: none;">
                  <i class="fas fa-trash" style="color: red;"></i>
                </button>
              </td>
            </tr>
          `;
        $tableBody.append(row);
      }

      // Add edit event handlers
      $(".edit-variable").on("click", function () {
        const name = $(this).data("name");
        const value = $(this).data("value");

        // Populate the edit form
        $("#editVariableName").val(name);
        $("#originalVariableName").val(name); // Store original name for reference
        $("#editVariableValue").val(value);

        // Show the edit modal
        $("#editVariableModal").modal("show");
      });

      // Add delete event handlers
      $(".delete-variable").on("click", function () {
        const name = $(this).data("name");
        delete variables[name];
        renderVariables();
      });
    }
    // Function to open the modal and populate it with contact data

    // Open variable modal
    $("#addVariableBtn").on("click", function () {
      // Clear the form
      $("#variableForm")[0].reset();
      // Show the modal
      $("#variableModal").modal("show");
    });

    // Save variable
    $("#saveVariableBtn").on("click", function () {
      const name = $("#variableName").val().trim();
      const value = $("#variableValue").val().trim();

      if (!name || !value) {
        showToast("Please enter both variable name and value", "danger");
        return;
      }

      // Add to variables object
      variables[name] = value;

      // Update UI
      renderVariables();

      // Close modal
      $("#variableModal").modal("hide");
    });

    // Update variable
    $("#updateVariableBtn").on("click", function () {
      const originalName = $("#originalVariableName").val().trim();
      const newName = $("#editVariableName").val().trim();
      const value = $("#editVariableValue").val().trim();

      if (!newName || !value) {
        showToast("Please enter both variable name and value", "danger");
        return;
      }

      // Check if the name has changed and if the new name already exists
      if (newName !== originalName && variables.hasOwnProperty(newName)) {
        showToast("A variable with this name already exists", "danger");
        return;
      }

      // If name has changed, delete the old one and add with new name
      if (newName !== originalName) {
        // Get the value
        const oldValue = variables[originalName];
        // Delete the old entry
        delete variables[originalName];
        // Add with new name
        variables[newName] = value;

        showToast("Variable name and value updated successfully", "primary");
      } else {
        // Just update the value
        variables[newName] = value;
        showToast("Variable value updated successfully", "primary");
      }

      // Update UI
      renderVariables();

      // Close modal
      $("#editVariableModal").modal("hide");
    });

    function openEditModal(contactId) {
      contactId=DOMPurify.sanitize(contactId);
      $.ajax({
        url: `/api/contacts/${contactId}`,
        method: "GET",
        success: function (contact) {
          console.log("contact", contact);
          // Populate the form with contact data - using standardized ID naming convention
          $("#editFullName").val(contact.fullName);
          $("#editFirstName").val(contact.firstName);
          $("#editLastName").val(contact.lastName);
          $("#editJobTitle").val(contact.jobTitle);
          $("#editCompanyName").val(contact.companyName);
          $("#editBusinessEmail").val(contact.businessEmail);
          $("#editPhoneNumber").val(contact.phoneNumber);
          $("#editCompanySize").val(contact.companySize);
          $("#editServiceInterest").val(contact.serviceInterest);
          $("#editIndustry").val(contact.industry);
          $("#editWebsite").val(contact.website);
          $("#editLeadSource").val(contact.leadSource);
          $("#editContactStatus").val(contact.contactStatus);
          $("#editCompanyRevenue").val(contact.companyRevenue);
          $("#editAccountManager").val(contact.accountManager);
          $("#editCompanyAddress").val(contact.companyAddress);
          $("#editLinkedinProfile").val(contact.linkedinProfile);
          $("#editTags").val(contact.tags);
          $("#editPreferredContactMethod").val(contact.preferredContactMethod);
          $("#editNotes").val(contact.notes);

          // Handle marketing opt-out checkboxes
          if (contact.communication) {
            console.log(contact.communication);
            $("#editOptOutEmail").prop("checked", contact.communication.email);
            $("#editOptOutSms").prop("checked", contact.communication.sms);
            $("#editOptOutPhone").prop("checked", contact.communication.phone);
            $("#editOptOutLinkedin").prop(
              "checked",
              contact.communication.linkedIn
            );
            $("#editOptOutWhatsapp").prop(
              "checked",
              contact.communication.whatsApp
            );
          }

          // Handle variables
          variables = contact.variables || {};
          renderVariables();

          // Store the contact ID in the form for later use
          $("#singleContactEditForm").data("contactId", contactId);

          // Show the modal
          $("#singleContactEditModal").modal("show");
        },
        error: function () {
          showToast(
            "Failed to fetch contact data. Please try again.",
            "danger"
          );
        },
      });
    }

    // Event handler for opening the edit modal
    $(document).on("click", ".edit-contact-btn", function () {
      const contactId = $(this).data("id");
      openEditModal(contactId);
    });

    // Handle form submission to save contact changes
    $("#saveContactChanges").on("click", function () {
      const contactId = DOMPurify.sanitize($("#singleContactEditForm").data("contactId"));
      const formData = {
        fullName: DOMPurify.sanitize($("#editFullName").val()),
        firstName: DOMPurify.sanitize($("#editFirstName").val()),
        lastName: DOMPurify.sanitize($("#editLastName").val()),
        jobTitle: DOMPurify.sanitize($("#editJobTitle").val()),
        companyName: DOMPurify.sanitize($("#editCompanyName").val()),
        businessEmail: DOMPurify.sanitize($("#editBusinessEmail").val()),
        phoneNumber: DOMPurify.sanitize($("#editPhoneNumber").val()),
        companySize: DOMPurify.sanitize($("#editCompanySize").val()),
        serviceInterest: DOMPurify.sanitize($("#editServiceInterest").val()),
        industry: DOMPurify.sanitize($("#editIndustry").val()),
        website: DOMPurify.sanitize($("#editWebsite").val()),
        leadSource: DOMPurify.sanitize($("#editLeadSource").val()),
        contactStatus: DOMPurify.sanitize($("#editContactStatus").val()),
        companyRevenue: DOMPurify.sanitize($("#editCompanyRevenue").val()),
        accountManager: DOMPurify.sanitize($("#editAccountManager").val()),
        companyAddress: DOMPurify.sanitize($("#editCompanyAddress").val()),
        linkedinProfile: DOMPurify.sanitize($("#editLinkedinProfile").val()),
        tags: $("#editTags").val(),
        preferredContactMethod: $("#editPreferredContactMethod").val(),
        notes: $("#editNotes").val(),
        communication: {
          email: $("#editOptOutEmail").is(":checked"),
          sms: $("#editOptOutSms").is(":checked"),
          phone: $("#editOptOutPhone").is(":checked"),
          linkedIn: $("#editOptOutLinkedin").is(":checked"),
          whatsApp: $("#editOptOutWhatsapp").is(":checked"),
        },
        variables: variables,
      };

      $.ajax({
        url: `/api/contacts/${contactId}`,
        method: "PUT",
        contentType: "application/json",
        data: JSON.stringify(formData),
        success: function () {
          $("#singleContactEditModal").modal("hide");
          table.ajax.reload(null, false);
          showToast("Contact saved successfully", "primary");
          // Re-render DataTable without resetting pagination
        },
        error: function () {
          showToast(
            "Failed to save contact changes. Please try again.",
            "danger"
          );
        },
      });
    });
  });
</script>
<!-- Single contact Edit ends -->
