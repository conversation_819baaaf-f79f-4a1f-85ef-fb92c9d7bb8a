const express = require("express");
const cloudflare = require("../services/cloudflareService.js");
const router = express.Router();

// create new subdomain record
router.post("/subdomains", async (req, res) => {
  try {
    console.log("req.body", req.body);
    const result = await cloudflare.createSubdomain(req.body);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

//  List all subdomains
router.get("/subdomains", async (req, res) => {
  try {
    const result = await cloudflare.listSubdomains();
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

//  Read one
router.get("/subdomains/:id", async (req, res) => {
  try {
    const result = await cloudflare.getSubdomain(req.params.id);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

//  Update
router.put("/subdomains/:id", async (req, res) => {
  try {
    const result = await cloudflare.updateSubdomain(req.params.id, req.body);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

//  Delete
router.delete("/subdomains/:id", async (req, res) => {
  try {
    const result = await cloudflare.deleteSubdomain(req.params.id);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

router.post("/check", async (req, res) => {
  try {
    const { domain } = req.body;
    if (
      !domain ||
      typeof domain !== "string" ||
      !/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain)
    ) {
      return res
        .status(400)
        .json({ error: "Domain Missing or invalid format" });
    }
    const result = await cloudflare.checkDomain(domain);
    res.json(result);
  } catch (err) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

router.post("/verify", async (req, res) => {
  try {
    const { domain } = req.body;
    const result = await cloudflare.verifyDomain(domain);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
  
})

router.post("/ssl", async (req, res) => {
  try {
    const { domain } = req.body;
    const result = await cloudflare.setupSSL(domain);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: err.response?.data || err.message });
  }
});
module.exports = router;
