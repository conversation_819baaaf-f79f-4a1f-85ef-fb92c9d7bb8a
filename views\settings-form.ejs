<%- contentFor('HeaderCss') %> 
<%-include("partials/title-meta", { "title":"Forms" }) %> 

<%- contentFor('body') %>
<%-include("partials/page-title",{"title": "Form Settings", "pagetitle": "Settings" }) %>

<style>
  
  .table thead th,
  .table tbody td {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  
  .table tbody td {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  
  .custom-bordered-table {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
  }
</style>

<div class="form-check form-switch d-flex align-items-center gap-2">
  <input 
    type="checkbox" 
    id="enableDisableAllForms" 
    class="form-check-input" 
    style="cursor: pointer;"
  >
  <label 
    class="form-check-label text-dark fw-semibold" 
    for="enableDisableAllForms"
    style="user-select: none;"
  >
    Enable/Disable All Forms
  </label>
</div>


<div class="container-fluid px-2 py-2 mt-4">
  <div class="row justify-content-center">
    <div class="col-12">
      <div class="card shadow-md border  custom-bordered-table">
        <div class="card-header bg-white border-bottom-0 pb-0">
          <h4 class="mb-0">All Forms</h4>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover align-middle w-100 mb-0">
              <thead class="">
                <tr>
                  <th>Name</th>
                  <th>Status</th>
                  <th>Default</th>
                  <th>Email Notifications</th>
                  <th>Webhook</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="formsTableBody">
                <!-- Forms will be loaded here by jQuery -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<%- contentFor('FooterJs') %>

<script>
$(function() {
  var currentFormId = null;

  // Show/hide custom email field
  $(document).on('change', '#sendToCustom', function() {
    $('#customEmailGroup').toggle(this.checked);
  });

  $(document).on('change', '#enableDisableAllForms', function() {
    const isEnabled=$(this).is(':checked');
    $.ajax({
      url: '/api/forms/enable-disable-all',
      method: 'PATCH',
      contentType: 'application/json',
      data: JSON.stringify({ isEnabled: isEnabled }),
      success: function() {
        loadForms();
        showToast('All forms ' + (isEnabled ? 'enabled' : 'disabled') + ' successfully!', 'success');
      },
      error: function() {
        showToast('Error updating form status', 'danger');
      }
    });
  });

  // Load all forms on page load
  loadForms();

  function loadForms() {
    $.get('/api/forms', function(forms) {
   
      var rows = '';
      $.each(forms, function(i, form) {
        rows += `<tr>
          <td>${form.name}</td>
          <td>
            <div class='form-check form-switch'>
              <input type='checkbox' class='form-check-input toggle-enabled' data-id='${form._id}' ${form.isEnabled ? 'checked' : ''}>
            </div>
          </td>
          <td>
            <span class='badge ${form.isDefault ? 'bg-success' : 'bg-secondary'}'>${form.isDefault ? 'Default' : 'Not Default'}</span>
          </td>
          <td>
            <div class='small'>
              ${form.emailNotifications.sendToCustom ? '<div><i class="bx bx-check"></i> Custom Email</div>' : ''}
              ${form.emailNotifications.sendToCandidate ? '<div><i class="bx bx-check"></i> Candidate</div>' : ''}
              ${!form.emailNotifications.sendToCustom && !form.emailNotifications.sendToCandidate ? '<div class="text-muted">No notifications</div>' : ''}
            </div>
          </td>
          <td>${form.webhookUrl ? '<span class="badge bg-info">Configured</span>' : '<span class="text-muted">Not set</span>'}</td>
          <td><small class='text-muted'>${new Date(form.createdAt).toLocaleDateString()}</small></td>
          <td>
            <button class='btn btn-sm btn-primary edit-form' data-id='${form._id}'>Edit</button>
          ${!form.isDefault ? `<button class='btn btn-sm btn-primary set-default' data-id='${form._id}'>Set Default</button>` : ''}
           
            <button class='btn btn-sm btn-danger delete-form' data-id='${form._id}'>Delete</button>
          </td>
        </tr>`;
      });
      $('#formsTableBody').html(rows);
    });
  }

  // Show form for add
  $(document).on('click', '[data-bs-target="#formModal"]', function() {
    resetForm();
    currentFormId = null;
    $('#formModalLabel').text('Add New Form');
    $('#formModal').modal('show');
  });

  // Show form for edit
  $(document).on('click', '.edit-form', function() {
    var id = $(this).data('id');
    $.get('/api/forms/' + id, function(form) {
      currentFormId = form._id;
      $('#formId').val(form._id);
      $('#formName').val(form.name);
      $('#isEnabled').prop('checked', form.isEnabled);
      if(form.isDefault) {
        $('#isDefaultYes').prop('checked', true);
      } else {
        $('#isDefaultNo').prop('checked', true);
      }
      $('#webhookUrl').val(form.webhookUrl || '');
      $('#sendToCustom').prop('checked', form.emailNotifications.sendToCustom);
      $('#customEmail').val(form.emailNotifications.customEmail || '');
      $('#sendToCandidate').prop('checked', form.emailNotifications.sendToCandidate);
      $('#customEmailGroup').toggle(form.emailNotifications.sendToCustom);
      $('#formModalLabel').text('Edit Form');
      $('#formModal').modal('show');
    });
  });

  // Save form (add or edit)
  $('#formSettingsForm').submit(function(e) {
    e.preventDefault();
    const saveFormBtn=$('#saveFormBtn')
    const originalText=saveFormBtn.text();
    saveFormBtn.prop('disabled',true);
    saveFormBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');
    var formObj = {
      name: $('#formName').val(),
      isEnabled: $('#isEnabled').is(':checked'),
      isDefault: $('input[name="isDefault"]:checked').val() === 'true',
      webhookUrl: $('#webhookUrl').val(),
      emailNotifications: {
        sendToCustom: $('#sendToCustom').is(':checked'),
        customEmail: $('#customEmail').val(),
        sendToCandidate: $('#sendToCandidate').is(':checked')
      }
    };
    var url = '/api/forms' + (currentFormId ? '/' + currentFormId : '');
    var method = currentFormId ? 'PUT' : 'POST';
    $.ajax({
      url: url,
      method: method,
      contentType: 'application/json',
      data: JSON.stringify(formObj),
      success: function() {
        $('#formModal').modal('hide');
        loadForms();
        resetForm();
        showToast('Form saved successfully!', 'success');
        saveFormBtn.prop('disabled',false);
        saveFormBtn.html(originalText);
      },
      error: function(xhr) {
        showToast(xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Error saving form', 'danger');
        saveFormBtn.prop('disabled',false);
        saveFormBtn.html(originalText);
      }
    });
  });

  // Toggle isEnabled
  $(document).on('change', '.toggle-enabled', function() {
    var id = $(this).data('id');
    
    var isEnabled = $(this).is(':checked');
    $.ajax({
      url: '/api/forms/' + id + '/enable-disable',
      method: 'PATCH',
      contentType: 'application/json',
      data: JSON.stringify({ isEnabled: isEnabled }),
      success: function() {
        loadForms();
        showToast('Form status updated!', 'success');
      },
      error: function() {
        showToast('Error updating status', 'danger');
      }
    });
  });

  // Set as default
  $(document).on('click', '.set-default', function() {
    var id = $(this).data('id');
    const originalText=$(this).text();
    $(this).prop('disabled',true);
    $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');
    $.ajax({
      url: '/api/forms/' + id + '/toggle-default',
      method: 'PATCH',
      contentType: 'application/json',
      data: JSON.stringify({ isDefault: true }),
      success: function() {
        loadForms();
        showToast('Form set as default!', 'success');
        $(this).prop('disabled',false);
        $(this).html(originalText);
      },
      error: function() {
        showToast('Error setting default', 'danger');
        $(this).prop('disabled',false);
        $(this).html(originalText);
      }
    });
  });

  // Delete form
  $(document).on('click', '.delete-form', function() {
    if(!confirm('Are you sure you want to delete this form?')) return;
    var id = $(this).data('id');
    const originalText=$(this).text();
    $(this).prop('disabled',true);
    $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...');
    $.ajax({
      url: '/api/forms/' + id,
      method: 'DELETE',
      success: function() {
        loadForms();
        showToast('Form deleted!', 'success');
        $(this).prop('disabled',false);
        $(this).html(originalText);
      },
      error: function() {
        showToast('Error deleting form', 'danger');
        $(this).prop('disabled',false);
        $(this).html(originalText);
      }
    });
  });

  // Reset form
  function resetForm() {
    $('#formSettingsForm')[0].reset();
    $('#formId').val('');
    $('#customEmailGroup').hide();
    currentFormId = null;
  }

  // Show alert
  

  // Reset form on modal close
  $('#formModal').on('hidden.bs.modal', function() {
    resetForm();
  });
});
</script>


