const mongoose = require("mongoose");

// Schema for individual font variants (Regular, Bold, Italic, etc.)
const fontVariantSchema = new mongoose.Schema({
  variant: {
    type: String,
    required: true,
    enum: [
      "Regular",
      "Bold",
      "Italic",
      "Bold Italic",
      "Light",
      "Medium",
      "SemiBold",
      "ExtraBold",
      "Black",
    ],
  },
  fileUrl: { type: String, required: true },
  fileName: { type: String, required: true },
  fileSize: { type: Number }, // in bytes
  fontWeight: { type: String }, // 100, 200, 300, 400, 500, 600, 700, 800, 900
  fontStyle: {
    type: String,
    enum: ["normal", "italic", "oblique"],
    default: "normal",
  },
});

const customFontSchema = new mongoose.Schema({
  name: { type: String, required: true },
  fontFamily: { type: String, required: true },

  fileUrl: { type: String },

  
  variants: [fontVariantSchema],

  uploadType: {
    type: String,
    enum: ["single", "zip"],
    default: "single",
  },
  zipFileUrl: { type: String },


  previewText: {
    type: String,
    default: "The quick brown fox jumps over the lazy dog",
  },

  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  businessId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Business",
    required: true,
  },
  createdAt: { type: Date, default: Date.now },
});

// Index for faster queries
customFontSchema.index({ businessId: 1, name: 1 });

module.exports = mongoose.model("customFont", customFontSchema);
