// routes/fontRoutes.js
const express = require("express");
const router = express.Router();
const customFontController = require("../controllers/customFontController");
const { checkPermissions } = require("../middleware/middleware");

router.post(
  "/",
  checkPermissions("Font", ["create"]),
  customFontController.uploadFont
);
router.post(
  "/upload-zip",
  checkPermissions("Font", ["create"]),
  customFontController.uploadFontZip
);
router.get(
  "/",
  checkPermissions("Font", ["read"]),
  customFontController.getAllFonts
);
router.get(
  "/:id",
  checkPermissions("Font", ["read"]),
  customFontController.getFontById
);
router.delete(
  "/:id",
  checkPermissions("Font", ["delete"]),
  customFontController.deleteFont
);

module.exports = router;
