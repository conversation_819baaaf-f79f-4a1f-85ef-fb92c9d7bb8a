//const { createCanvas, loadImage } = require('canvas');

//import system libs
const path = require("path");

//import npms
const { exec } = require("child_process");
const { PDFDocument, rgb, StandardFonts } = require("pdf-lib");
const puppeteer = require("puppeteer");
const nodemailer = require("nodemailer"); // For sending emails
const { v4: uuidv4 } = require("uuid");
const axios = require("axios");
//import Models
const Notification = require("../models/Notification"); // Adjust path as needed
const Contact = require("../models/Contact");
const Design = require("../models/Design");
const Event = require("../models/Event");
const Job = require("../models/Job");
const CertificateTemplate = require("../models/CertificateTemplate");
const Certificate = require("../models/Certificate");
const sendEmail = require("../utils/email");
const Webhook = require("../models/Webhook");
const { createLog } = require("../controllers/logController");
// Create a new certificate template
exports.createCertificateTemplate = async (req, res) => {
  console.log("req.body", req.body);
  try {
    const template = new CertificateTemplate({
      ...req.body,
      creatorId: req.user._id, // Set the creatorId to the current user's ID
    });

    console.log("certificate template", template);

    await template.save();
    res.status(201).json(template);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get all certificate templates
exports.getAllCertificateTemplates = async (req, res) => {
  try {
    const templates = await CertificateTemplate.find();
    res.status(200).json(templates);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Get a single certificate template by ID
exports.getCertificateTemplateById = async (req, res) => {
  try {
    const template = await CertificateTemplate.findById(req.params.id);
    if (!template) {
      return res.status(404).json({ error: "Template not found" });
    }
    res.status(200).json(template);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Update a certificate template by ID
exports.updateCertificateTemplate = async (req, res) => {
  try {
    const template = await CertificateTemplate.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!template) {
      return res.status(404).json({ error: "Template not found" });
    }
    res.status(200).json(template);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Delete a certificate template by ID
exports.deleteCertificateTemplate = async (req, res) => {
  try {
    const template = await CertificateTemplate.findByIdAndDelete(req.params.id);
    if (!template) {
      return res.status(404).json({ error: "Template not found" });
    }
    res.status(200).json({ message: "Template deleted successfully" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Search certificate templates by criteria
exports.searchCertificateTemplates = async (req, res) => {
  try {
    const { certificateName, startDate, endDate, location } = req.query;
    const query = {};

    if (certificateName) {
      query.certificateName = new RegExp(certificateName, "i"); // Case-insensitive search
    }
    if (startDate) {
      query.startDate = { $gte: new Date(startDate) };
    }
    if (endDate) {
      query.endDate = { $lte: new Date(endDate) };
    }
    if (location) {
      query.location = new RegExp(location, "i"); // Case-insensitive search
    }

    const templates = await CertificateTemplate.find(query);
    res.status(200).json(templates);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// certificateTemplateController.js
exports.generateBulkCertificates = async (req, res) => {
  try {
    const { designId, recipients, event } = req.body;

    const design = await Design.findById(designId);
    if (!design) throw new Error("Design not found");

    const certificates = [];

    for (const recipient of recipients) {
      const foundContact = await Contact.findById(recipient._id);
      if (!foundContact) continue;

      const filePaths = await generatePDFAndImage(design, foundContact, req);

      const certificate = new Certificate({
        contactId: foundContact._id,
        certificateName: design.designName,
        certificateId: generateCertificateId(),
        pdfURL: filePaths.pdfURL,
        imageURL: filePaths.imageURL,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      });

      await certificate.save();
      certificates.push(certificate);

      foundContact.certificates.push(certificate._id);
      await foundContact.save();

      await Event.findByIdAndUpdate(event._id, {
        $push: { certificates: certificate._id },
      });

      console.log(
        "shouldCertificateBeSentToRecipient",
        req.shouldCertificateBeSentToRecipient
      );
      if (req.shouldCertificateBeSentToRecipient)
        await sendCertificateEmail(foundContact, certificate);

      const generatedWebhooks = await Webhook.find({
        isEnabled: true,
        triggers: { $in: ["certificate.issued"] },
      });

      if (generatedWebhooks.length > 0) {
        const webhookPromises = generatedWebhooks.map((webhook) =>
          sendWebhookWithRetry(
            webhook.url,
            {
              event: "certificate.generated",
              timestamp: new Date().toISOString(),
              data: {
                certificateId: certificate._id,
                candidateId: foundContact._id,
                eventId: event._id,
                designId,
              },
            },
            webhook.secret || null
          )
        );
        await Promise.all(webhookPromises);
      }
      console.log(req, "reqgeetam");
      await createLog(
        {
          eventType: "Certificate",
          action: "Generate",
          target: certificate._id,
        },
        {
          user: {
            _id: req.user?._id,
            businessId: req.user?.businessId,
            role: req.user?.role,
          },
          ip: req.ip,
          headers: req.headers,
        },
        res
      );
    }

    await Event.findByIdAndUpdate(event._id, {
      $set: { status: "Completed" },
    });

    createCertificateNotification(
      req.user,
      certificates,
      req.user.businessId,
      event,
      req.shouldCertificateBeSentToRecipient
    );
    const bulkGenerationWebhook = await Webhook.find({
      isEnabled: true,
      triggers: { $in: ["certificate.bulk_generated"] },
    });

    if (bulkGenerationWebhook.length > 0) {
      const webhookPromises = bulkGenerationWebhook.map((webhook) =>
        sendWebhookWithRetry(
          webhook.url,
          {
            event: "certificate.bulk_generated",
            timestamp: new Date().toISOString(),
            data: {
              certificateId: certificates.map((cert) => cert._id).join(","),
              candidateId: recipients
                .map((recipient) => recipient._id)
                .join(","),
              eventId: event._id,
              designId,
            },
          },
          webhook.secret || null
        )
      );
      await Promise.all(webhookPromises);
    }
  } catch (error) {
    console.error("Error creating bulk cert generation", error.message);
    throw new Error(error.message);
  }
};

//generatebUlkCertififcateStarts
// Generate bulk certificates
exports.generateBulkCertificatesOld = async (req, res) => {
  try {
    const { designId, recipients } = req.body;

    // Fetch the design from the database
    const design = await Design.findById(designId);
    if (!design) {
      //design not found, throw notification and email error
      return res.status(404).json({ error: "Design not found" });
    }

    const certificates = [];

    for (const recipient of recipients) {
      const foundContact = await Contact.findById(recipient._id);
      if (!foundContact) {
        continue; // Skip if the contact isn't found
      }

      // Generate the certificate PDF and image
      const filePaths = await generatePDFAndImage(design, foundContact, req);

      // Save the certificate information to the database
      const certificate = new Certificate({
        contactId: foundContact._id,
        certificateName: design.designName,
        certificateId: generateCertificateId(),
        pdfURL: filePaths.pdfURL,
        imageURL: filePaths.imageURL,
        createdBy: req.user._id,
        updatedBy: req.user._id,
      });

      await certificate.save();
      certificates.push(certificate);

      // Send the certificate via email
      await sendCertificateEmail(foundContact, certificate);
    }

    res.status(200).json({
      message: "Certificates generated and emails sent successfully",
      certificates,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Function to generate PDF and Image of the certificate
async function generatePDFAndImage(design, recipient, req) {
  // Launch Puppeteer with additional settings for better rendering
  const browser = await puppeteer.launch({
    executablePath: process.env.EXECUTABLE_PATH,
    headless: true,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-web-security",
      "--allow-file-access-from-files",
    ],
    defaultViewport: null, // Let the viewport be determined by the page
  });

  const page = await browser.newPage();

  // Optionally, set session cookies if using session-based auth
  // const cookies = req.headers.cookie;  // Extract session cookies

  // console.log("req.headers.cookie", req.headers.cookie)

  // if (cookies) {
  //     const parsedCookies = cookies.split(';').map(cookie => {
  //         const [name, value] = cookie.split('=').map(str => str.trim());  // Trim spaces around name and value
  //         return {
  //             name,
  //             value,
  //             domain: '.mixcommerce.co',  // Leading dot allows cookies on subdomains
  //             path: '/',                  // Default path (applies to all pages)
  //             secure: false,              // Optional: set to true if you're serving cookies over HTTPS
  //             httpOnly: false,            // Optional: set based on your needs
  //         };
  //     });
  //     await page.setCookie(...parsedCookies);
  //     console.log("parsedCookies", parsedCookies)
  // }

  // Prepare the HTML page content with Fabric.js embedded, pass design data directly
  const htmlContent = getFabricHTML(design, recipient);
  console.log("htmlContent", htmlContent);

  // Enable request interception to handle image loading issues
  await page.setRequestInterception(true);
  page.on("request", (request) => {
    // Allow all requests to proceed
    request.continue();
  });

  // Log any resource loading errors
  page.on("console", (msg) => {
    if (msg.type() === "error") {
      console.log("Page error:", msg.text());
    }
  });

  // Set the content with extended timeout and wait for all resources
  await page.setContent(htmlContent, {
    waitUntil: ["load", "domcontentloaded", "networkidle0"],
    timeout: 60000, // Extended timeout (60 seconds)
  });

  // Wait for the canvas to finish rendering with a custom timeout of 60 seconds
  await page.waitForFunction(() => window.canvasLoaded === true, {
    timeout: 60000,
  });

  // Set the viewport to ensure proper scaling
  await page.setViewport({
    width: 1600,
    height: 1120,
    deviceScaleFactor: 2, // This helps with quality (effectively 2x the resolution)
  });

  // Modify the page to ensure the certificate is rendered exactly as designed
  await page.evaluate(() => {
    const canvas = document.getElementById("fabricCanvas");

    // Remove the border to eliminate extra space
    canvas.style.border = "none";

    // Make sure the body has no margins or padding
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.overflow = "hidden";
    document.body.style.backgroundColor = "white";

    // Set the canvas to maintain its exact dimensions
    canvas.style.display = "block";

    // Center the canvas in the page
    document.body.style.display = "flex";
    document.body.style.justifyContent = "center";
    document.body.style.alignItems = "center";

    // Ensure all canvas content is visible
    const objects = canvas.getObjects ? canvas.getObjects() : [];
    for (let obj of objects) {
      if (obj && typeof obj.set === "function") {
        obj.set("visible", true);
      }
    }

    // Force a re-render to ensure all elements are displayed
    if (typeof canvas.renderAll === "function") {
      canvas.renderAll();
    }
  });

  // Generate PDF and save it to a file
  const pdfFilename = `${recipient._id}-${Date.now()}.pdf`;
  const pdfPath = path.join(__dirname, "../public/certificates", pdfFilename);

  // Wait to ensure all images and elements are fully loaded
  await page.waitForFunction("window.canvasLoaded === true", {
    timeout: 10000,
  });

  // Additional wait to ensure all images are loaded
  await page.evaluate(() => {
    return new Promise((resolve) => {
      // Check if all images are loaded
      const checkImages = () => {
        const images = Array.from(document.querySelectorAll("img"));
        const fabricCanvas = document.getElementById("fabricCanvas");

        // Get all fabric objects that are images
        let fabricImages = [];
        if (
          fabricCanvas &&
          fabricCanvas.fabric &&
          fabricCanvas.fabric.getObjects
        ) {
          fabricImages = fabricCanvas.fabric
            .getObjects()
            .filter((obj) => obj.type === "image");
        }

        // Check if all regular images are loaded
        const allImagesLoaded = images.every((img) => img.complete);

        // Check if all fabric images are loaded
        const allFabricImagesLoaded = fabricImages.every(
          (img) => img._element && img._element.complete && img.visible === true
        );

        if (allImagesLoaded && allFabricImagesLoaded) {
          resolve();
        } else {
          // Check again after a short delay
          setTimeout(checkImages, 100);
        }
      };

      // Start checking
      checkImages();
    });
  });

  // Wait for the canvas to be fully loaded
  await page.waitForFunction("window.canvasLoaded === true", {
    timeout: 30000,
  });

  // Additional delay to ensure rendering is complete
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Force a final render and check all images
  await page.evaluate(() => {
    const canvas = document.getElementById("fabricCanvas");
    if (canvas.fabric) {
      // Check all images are loaded
      const images = canvas.fabric
        .getObjects()
        .filter((obj) => obj.type === "image");
      console.log(`Found ${images.length} images on canvas`);

      // Force reload any images that aren't complete
      images.forEach((img) => {
        if (img._element && !img._element.complete) {
          console.log("Reloading incomplete image:", img.getSrc());
          const src = img.getSrc();
          fabric.Image.fromURL(
            src,
            function (newImg) {
              if (newImg) {
                newImg.set({
                  left: img.left,
                  top: img.top,
                  scaleX: img.scaleX,
                  scaleY: img.scaleY,
                  angle: img.angle,
                  clipPath: img.clipPath,
                  visible: true,
                  opacity: img.opacity !== undefined ? img.opacity : 1,
                });
                canvas.fabric.remove(img);
                canvas.fabric.add(newImg);
              }
            },
            { crossOrigin: "anonymous" }
          );
        }
      });

      // Final render
      canvas.fabric.renderAll();
    }
  });

  // One more delay to ensure everything is rendered
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Get the canvas dimensions for precise PDF sizing
  const dimensions = await page.evaluate(() => {
    const canvas = document.getElementById("fabricCanvas");

    // Ensure all objects are visible and properly rendered
    const fabricCanvas = canvas.fabric;
    if (fabricCanvas && typeof fabricCanvas.renderAll === "function") {
      fabricCanvas.renderAll();
    }

    // Use the stored dimensions if available, otherwise get from canvas
    if (window.canvasDimensions) {
      return window.canvasDimensions;
    } else {
      return {
        width: canvas.width,
        height: canvas.height,
      };
    }
  });

  // Ensure the page is properly sized before generating PDF
  await page.evaluate((dimensions) => {
    // Set the body and container to exact dimensions
    document.body.style.width = dimensions.width + "px";
    document.body.style.height = dimensions.height + "px";
    document.body.style.overflow = "hidden";

    // Center the canvas
    const container = document.getElementById("canvas-container");
    if (container) {
      container.style.width = dimensions.width + "px";
      container.style.height = dimensions.height + "px";
      container.style.display = "flex";
      container.style.justifyContent = "center";
      container.style.alignItems = "center";
      container.style.margin = "0";
      container.style.padding = "0";
    }

    // Make sure the canvas is exactly sized
    const canvas = document.getElementById("fabricCanvas");
    if (canvas) {
      canvas.style.width = dimensions.width + "px";
      canvas.style.height = dimensions.height + "px";
      canvas.style.margin = "0 auto";
      canvas.style.display = "block";
    }

    console.log(
      "PDF dimensions set to:",
      dimensions.width,
      "x",
      dimensions.height
    );
  }, dimensions);

  // Ensure the canvas fills the entire page with no padding or margins
  await page.evaluate(() => {
    // Remove all padding and margins
    document.body.style.padding = "0";
    document.body.style.margin = "0";
    document.body.style.boxSizing = "border-box";
    document.body.style.width = "100%";
    document.body.style.height = "100%";

    // Make the canvas container fill the entire page
    const container = document.getElementById("canvas-container");
    if (container) {
      container.style.padding = "0";
      container.style.margin = "0";
      container.style.boxSizing = "border-box";
      container.style.overflow = "hidden";
      container.style.width = "100%";
      container.style.height = "100%";
    }

    // Make the canvas fill its container
    const canvas = document.getElementById("fabricCanvas");
    if (canvas) {
      canvas.style.padding = "0";
      canvas.style.margin = "0";
      canvas.style.width = "100%";
      canvas.style.height = "100%";
    }

    console.log("Removed all padding and margins for full-page PDF");
  });

  // Use exact dimensions to fill the entire page with no margins
  await page.pdf({
    path: pdfPath,
    width: dimensions.width + "px", // Use exact width
    height: dimensions.height + "px", // Use exact height
    printBackground: true,
    margin: {
      top: "0",
      right: "0",
      bottom: "0",
      left: "0",
    },
    scale: 1.0, // Use exact scale
    preferCSSPageSize: false,
    omitBackground: false,
    printBackground: true,
  });

  // Generate an image from the certificate (screenshot of the canvas)
  const imageFilename = `${recipient._id}-${Date.now()}.jpeg`;
  const imagePath = path.join(
    __dirname,
    "../public/certificates",
    imageFilename
  );

  // Take a screenshot with the exact dimensions of the canvas
  // Set the viewport to match the canvas dimensions with minimal padding
  await page.setViewport({
    width: Math.ceil(dimensions.width) + 40, // Add minimal padding to ensure nothing is cut off
    height: Math.ceil(dimensions.height) + 40,
    deviceScaleFactor: 2, // Balance between quality and performance
  });

  // Ensure the canvas is centered and visible
  await page.evaluate(() => {
    // Get the canvas
    const canvas = document.getElementById("fabricCanvas");

    // Center the canvas in the viewport with minimal padding
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.backgroundColor = "white";
    document.body.style.boxSizing = "border-box";
    document.body.style.overflow = "hidden";

    // Make sure the canvas is visible and properly sized with no extra margins
    canvas.style.display = "block";
    canvas.style.margin = "0 auto";
    canvas.style.padding = "0";
    canvas.style.boxSizing = "border-box";

    // Make sure all objects are visible but preserve opacity
    if (canvas.fabric) {
      canvas.fabric.getObjects().forEach((obj) => {
        // Store original opacity if it exists
        const originalOpacity = obj.opacity !== undefined ? obj.opacity : 1;

        obj.set({
          visible: true,
          opacity: originalOpacity, // Preserve original opacity
        });

        // Special handling for background images
        if (obj.type === "image") {
          console.log("Preserving image opacity:", originalOpacity);
        }
      });

      // Force a final render
      canvas.fabric.renderAll();
    }
  });

  // Wait a moment for rendering to complete
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Take a full page screenshot first
  const fullPageImagePath = path.join(
    __dirname,
    "../public/certificates",
    `full-${imageFilename}`
  );

  await page.screenshot({
    path: fullPageImagePath,
    type: "jpeg",
    fullPage: true,
    quality: 100,
  });

  // Take the screenshot of the canvas element with precise positioning
  const clipRect = await page.evaluate(() => {
    const canvas = document.getElementById("fabricCanvas");
    const rect = canvas.getBoundingClientRect();

    // Get the exact position of the canvas
    const computedStyle = window.getComputedStyle(canvas);
    const containerStyle = window.getComputedStyle(
      document.getElementById("canvas-container")
    );

    console.log("Canvas position:", rect);
    console.log("Canvas computed style - margin:", computedStyle.margin);
    console.log("Container computed style - padding:", containerStyle.padding);

    // Calculate exact position with no margins
    // Use exact dimensions to capture the entire canvas without white space
    return {
      x: rect.left,
      y: rect.top,
      width: rect.width,
      height: rect.height,
    };
  });

  // Ensure all images with opacity are properly rendered before taking screenshot
  await page.evaluate(() => {
    const canvas = document.getElementById("fabricCanvas");
    if (canvas.fabric) {
      // Final check to ensure background images with opacity are preserved
      canvas.fabric.getObjects().forEach((obj) => {
        if (
          obj.type === "image" &&
          obj.opacity !== undefined &&
          obj.opacity < 1
        ) {
          console.log("Final check - preserving image opacity:", obj.opacity);
          obj.dirty = true;
        }
      });
      canvas.fabric.renderAll();
    }
  });

  // Wait a moment for final rendering
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Take the screenshot with exact dimensions
  await page.screenshot({
    path: imagePath,
    type: "jpeg",
    fullPage: false,
    omitBackground: false, // Include the white background
    quality: 100, // Highest quality for JPEG
    clip: clipRect,
  });

  await browser.close();

  return {
    pdfURL: `/certificates/${pdfFilename}`,
    imageURL: `/certificates/${imageFilename}`,
    fullImageURL: `/certificates/full-${imageFilename}`,
  };
}

// Function to generate HTML for Fabric.js without AJAX or jQuery
function getFabricHTML(design, recipient) {
  return `
             <!DOCTYPE html>
                <html lang="en">

                <head>
                <meta charset="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="print-color-adjust" content="exact" />
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="color-scheme" content="normal" />
                <meta http-equiv="Content-Security-Policy" content="img-src * data: blob:; default-src * 'unsafe-inline' 'unsafe-eval' data: blob:;">
                <title>Certificate</title>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"
                    integrity="sha512-CeIsOAsgJnmevfCi2C7Zsyy6bQKi43utIjdA87Q0ZY84oDqnI0uwfM9+bKiIkI75lUeI00WG/+uJzOmuHlesMA=="
                    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
                <style>
                    @page {
                      size: A4 landscape;
                      margin: 0;
                    }

                    body,
                    html {
                      margin: 0;
                      padding: 0;
                      overflow: hidden;
                      width: 100%;
                      height: 100%;
                      -webkit-print-color-adjust: exact;
                      print-color-adjust: exact;
                      background-color: white;
                      box-sizing: border-box;
                    }

                    #canvas-container {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      width: 100%;
                      height: 100vh;
                      position: relative;
                      overflow: hidden;
                      padding: 0;
                      margin: 0;
                      box-sizing: border-box;
                    }

                    #fabricCanvas {
                      display: block;
                      margin: 0;
                      border: none !important;
                      box-shadow: none !important;
                      position: relative;
                      transform-origin: center center;
                      padding: 0;
                      box-sizing: border-box;
                      width: 100%;
                      height: 100%;
                    }

                    /* Ensure images are properly displayed */
                    img {
                      display: block;
                      max-width: none;
                      max-height: none;
                    }
                </style>
                </head>

                <body>
                <div id="canvas-container">
                    <canvas id="fabricCanvas"></canvas>
                </div>
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                                        const canvas = new fabric.Canvas('fabricCanvas');
                                        window.canvasLoaded = false;

                                        const designData = ${JSON.stringify(
                                          design.canvas
                                        )};
                                        const recipient = ${JSON.stringify(
                                          recipient
                                        )};

                                        // Flatten recipient object (including nested variables map)
                                        const flattened = {};
                                        for (const key in recipient) {
                                            if (key === 'variables' && typeof recipient[key] === 'object') {
                                                for (const varKey in recipient.variables) {
                                                    flattened[varKey] = recipient.variables[varKey];
                                                }
                                            } else if (typeof recipient[key] !== 'object') {
                                                flattened[key] = recipient[key];
                                            }
                                        }

                                        // Set canvas dimensions to match the design
                                        if (designData.width && designData.height) {
                                            // Calculate the aspect ratio
                                            const aspectRatio = designData.width / designData.height;

                                            // Use the original dimensions from the design to maintain exact layout
                                            // This ensures the certificate looks exactly like the original design
                                            const originalWidth = designData.width;
                                            const originalHeight = designData.height;

                                            // Set fixed dimensions for A4 landscape (297mm x 210mm)
                                            // Convert to pixels (assuming 96 DPI)
                                            const a4Width = 1123; // 297mm at 96 DPI
                                            const a4Height = 794; // 210mm at 96 DPI

                                            // Calculate dimensions that fit within A4 while maintaining aspect ratio
                                            // but prioritize using original dimensions when possible
                                            let newWidth, newHeight;

                                            // Use original dimensions if they're reasonable
                                            if (originalWidth <= a4Width && originalHeight <= a4Height) {
                                                console.log('Using original dimensions:', originalWidth, 'x', originalHeight);
                                                newWidth = originalWidth;
                                                newHeight = originalHeight;
                                            } else if (aspectRatio > a4Width / a4Height) {
                                                // Width constrained
                                                newWidth = a4Width;
                                                newHeight = newWidth / aspectRatio;
                                            } else {
                                                // Height constrained
                                                newHeight = a4Height;
                                                newWidth = newHeight * aspectRatio;
                                            }

                                            // Use full dimensions with no margins
                                            newWidth = Math.min(newWidth, a4Width);
                                            newHeight = Math.min(newHeight, a4Height);

                                            // Set canvas dimensions
                                            canvas.setWidth(newWidth);
                                            canvas.setHeight(newHeight);

                                            // Set the canvas element dimensions
                                            const canvasElement = document.getElementById('fabricCanvas');
                                            canvasElement.style.width = newWidth + 'px';
                                            canvasElement.style.height = newHeight + 'px';

                                            // Store the dimensions for later use
                                            window.canvasDimensions = {
                                                width: newWidth,
                                                height: newHeight
                                            };
                                        }

                                        // Check if the design has a background image and ensure it's loaded
                                        if (designData.backgroundImage && designData.backgroundImage.src) {
                                            fabric.Image.fromURL(designData.backgroundImage.src, function(img) {
                                                if (img) {
                                                    canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                                                        scaleX: canvas.width / img.width,
                                                        scaleY: canvas.height / img.height
                                                    });
                                                }
                                            }, { crossOrigin: 'anonymous' });
                                        }

                                        // Process all objects to ensure colors and properties are preserved
                                        if (designData.objects) {
                                            // First, create a map of all colors used in the design
                                            const colorMap = new Map();

                                            designData.objects.forEach(obj => {
                                                // Store all fill colors
                                                if (obj.fill && typeof obj.fill === 'string') {
                                                    colorMap.set(obj.fill, obj.fill);
                                                    console.log('Found fill color:', obj.fill);
                                                }

                                                // Store all stroke colors
                                                if (obj.stroke && typeof obj.stroke === 'string') {
                                                    colorMap.set(obj.stroke, obj.stroke);
                                                    console.log('Found stroke color:', obj.stroke);
                                                }

                                                // Check for gold/yellow border elements
                                                if (obj.type === 'rect' &&
                                                    (obj.fill === '#f0c14b' ||
                                                     obj.fill === '#ffd700' ||
                                                     obj.fill === 'gold' ||
                                                     (obj.fill && typeof obj.fill === 'string' && obj.fill.includes('gold')))) {
                                                    // This is likely a border element, make sure it's included
                                                    console.log('Found border element:', obj);
                                                }

                                                // Check for background images with opacity
                                                if (obj.type === 'image' && obj.opacity !== undefined && obj.opacity < 1) {
                                                    console.log('Found background image with opacity:', obj.opacity);
                                                    // Make sure these are preserved
                                                }
                                            });

                                            // Log all colors for debugging
                                            console.log('All colors in design:', Array.from(colorMap.keys()));
                                        }

                                    canvas.loadFromJSON(designData, function() {
                                // Calculate scale factors if dimensions changed
                                let scaleX = 1, scaleY = 1;
                                if (window.canvasDimensions && designData.width && designData.height) {
                                    // Calculate scale factors but check if they're very close to 1
                                    // If they're very close to 1, just use 1 to avoid tiny scaling issues
                                    const rawScaleX = window.canvasDimensions.width / designData.width;
                                    const rawScaleY = window.canvasDimensions.height / designData.height;

                                    // Only apply scaling if it's significantly different from 1
                                    scaleX = Math.abs(rawScaleX - 1) < 0.05 ? 1 : rawScaleX;
                                    scaleY = Math.abs(rawScaleY - 1) < 0.05 ? 1 : rawScaleY;

                                    console.log('Canvas scaling:', scaleX, scaleY);
                                }

                                // Store original positions of all objects before scaling
                                const originalPositions = {};
                                canvas.getObjects().forEach((obj, index) => {
                                    originalPositions[index] = {
                                        left: obj.left,
                                        top: obj.top,
                                        scaleX: obj.scaleX,
                                        scaleY: obj.scaleY
                                    };
                                });

                                // Process all objects including images and text
                                canvas.getObjects().forEach(function(obj, index) {
                                    // Store all original properties to ensure exact positioning and alignment
                                    const originalProps = {
                                        left: obj.left,
                                        top: obj.top,
                                        scaleX: obj.scaleX || 1,
                                        scaleY: obj.scaleY || 1,
                                        angle: obj.angle || 0,
                                        width: obj.width,
                                        height: obj.height,
                                        originX: obj.originX || 'left',
                                        originY: obj.originY || 'top',
                                        textAlign: obj.textAlign,
                                        charSpacing: obj.charSpacing,
                                        lineHeight: obj.lineHeight,
                                        fontSize: obj.fontSize,
                                        fontFamily: obj.fontFamily
                                    };

                                    // Apply scaling if needed and if it's significantly different from 1
                                    if ((scaleX !== 1 || scaleY !== 1) &&
                                        (Math.abs(scaleX - 1) > 0.05 || Math.abs(scaleY - 1) > 0.05)) {
                                        obj.set({
                                            scaleX: obj.scaleX * scaleX,
                                            scaleY: obj.scaleY * scaleY,
                                            left: obj.left * scaleX,
                                            top: obj.top * scaleY
                                        });
                                        console.log('Scaled object:', obj.type, 'from',
                                            originalPositions[index].left, originalPositions[index].top,
                                            'to', obj.left, obj.top);
                                    }

                                    // For text objects, ensure alignment properties are preserved
                                    if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                                        obj.set({
                                            originX: originalProps.originX,
                                            originY: originalProps.originY,
                                            textAlign: originalProps.textAlign,
                                            charSpacing: originalProps.charSpacing,
                                            lineHeight: originalProps.lineHeight,
                                            fontSize: originalProps.fontSize,
                                            fontFamily: originalProps.fontFamily
                                        });
                                        console.log('Preserved text alignment:', obj.text, 'Align:', obj.textAlign);
                                    }

                                    // Handle text objects with placeholders
                                    if (obj.text) {
                                        // Store original text properties to preserve alignment
                                        const originalProps = {
                                            textAlign: obj.textAlign,
                                            originX: obj.originX,
                                            originY: obj.originY,
                                            left: obj.left,
                                            top: obj.top,
                                            width: obj.width,
                                            fontSize: obj.fontSize,
                                            fontFamily: obj.fontFamily,
                                            fontWeight: obj.fontWeight,
                                            fontStyle: obj.fontStyle,
                                            lineHeight: obj.lineHeight,
                                            charSpacing: obj.charSpacing,
                                            styles: obj.styles
                                        };

                                        const matches = obj.text.match(/{{(.*?)}}/g);
                                        if (matches) {
                                            matches.forEach((placeholder) => {
                                                const key = placeholder.replace(/{{|}}/g, '').trim();
                                                // Check if the key exists in flattened data
                                                const value = flattened[key] !== undefined ? flattened[key] : '';
                                                // Replace the placeholder with the actual value
                                                obj.set('text', obj.text.replace(placeholder, value));
                                            });
                                        }

                                        // Restore original text properties to maintain alignment
                                        obj.set({
                                            textAlign: originalProps.textAlign,
                                            originX: originalProps.originX,
                                            originY: originalProps.originY,
                                            left: originalProps.left,
                                            top: originalProps.top,
                                            width: originalProps.width,
                                            fontSize: originalProps.fontSize,
                                            fontFamily: originalProps.fontFamily,
                                            fontWeight: originalProps.fontWeight,
                                            fontStyle: originalProps.fontStyle,
                                            lineHeight: originalProps.lineHeight,
                                            charSpacing: originalProps.charSpacing,
                                            styles: originalProps.styles
                                        });

                                        // Log text alignment for debugging
                                        console.log('Text alignment preserved:', obj.text, 'Align:', obj.textAlign, 'Origin:', obj.originX, obj.originY);
                                    }

                                    // Ensure all objects are visible
                                    obj.visible = true;

                                    // Make sure images are properly loaded
                                    if (obj.type === 'image') {
                                        // Preserve original image properties for scaling
                                        const originalProps = {
                                            left: obj.left,
                                            top: obj.top,
                                            scaleX: obj.scaleX || 1,
                                            scaleY: obj.scaleY || 1,
                                            angle: obj.angle || 0,
                                            width: obj.width,
                                            height: obj.height,
                                            clipPath: obj.clipPath,
                                            opacity: obj.opacity !== undefined ? obj.opacity : 1 // Preserve opacity
                                        };

                                        // Handle profile image replacement
                                        if (obj.getSrc) {
                                            const src = obj.getSrc();
                                            console.log('Processing image:', src, 'Opacity:', originalProps.opacity);

                                            // Check if this is a profile/placeholder image
                                            if (src && (src.includes('placeholder') || src.includes('profile') || src.includes('avatar'))) {
                                                console.log('Found profile/placeholder image:', src);
                                                // If this is a placeholder image, try to replace it with profile image
                                                if (recipient.profileImage) {
                                                    fabric.Image.fromURL(recipient.profileImage, function(img) {
                                                        if (img) {
                                                            // Maintain the original image properties
                                                            img.set({
                                                                left: originalProps.left,
                                                                top: originalProps.top,
                                                                scaleX: originalProps.scaleX,
                                                                scaleY: originalProps.scaleY,
                                                                angle: originalProps.angle,
                                                                clipPath: originalProps.clipPath,
                                                                opacity: originalProps.opacity, // Preserve opacity
                                                                selectable: false,
                                                                visible: true,
                                                                crossOrigin: 'anonymous'
                                                            });

                                                            // Replace the placeholder with the actual image
                                                            canvas.remove(obj);
                                                            canvas.add(img);
                                                            canvas.renderAll();
                                                        }
                                                    }, { crossOrigin: 'anonymous' });
                                                }
                                            } else {
                                                // For other images (logos, signatures, etc.), ensure they load properly
                                                fabric.Image.fromURL(src, function(img) {
                                                    if (img) {
                                                        // Maintain the original image properties
                                                        img.set({
                                                            left: originalProps.left,
                                                            top: originalProps.top,
                                                            scaleX: originalProps.scaleX,
                                                            scaleY: originalProps.scaleY,
                                                            angle: originalProps.angle,
                                                            clipPath: originalProps.clipPath,
                                                            opacity: originalProps.opacity, // Preserve opacity
                                                            selectable: false,
                                                            visible: true,
                                                            crossOrigin: 'anonymous'
                                                        });

                                                        // Replace the original with the reloaded image
                                                        canvas.remove(obj);
                                                        canvas.add(img);
                                                        canvas.renderAll();
                                                    }
                                                }, { crossOrigin: 'anonymous' });
                                            }
                                        }

                                        // Ensure all images are visible but preserve opacity
                                        obj.set({
                                            visible: true,
                                            dirty: true,
                                            crossOrigin: 'anonymous',
                                            opacity: originalProps.opacity // Keep original opacity
                                        });

                                        // Force image to load if it hasn't already
                                        if (obj._element && !obj._element.complete) {
                                            obj._element.crossOrigin = 'anonymous';
                                            obj._element.onload = function() {
                                                canvas.renderAll();
                                            };
                                        }
                                    }
                                });

                                // Ensure all elements of the certificate are visible
                                canvas.setBackgroundColor('white', canvas.renderAll.bind(canvas));

                                // Ensure all colors and text alignments are preserved exactly as they were defined
                                canvas.getObjects().forEach(obj => {
                                    // If the object has a fill color, make sure it's preserved exactly
                                    if (obj.fill && typeof obj.fill === 'string') {
                                        // Force the color to be exactly as defined in the original
                                        const originalFill = obj.fill;
                                        obj.set('fill', originalFill);
                                        console.log('Preserving fill color:', originalFill);
                                    }

                                    // If the object has a stroke color, make sure it's preserved exactly
                                    if (obj.stroke && typeof obj.stroke === 'string') {
                                        // Force the color to be exactly as defined in the original
                                        const originalStroke = obj.stroke;
                                        obj.set('stroke', originalStroke);
                                        console.log('Preserving stroke color:', originalStroke);
                                    }

                                    // For text objects, ensure alignment is preserved exactly
                                    if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                                        // Get the original text alignment from the design data
                                        const originalObj = designData.objects.find(o =>
                                            o.type === obj.type && o.left === obj.left && o.top === obj.top);

                                        if (originalObj) {
                                            // Force text alignment properties to match the original design
                                            obj.set({
                                                textAlign: originalObj.textAlign || obj.textAlign,
                                                originX: originalObj.originX || obj.originX,
                                                originY: originalObj.originY || obj.originY,
                                                fontSize: originalObj.fontSize || obj.fontSize,
                                                fontFamily: originalObj.fontFamily || obj.fontFamily,
                                                fontWeight: originalObj.fontWeight || obj.fontWeight,
                                                fontStyle: originalObj.fontStyle || obj.fontStyle,
                                                lineHeight: originalObj.lineHeight || obj.lineHeight,
                                                charSpacing: originalObj.charSpacing || obj.charSpacing
                                            });
                                            console.log('Restored text alignment from original design:', obj.text);
                                        }
                                    }
                                });

                                // Make sure background images with opacity are rendered correctly
                                canvas.getObjects().forEach(obj => {
                                    if (obj.type === 'image' && obj.opacity !== undefined) {
                                        console.log('Rendering image with opacity:', obj.opacity);
                                        obj.dirty = true;
                                    }
                                });
                                canvas.renderAll();

                                // Make sure all objects are visible and properly positioned
                                canvas.getObjects().forEach(obj => {
                                    // Store original opacity if it exists
                                    const originalOpacity = obj.opacity !== undefined ? obj.opacity : 1;

                                    obj.set({
                                        visible: true,
                                        dirty: true
                                        // Don't set opacity here to preserve original values
                                    });

                                    // If this is a border or decorative element, ensure it's visible
                                    if (obj.fill && (
                                        typeof obj.fill === 'string' && (
                                            obj.fill.includes('gold') ||
                                            obj.fill.includes('yellow') ||
                                            obj.fill === '#f0c14b' ||
                                            obj.fill === '#ffd700'
                                        )
                                    )) {
                                        obj.set({
                                            opacity: 1, // Full opacity for borders
                                            visible: true
                                        });
                                    }

                                    // Ensure all images are visible but preserve their original opacity
                                    if (obj.type === 'image') {
                                        obj.set({
                                            opacity: originalOpacity, // Use original opacity
                                            visible: true
                                        });
                                        console.log('Setting image opacity to:', originalOpacity);
                                    }
                                });

                                // Final positioning check to ensure everything is in the right place
                                canvas.getObjects().forEach(obj => {
                                    // For text objects, we need to be careful with positioning to preserve alignment
                                    if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
                                        // For text objects, we'll only adjust if they're completely outside the canvas
                                        if (obj.left < -obj.width) obj.set({ left: 0 });
                                        if (obj.top < -obj.height) obj.set({ top: 0 });
                                        if (obj.left > canvas.width + obj.width) {
                                            obj.set({ left: canvas.width - obj.width });
                                        }
                                        if (obj.top > canvas.height + obj.height) {
                                            obj.set({ top: canvas.height - obj.height });
                                        }

                                        // Log text position for debugging
                                        console.log('Final text position:', obj.text, 'Left:', obj.left, 'Top:', obj.top, 'Align:', obj.textAlign);
                                    } else {
                                        // For non-text objects, we can be more aggressive with positioning
                                        if (obj.left < 0) obj.set({ left: 0 });
                                        if (obj.top < 0) obj.set({ top: 0 });
                                        if (obj.left + obj.width * obj.scaleX > canvas.width) {
                                            obj.set({ left: canvas.width - obj.width * obj.scaleX });
                                        }
                                        if (obj.top + obj.height * obj.scaleY > canvas.height) {
                                            obj.set({ top: canvas.height - obj.height * obj.scaleY });
                                        }
                                    }

                                    // Ensure all objects are properly positioned
                                    obj.setCoords();
                                });

                                // Force a complete re-render
                                canvas.calcOffset();
                                canvas.renderAll();

                                // Log final canvas dimensions
                                console.log('Final canvas dimensions:', canvas.width, 'x', canvas.height);
                                console.log('Total objects on canvas:', canvas.getObjects().length);

                                // Wait for all images to load with improved reliability
                                const loadAllImages = () => {
                                    const imageObjects = canvas.getObjects().filter(obj => obj.type === 'image');
                                    console.log('Checking', imageObjects.length, 'images for loading completion');

                                    const allImagesLoaded = imageObjects.every(img => {
                                        const isLoaded = img._element && img._element.complete;
                                        if (!isLoaded) {
                                            console.log('Image still loading:', img.getSrc ? img.getSrc() : 'unknown source');
                                        }
                                        return isLoaded;
                                    });

                                    if (allImagesLoaded) {
                                        console.log('All images loaded successfully');
                                        canvas.renderAll();
                                        window.canvasLoaded = true;
                                    } else {
                                        console.log('Some images still loading, checking again in 100ms');
                                        setTimeout(loadAllImages, 100);
                                    }
                                };

                                // Start checking for image loading
                                loadAllImages();
                            });


                                        canvas.on('after:render', function() {
                                            window.canvasLoaded = true;
                                        });
                                    });
                </script>
                </body>

                </html>
  `;
}



// Helper function to send an email with the certificate
async function sendCertificateEmail(recipient, certificate) {
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    auth: {
      user: process.env.EMAIL_USERNAME,
      pass: process.env.EMAIL_PASSWORD,
    },
  });

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: recipient.businessEmail,
    subject: "Your Certificate",
    text: "Please find your certificate attached.",
    attachments: [
      {
        filename: path.basename(certificate.pdfURL),
        path: path.join(__dirname, "../public", certificate.pdfURL),
      },
    ],
  };

  await transporter.sendMail(mailOptions);
}

// Helper function to generate a unique certificate ID
function generateCertificateId() {
  return "CERT-" + Math.random().toString(36).substring(2, 11).toUpperCase();
}

//generatebUlkCertififcateEnds

async function createCertificateNotification(
  user,
  certificates,
  businessId,
  event,
  shouldCertificateBeSentToRecipient
) {
  //create a web app notification
  const notification = new Notification({
    type: "CertificateGeneration",
    title: "Bulk Certificate Generation Completed",
    message: `Certificates generated and emails sent successfully for ${certificates.length} recipients.`,
    url: `/events/${event._id}`, // Replace with the URL path to view certificates if available
    icon: "fa fa-paw", // Optional: specify an icon file name or path if desired
    //imageUrl: "bulk-generation-image.png", // Optional: specify an image URL or file path
    createdBy: user._id, // User who triggered the certificate generation
    createdFor: user._id, // User who should receive the notification (typically same as createdBy)
    businessId: businessId, // ID of the business associated with the event
  });

  //email sending process starts
  console.log(
    "post certification generation mail will be sent to admin",
    user.email,
    event
  );

  const options = {
    email: user.email, // The recipient's email address
    subject: "Certificate Generation Completed",
    message: `<p>Congratulations,</p>
                  <p>Your <a href="${
                    process.env.APP_HOST ||
                    "https://mixcertificate.mixcommerce.co"
                  }events/${event._id}">${
      event.title
    }</a> certificates have been successfully generated and sent.</p>
                  <p>Thank you,<br>The MixCertificate Team</p>`,
  };

  console.log("options data in email", options);
  //Create an email notification
  const sendEmail = async (options) => {
    // 1) Create a transporter

    console.log("options data in email", options);
    console.log(
      "Sending certificate job completion mail from EMAIL_USERNAME",
      process.env.EMAIL_USERNAME
    );

    //transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      // secure: true,
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    // 2) Define the email options
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: options.email,
      subject: options.subject,
      text: options.message,
      html: options.message,
    };

    // 3) Actually send the email
    await transporter
      .sendMail(mailOptions)
      .then("mail sent successfully to", mailOptions)
      .catch("mail sending failed", mailOptions);
  };

  try {
    await notification.save();
    // if (shouldCertificateBeSentToRecipient) await sendEmail(options);
    await sendEmail(options);
    console.log("Notification created successfully:", notification);
  } catch (error) {
    console.error("Error creating notification:", error.message);
  }
}

exports.generateCertificateForNewlyAddedContactAfterBulkGeneration = async (
  req,
  res
) => {
  try {
    const { contactId, eventId } = req.body;

    const existingEvent = await Event.findById(eventId);
    if (!existingEvent) {
      return res.status(404).json({ error: "Event not found" });
    }

    const design = await Design.findById(existingEvent.designId);
    if (!design) {
      return res.status(404).json({ error: "Design not found" });
    }

    const foundContact = await Contact.findById(contactId);
    if (!foundContact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    const filePaths = await generatePDFAndImage(design, foundContact, req);

    const certificate = new Certificate({
      contactId: foundContact._id,
      certificateName: design.designName,
      certificateId: generateCertificateId(),
      pdfURL: filePaths.pdfURL,
      imageURL: filePaths.imageURL,
      createdBy: req.user._id,
      updatedBy: req.user._id,
    });

    await certificate.save();

    foundContact.certificates.push(certificate._id);
    await foundContact.save();

    await Event.findByIdAndUpdate(existingEvent._id, {
      $push: { certificates: certificate._id },
      status: "Scheduled",
    });

    await sendCertificateEmail(foundContact, certificate);

    await Event.findByIdAndUpdate(existingEvent._id, {
      $set: { status: "Completed" },
    });

    createCertificateNotification(
      req.user,
      [certificate],
      req.user.businessId,
      existingEvent
    );

    const generatedWebhooks = await Webhook.find({
      isEnabled: true,
      triggers: { $in: ["candidate.added"] },
    });
    if (generatedWebhooks.length > 0) {
      const webhookBody = {
        event: "candidate.added",
        timeStamp: Date.now(),
        data: {
          candidateId: foundContact._id,
          certificateId: certificate._id,
          eventId: existingEvent._id,
          designId: design._id,
        },
      };
      const webhookPromises = generatedWebhooks.map((webhook) =>
        sendWebhookWithRetry(webhook.url, webhookBody, webhook.secret)
      );
      await Promise.all(webhookPromises);
    }

    return res
      .status(200)
      .json({ message: "Certificate generated successfully", certificate });
  } catch (error) {
    console.error(
      "Error in generateCertificateForNewlyAddedContactAfterBulkGeneration:",
      error.message
    );
    return res.status(500).json({
      error: "Failed to generate certificate",
      details: error.message,
    });
  }
};
async function sendWebhookWithRetry(url, body, signature = null, attempt = 1) {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    if (signature) {
      headers["X-Signature"] = signature;
    }

    await axios.post(url, body, { headers });

    console.log(`✅ Webhook sent successfully to ${url} (Attempt ${attempt})`);
  } catch (err) {
    console.error(
      `❌ Failed to send webhook to ${url} (Attempt ${attempt}):`,
      err.message
    );

    if (attempt < 3) {
      const delay = Math.pow(2, attempt) * 1000;
      console.log(`🔁 Retrying in ${delay / 1000} seconds...`);

      setTimeout(() => {
        sendWebhookWithRetry(url, body, signature, attempt + 1);
      }, delay);
    } else {
      console.error(`🛑 Max retries reached. Giving up on ${url}`);
    }
  }
}
