const rolePermissions = {
  Admin: {
    Activity: ["create", "read", "update", "delete"],
    // Agreement: ["create", "read", "update", "delete"],
    // Background: ["create", "read", "update", "delete"],
    Badge: ["create", "read", "update", "delete"],
    BadgeTemplate: ["create", "read", "update", "delete"],
    Business: ["create", "read", "update", "delete"],
    // Call: ["create", "read", "update", "delete"],
    // Campaign: ["create", "read", "update", "delete"],
    Certificate: ["create", "read", "update", "delete", "export"],
    CertificateTemplate: ["create", "read", "update", "delete"],
    // Comment: ["create", "read", "update", "delete"],
    // Company: ["create", "read", "update", "delete"],
    Contact: ["create", "read", "update", "delete", "import", "export"],
    ContactList: ["create", "read", "update", "delete", "import", "export"],
    Dashboard: ["read"],
    // Deal: ["create", "read", "update", "delete"],
    // DealPipeline: ["create", "read", "update", "delete"],
    Design: ["create", "read", "update", "delete"],
    // Element: ["create", "read", "update", "delete"],
    // Email: ["create", "read", "update", "delete"],
    // EmailSequence: ["create", "read", "update", "delete"],
    Event: ["create", "read", "update", "delete"],
    File: ["create", "read", "update", "delete"],
    Form: ["create", "read", "update", "delete"],
    ImportHistory: ["create", "read", "delete"],
    Invoice: ["create", "read", "update", "delete", "export"],
    Log: ["create", "read", "delete"],
    // Meeting: ["create", "read", "update", "delete"],
    // Note: ["create", "read", "update", "delete"],
    Notification: ["create", "read", "update", "delete"],
    Plan: ["create", "read", "update", "delete"],
    // Product: ["create", "read", "update", "delete"],
    // Proposal: ["create", "read", "update", "delete"],
    // Quote: ["create", "read", "update", "delete"],
    Search: ["read"],
    // Segment: ["create", "read", "update", "delete"],
    // Service: ["create", "read", "update", "delete"],
    SMTP: ["create", "read", "update", "delete"],
    // Task: ["create", "read", "update", "delete"],
    // Template: ["create", "read", "update", "delete"],
    Ticket: ["create", "read", "update", "delete"],
    Visitor: ["read"],
    User: ["create", "read", "update", "delete"],
    // Workflow: ["create", "read", "update", "delete"],
    Backup: ["read", "create", "update", "delete"],
    PublicMenu: ["create", "read", "update", "delete"],
    FAQ: ["create", "read", "update", "delete"],
    CustomVariable: ["create", "read", "update", "delete"],
    Discount: ["create", "read", "update", "delete"],
    Payment: ["create", "read", "update", "delete"],
    Font: ["create", "read", "update", "delete"],
    ApiKey: ["create", "read", "update", "delete"],
  },

  Manager: {
    // Activity: ["create", "read"],
    // Agreement: ["create", "read", "update"], // removed: not part of Admin’s allowed resources
    // Background: ["create", "read", "update"], // removed
    Badge: ["create", "read", "update"],
    BadgeTemplate: ["create", "read", "update"],
    // Business: ["read"],
    // Call: ["create", "read", "update"], // removed
    // Campaign: ["create", "read", "update"], // removed
    Certificate: ["create", "read", "update", "export"],
    CertificateTemplate: ["create", "read", "update"],
    // Comment: ["create", "read", "update"], // removed
    // Company: ["create", "read", "update"], // removed
    Contact: ["create", "read", "update", "import", "export"],
    ContactList: ["create", "read", "update", "import", "export"],
    Dashboard: ["read"],
    // Deal: ["create", "read", "update"], // removed
    // DealPipeline: ["create", "read", "update"], // removed
    Design: ["create", "read", "update", "delete"],
    // Element: ["create", "read", "update"], // removed
    // Email: ["create", "read", "update"], // removed
    // EmailSequence: ["create", "read", "update"], // removed
    Event: ["create", "read", "update"],
    File: ["create", "read", "update", "delete"],
    Form: ["create", "read", "update"],
    ImportHistory: ["read"],
    // Invoice: ["create", "read", "update", "export"],
    // Log: ["read"],
    // Meeting: ["create", "read", "update"], // removed
    // Note: ["create", "read", "update"], // removed
    Notification: ["read"],
    // Plan: ["create", "read", "update"],
    // Product: ["create", "read", "update"], // removed
    // Proposal: ["create", "read", "update"], // removed
    // Quote: ["create", "read", "update"], // removed
    Search: ["read"],
    // Segment: ["create", "read", "update"], // removed
    // Service: ["create", "read", "update"], // removed
    // SMTP: ["create", "read", "update"],
    // Task: ["create", "read", "update"], // removed
    // Template: ["create", "read", "update"], // removed
    Ticket: ["create", "read", "update"],
    Visitor: ["read"],
    User: ["create","read","update","delete"],
    // Workflow: ["create", "read", "update"], // removed
    // Backup: ["read", "create", "update"],
    FAQ: ["create", "read", "update"],
    PublicMenu: ["read"],
    CustomVariable: ["create", "read", "update", "delete"],
    Discount: ["create", "read", "update", "delete"],
    Payment: ["create", "read", "update", "delete"],
    Font: ["create", "read", "update", "delete"],
    ApiKey: ["create", "read", "update", "delete"],
  },

  Editor: {
    // Activity: ["read", "update"],
    // Agreement: ['read', 'update'], // removed
    // Background: ["read", "update"], // removed
    Badge: ["read", "update"],
    BadgeTemplate: ["read", "update"],
    // Business: ["read", "update"],
    // Call: ["read", "update"], // removed
    // Campaign: ["read", "update"], // removed
    // Certificate: ["read", "update", "export"],
    CertificateTemplate: ["read", "update"],
    // Comment: ['read', 'update'], // removed
    // Company: ["read", "update"], // removed
    // Contact: ["read", "update", "import"],
    // ContactList: ["read", "update", "import"],
    Dashboard: ["read"],
    // Deal: ['read', 'update'], // removed
    // DealPipeline: ['read', 'update'], // removed
    // Design: ['read', 'update'], // removed
    // Instead, since "Design" is in Admin we could consider uncommenting it if appropriate.
    // Element: ["read", "update"], // removed – Admin does not include Element
    // Email: ["read", "update"], // removed
    // EmailSequence: ["read", "update"], // removed
    Event: ["read"],
    File: ["create", "read"],
    Form: ["read", "update"],
    // ImportHistory: ["read"],
    // Invoice: ["read", "update", "export"],
    // Log: ["read"],
    // Meeting: ['read', 'update'], // removed
    // Note: ["read", "update"], // removed
    Notification: ["read"],
    // Plan: ["read", "update"],
    // Product: ["read", "update"], // removed
    // Proposal: ['read', 'update'], // removed
    // Quote: ['read', 'update'], // removed
    Search: ["read"],
    // Segment: ['read', 'update'], // removed
    // Service: ["read", "update"], // removed
    // SMTP: ["read", "update"],
    // Task: ["read", "update"], // removed
    // Template: ["read", "update"], // removed
    Ticket: ["create", "read", "update"],
    Visitor: ["read"],
    User: ["read"],
    // Workflow: ["read", "update"], // removed
    // Backup: ["read", "update"],
    FAQ: ["read", "update"],
    PublicMenu: ["read"],
    CustomVariable: ["create", "read", "update", "delete"],
    Discount: ["create", "read", "update", "delete"],
    Payment: ["create", "read", "update", "delete"],
    Font: ["create", "read", "update", "delete"],
    ApiKey: ["create", "read", "update", "delete"],
  },

  Viewer: {
    // Activity: ['read'], // removed – not in the active list for Viewer based on Admin's selection
    // Agreement: ['read'], // removed
    // Background: ['read'], // removed
    Badge: ["read"],
    // BadgeTemplate: ['read'], // removed
    // Business: ['read'], // removed
    // Call: ['read'], // removed
    // Campaign: ['read'], // removed
    Certificate: ["read"],
    // CertificateTemplate: ['read'], // removed
    // Comment: ['read'], // removed
    // Company: ['read'], // removed
    // Contact: ['read'], // removed
    // ContactList: ['read'], // removed
    // Dashboard: ['read'], // removed
    // Deal: ['read'], // removed
    // DealPipeline: ['read'], // removed
    // Design: ['read'], // removed
    // Element: ['read'], // removed
    // Email: ['read'], // removed
    // EmailSequence: ['read'], // removed
    // Event: ['read'], // removed
    // File: ['read'], // removed
    // Form: ['read'], // removed
    // ImportHistory: ['read'], // removed
    // Invoice: ['read', 'export'], // removed
    // Log: ['read'], // removed
    // Meeting: ['read'], // removed
    // Note: ['read'], // removed
    // Notification: ['read'], // removed
    // Plan: ['read'], // removed
    // Product: ['read'], // removed
    // Proposal: ['read'], // removed
    // Quote: ['read'], // removed
    // Search: ['read'], // removed
    // Segment: ['read'], // removed
    // Service: ['read'], // removed
    // SMTP: ["read"],
    // Ticket: ["read"],
    // Visitor: ['read'], // if desired, you can uncomment Visitor since Admin includes it – uncomment below if needed:
    // Visitor: ["read"],
    // Workflow: ['read'], // removed
    // Backup: ['read'] // removed
    FAQ: ["read"],
    PublicMenu: ["read"],
    CustomVariable: ["read"],
    Discount: ["read"],
    Payment: ["read"],
    Font: ["read"],
    ApiKey: ["read"],
  },

  // Additional roles:

  Client: {
    Certificate: ["read"],
    Badge: ["read"],
    // Contact: ["read"],
    // Dashboard: ["read"],
    // File: ["read"],
    Invoice: ["read"],
    Notification: ["read"],
    // Product: ["read"], // removed – not in Admin’s active list
    // Proposal: ["read"], // removed
    // Service: ["read"], // removed
    // Task: ["read"], // removed
    Ticket: ["create", "read", "update"],
    Visitor: ["read"],
    Dashboard: ["read"],
    FAQ: ["read"],
    PublicMenu: ["read"],
    CustomVariable: ["read"],
    Discount: ["read"],
    Payment: ["read"],
    Font: ["read"],
    ApiKey: ["read"],
  },

  Support: {
    Activity: ["read"],
    Dashboard: ["read"],
    Contact: ["read"],
    File: ["read", "create"],
    Notification: ["read"],
    // Task: ["create", "read", "update"], // removed – not in Admin’s active list
    Ticket: ["create", "read", "update", "delete"],
    Event: ["read"],
    Log: ["read"],
    Visitor: ["read"],
    // Service: ["read"], // removed – not in Admin’s active list
    FAQ: ["read"],
    PublicMenu: ["read"],
    CustomVariable: ["read"],
    Discount: ["read"],
    Payment: ["read"],
    Font: ["read"],
    ApiKey: ["read"],
  },
};

module.exports = rolePermissions;
