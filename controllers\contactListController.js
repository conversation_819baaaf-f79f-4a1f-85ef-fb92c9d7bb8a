const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");
const Contact = require("../models/Contact"); // Assuming the Contact model is located here
const { parse } = require("json2csv"); // Import the parse function from json2csv
const sanitizer = require("sanitizer");
const ContactList = require("../models/ContactList");
const ImportHistory = require("../models/ImportHistory"); // Adjust the path as necessary
const { createLog } = require("../controllers/logController");

// Create a new contact list
exports.createContactList = async (req, res) => {
  try {
    // Sanitize string fields in req.body
    req.body.name = sanitizer.sanitize(req.body.name || "");
    req.body.source = sanitizer.sanitize(req.body.source || "");
    req.body.description = sanitizer.sanitize(req.body.description || "");

    // Ensure contacts is always an array
    if (!Array.isArray(req.body.contacts)) {
      req.body.contacts = [];
    }

    console.log("Sanitized body:", req.body);

    const contactList = new ContactList(req.body);
    contactList.creatorId = req.user.id;

    await contactList.save();
    await createLog(
      {
        eventType: "Contact List",
        action: "Create",
        target: contactList._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"],
      },
      res
    );
    res.status(201).json(contactList);
  } catch (error) {
    console.error("Error creating contact list:", error);
    res.status(400).json({ error: error.message });
  }
};

// Get all contact lists
exports.getContactLists = async (req, res) => {
  try {
    const contactLists = await ContactList.find().populate("contacts");
    res.status(200).json(contactLists);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get a single contact list by ID
exports.getContactListById = async (req, res) => {
  try {
    const contactList = await ContactList.findById(req.params.id).populate(
      "contacts"
    );
    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }
    res.status(200).json(contactList);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update a contact list by ID
exports.updateContactList = async (req, res) => {
  try {
    const contactList = await ContactList.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    //console.log("contact list update req received", req.body)

    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }
    await createLog(
      {
        eventType: "Contact List",
        action: "Modify",
        target: contactList._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"],
      },
      res
    );
    res.status(200).json(contactList);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Delete a contact list by ID
// Delete a contact list by ID and remove its reference from associated contacts
exports.deleteContactList = async (req, res) => {
  try {
    const contactListId = req.params.id;

    // Find the contact list by ID
    const contactList = await ContactList.findByIdAndDelete(contactListId);
    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }

    // Remove the contactListId from the `list` array of each associated contact
    await Contact.updateMany(
      { list: contactListId }, // Find contacts containing the contactListId
      { $pull: { list: contactListId } } // Remove the contactListId from each contact's list array
    );
    await createLog(
      {
        eventType: "Contact List",
        action: "Delete",
        target: contactList._id,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"],
      },
      res
    );

    res.status(200).json({
      message:
        "Contact list deleted successfully and references removed from contacts",
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Search for contact lists
exports.searchContactLists = async (req, res) => {
  try {
    // Extract search parameters from the query
    const { name, description, status, creatorId } = req.query;

    // Build the search query object
    let searchQuery = {};

    if (name) {
      // Perform case-insensitive search using regex
      searchQuery.name = { $regex: new RegExp(name, "i") };
    }

    if (description) {
      // Perform case-insensitive search using regex
      searchQuery.description = { $regex: new RegExp(description, "i") };
    }

    if (status) {
      // Match status exactly as it's an enum field
      searchQuery.status = status;
    }

    if (creatorId) {
      // Match the contact list created by a specific user
      searchQuery.creatorId = creatorId;
    }

    // Find matching contact lists
    const contactLists = await ContactList.find(searchQuery)
      .populate("contacts") // Populate the 'contacts' field with reference data
      .populate("businessId") // Populate the 'businessId' field if needed
      .exec();

    res.status(200).json(contactLists);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

//single contact list functions

// Controller function to add a contact to a contact list and add contact list to contact
exports.addContact = async (req, res) => {
  try {
    const { contactId } = req.body; // Get contactId from request body
    const { id } = req.params; // contactListId from the URL

    console.log("Contact request received for contact list", id, contactId);

    // Check if the contact exists in the Contact collection
    const contactExists = await Contact.findById(contactId);
    if (!contactExists) {
      return res.status(404).json({ message: "Contact not found" });
    }

    const isContactAlreadyExistsInContactList = await ContactList.findOne({
      _id: id,
      contacts: { $in: [contactId] },
    });
    console.log(
      "isContactAlreadyExistsInContactList:",
      isContactAlreadyExistsInContactList
    );
    if (isContactAlreadyExistsInContactList) {
      return res
        .status(400)
        .json({ message: "Contact already exists in list" });
    }

    // Add the contactId to the contacts array in ContactList if not already present
    const contactList = await ContactList.findByIdAndUpdate(
      id,
      { $addToSet: { contacts: contactId } }, // $addToSet avoids adding duplicates
      { new: true } // Return the updated document
    );

    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }

    // Update the Contact document to include the contactListId in the 'list' field
    await Contact.findByIdAndUpdate(
      contactId,
      { $addToSet: { list: id } }, // Ensures the contactListId is only added once
      { new: true }
    );

    await createLog(
      {
        eventType: "Contact List",
        action: "Add",
        target: contactId,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"],
      },
      res
    );

    res.status(200).json(contactList); // Return the updated contact list
  } catch (error) {
    console.log("error adding contact in CL:", error);
    res.status(500).json({ error: error.message });
  }
};

// Controller function to remove a contact from a contact list and contactlist from contact object
exports.removeContact = async (req, res) => {
  try {
    const { contactId } = req.body; // Get contactId from request body
    const { id } = req.params; // contactListId in the URL

    // Check if the contact exists in the Contact collection
    const contactExists = await Contact.findById(contactId);
    if (!contactExists) {
      return res.status(404).json({ message: "Contact not found" });
    }

    // Find the contact list by ID and update it by removing the contactId from contacts array
    const contactList = await ContactList.findByIdAndUpdate(
      id,
      { $pull: { contacts: contactId } }, // $pull removes the contactId from the contacts array
      { new: true } // Return the updated document
    );

    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }

    // Remove the contactListId from the list field of the Contact document
    await Contact.findByIdAndUpdate(
      contactId,
      { $pull: { list: id } }, // $pull removes the contactListId from the list field
      { new: true }
    );

    await createLog(
      {
        eventType: "Contact List",
        action: "Remove",
        target: contactId,
      },
      {
        user: {
          _id: req.user?._id,
          businessId: req.user?.businessId,
          role: req.user?.role,
        },
        ip: req.ip,
        headers: req.headers["user-agent"],
      },
      res
    );
    res.status(200).json(contactList); // Send back the updated contact list
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Controller function to export all contacts associated with a single contactList to CSV
exports.exportContactsToCSV = async (req, res) => {
  try {
    const { id } = req.params; // contactListId from the URL

    // Find the contact list by ID and populate contacts
    const contactList = await ContactList.findById(id).populate("contacts");
    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }

    // Check if there are contacts to export
    if (contactList.contacts.length === 0) {
      return res
        .status(404)
        .json({ message: "No contacts found in this contact list" });
    }

    // Map contacts to a simpler format (if needed) for CSV export
    const contactsToExport = contactList.contacts.map((contact) => ({
      fullName: contact.fullName,
      businessEmail: contact.businessEmail,
      certificates: contact.certificates,
      badges: contact.badges,
      // phoneNumber: contact.phoneNumber,
      // Add other fields you want to include
    }));

    // Convert contacts to CSV format with specified fields
    const csv = parse(contactsToExport);

    // Get current date and time
    const now = new Date();
    const formattedDate = now.toISOString().replace(/[:.]/g, "-").split("T")[0]; // YYYY-MM-DD
    const formattedTime = now.toTimeString().split(" ")[0].replace(/:/g, "-"); // HH-MM-SS
    const dateTimeString = `${formattedDate}_${formattedTime}`; // Combine date and time

    // Set headers to prompt a file download
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="contacts_${id}_${dateTimeString}.csv"`
    );

    // Send the CSV data as the response
    res.status(200).send(csv);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Controller function to export all contacts associated with a single contactList to CSV
exports.exportContactsToCSVJSON = async (req, res) => {
  try {
    const { id } = req.params; // contactListId from the URL

    // Find the contact list by ID
    const contactList = await ContactList.findById(id).populate("contacts");
    if (!contactList) {
      return res.status(404).json({ message: "Contact list not found" });
    }

    // Check if there are contacts to export
    if (contactList.contacts.length === 0) {
      return res
        .status(404)
        .json({ message: "No contacts found in this contact list" });
    }

    // Convert contacts to CSV format
    const csv = parse(contactList.contacts);

    // Get current date and time
    const now = new Date();
    const formattedDate = now.toISOString().replace(/[:.]/g, "-").split("T")[0]; // YYYY-MM-DD
    const formattedTime = now.toTimeString().split(" ")[0].replace(/:/g, "-"); // HH-MM-SS
    const dateTimeString = `${formattedDate}_${formattedTime}`; // Combine date and time

    // Set headers to prompt a file download
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="contacts_${id}_${dateTimeString}.csv"`
    );

    // Send the CSV data as the response
    res.status(200).send(csv);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Controller function to import contacts into a specific contact list from a CSV file
exports.importContactsToContactList = async (req, res) => {
  const { csvFileUrl, contactListId } = req.body;

  // Check if CSV file URL and contact list ID are provided
  if (!csvFileUrl || !contactListId) {
    return res
      .status(400)
      .json({ error: "No CSV file URL or contact list ID provided" });
  }

  // Construct the local file path from the URL
  const baseUrl = `${req.protocol}://${req.get("host")}`;
  const relativeFilePath = csvFileUrl.replace(`${baseUrl}/`, "");
  const filePath = path.join(__dirname, "../public/", relativeFilePath);

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: "File not found" });
  }

  let contacts = [];
  let ignoredContacts = [];
  let startTime = Date.now();

  // Read the CSV file
  const readStream = fs.createReadStream(filePath).pipe(csv());

  // Process each row in the CSV
  for await (const row of readStream) {
    // Ensure mandatory fields are provided
    if (!row.fullName || !row.businessEmail) {
      ignoredContacts.push({
        ...row,
        reason: "Missing mandatory fields (fullName or businessEmail)",
      });
      continue; // Skip this row
    }

    // Set additional fields
    row.creatorId = req.user._id;
    row.src = "csv import";

    // Check for existing contacts with the same businessEmail
    const existingContact = await Contact.findOne({
      businessEmail: row.businessEmail,
    });

    if (existingContact) {
      ignoredContacts.push({
        ...row,
        reason: "Email already exists in the database",
      });
    } else {
      contacts.push(row);
    }
  }

  try {
    let totalImported = 0;
    let importStatus = "Failed";

    // Find the contact list by ID
    const contactList = await ContactList.findById(contactListId);
    if (!contactList) {
      return res.status(404).json({ error: "Contact list not found" });
    }

    // Add valid contacts to the contact list
    if (contacts.length > 0) {
      // Create new contacts and add them to the contact list
      const result = await Contact.insertMany(contacts);
      totalImported = result.length;

      // Update the contact list with the new contacts' IDs
      const contactIds = result.map((contact) => contact._id);
      contactList.contacts.push(...contactIds);
      await contactList.save();

      importStatus = "completed";
    }

    // Calculate ignored contacts and duration
    let totalIgnored = ignoredContacts.length;
    let durationToImport = (Date.now() - startTime) / 1000; // Duration in seconds

    // Save the import history
    const importHistory = new ImportHistory({
      importedFileName: path.basename(filePath),
      importedFileURL: csvFileUrl,
      importedBy: req.user._id,
      importType: "contact",
      totalImported: totalImported,
      totalIgnored: totalIgnored,
      ignoredFileURL: totalIgnored ? csvFileUrl : "", // URL for ignored contacts
      durationToImport: durationToImport,
      status: importStatus,
    });

    const importedHistory = await importHistory.save();

    // Optional: Clean up the uploaded file if needed
    // fs.unlinkSync(filePath); // Uncomment to delete the file after import

    return res.status(200).json({
      message: `${totalImported} Contacts imported, ${totalIgnored} Contacts ignored`,
      totalImported,
      totalIgnored,
      ignoredContacts,
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};
