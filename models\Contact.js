const mongoose = require("mongoose");

const contactSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: false,
    },
    lastName: {
      type: String,
      required: false,
    },
    fullName: {
      type: String,
      required: true,
    },
    jobTitle: {
      type: String,
      required: false,
    },

    company: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Company",
    },

    companyName: {
      type: String,
      required: false,
    },

    companySize: {
      type: String,
      enum: [
        "0-1",
        "2-10",
        "11-50",
        "51-200",
        "201-500",
        "501-1000",
        "1001-5000",
        "5001-10000",
        "10000+",
      ],
      required: false,
    },

    phoneNumber: {
      type: String,
      required: false,
    },

    businessEmail: {
      type: String,
      required: true,
  
    },

    personalPhone: {
      type: String,
    },
    personalEmail: {
      type: String,
    },

    website: {
      type: String,
      required: false,
    },

    linkedin: {
      type: String,
      required: false,
    },
    facebook: {
      type: String,
      required: false,
    },
    twitter: {
      type: String,
      required: false,
    },
    blog: {
      type: String,
      required: false,
    },

    birthday: {
      type: Date,
      required: false,
    },
    city: {
      type: String,
      required: false,
    },
    state: {
      type: String,
      required: false,
    },
    country: {
      type: String,
      required: false,
    },
    serviceInterest: {
      type: String,
      required: false,
    },
    industry: {
      type: String,
      required: false,
    },

    source: {
      type: String,
      required: false,
    },
    referral: {
      type: Boolean,
      required: false,
    },

    contactStatus: {
      type: String,
      enum: [
        "New",
        "In Progress",
        "Closed",
        "Converted",
        "Contacted",
        "Qualified",
      ],
      required: false,
    },

    subscribedTo: {
      //it should be listing all the email list, blog, and types of subscription he current has
      required: false,
    },

    //list contactList Ids in which this is added
    list: [
      {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
        ref: "ContactList",
      },
    ],

    //store in another object
    activities: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Activity",
    },

    meetings: {
      type: Array,
      required: false,
    },

    likes: {
      type: String,
      required: false,
    },

    communication: {
      email: { type: Boolean, default: false },
      sms: { type: Boolean, default: false },
      phone: { type: Boolean, default: false },
      linkedIn: { type: Boolean, default: false },
      whatsApp: { type: Boolean, default: false },
    },
    preferredContactMethod: {
      type: String,
      enum: ["Email", "SMS", "Phone", "LinkedIn", "WhatsApp"],
      required: false,
    },
    notes: {
      type: [String], // Array of strings
      required: false,
    },
    deals: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Deal",
    },
    emails: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Email",
    },
    calls: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Call",
    },
    tasks: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Task",
    },
    meeting: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "Meeting",
    },
    tags: {
      type: [String],
      required: false,
      default: " ",
    },

    //Modified for MixCertificate
    certificates: [
      {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
        ref: "Certificate",
      },
    ],
    badges: [
      {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
        ref: "Badge",
      },
    ],
    creatorId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "User",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
      ref: "User",
    },
    businessId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Business",
    },

    variables: {
      type: Map,
      of: String,
      default: {},
    },
    accountManager: {
      type: String,
    },

    companyAddress: {
      type: String,
      required: false,
    },
    companyRevenue:{
      type: String,
      required: false,
    },
    leadSource:{
      type: String,
      required: false,
    },
    linkedinProfile:{
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const Contact = mongoose.model("Contact", contactSchema);

module.exports = Contact;
