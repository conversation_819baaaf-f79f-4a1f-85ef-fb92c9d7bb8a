


<!-- Search Section Starts -->
<section id="search" class="py-5 mt-5">
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-md-10 col-lg-8">
        <div class="card border-0 shadow">
          <div class="card-body p-4 p-md-5">
            <h2 class="text-center mb-4">Search for Badge or Certificate</h2>
            <p class="text-center text-muted mb-4">
              Enter the ID to verify and view details of a certificate or badge
              issued by Shine Events.
            </p>

            <div class="d-flex justify-content-center mb-4">
              <div
                class="btn-group"
                role="group"
                aria-label="Search type selection"
              >
                <input
                  type="radio"
                  class="btn-check"
                  name="searchType"
                  id="certificate"
                  value="certificate"
                  checked
                />
                <label
                  class="btn"
                  for="certificate"
                  style="border: 1px solid #5156be; color: #5156be"
                >
                  <i class="fas fa-certificate me-2"></i>Certificate
                </label>

                <input
                  type="radio"
                  class="btn-check"
                  name="searchType"
                  id="badge"
                  value="badge"
                />
                <label
                  class="btn"
                  for="badge"
                  style="border: 1px solid #5156be; color: #5156be"
                >
                  <i class="fas fa-shield-alt me-2"></i>Badge
                </label>
              </div>
            </div>

            <form id="search-form" class="mb-4">
              <div class="input-group input-group-lg">
                <span class="input-group-text bg-light border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input
                  type="text"
                  id="search-query"
                  class="form-control bg-light border-start-0"
                  placeholder="Enter ID number"
                  aria-label="Search Query"
                />
                <button
                  class="btn px-4"
                  type="submit"
                  style="background-color: #5156be; color: white"
                >
                  Search
                </button>
              </div>
              <div class="form-text text-center mt-2">
                Example: Enter the unique ID provided in your email or
                certificate document
              </div>
            </form>

            <div id="search-results" class="mt-4"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- Search Section Ends -->

<!-- FAQ Starts -->
<section id="faq" class="py-5 my-5">
  <div class="container">
    <h2 class="text-center mb-4">Frequently Asked Questions</h2>
    <div class="accordion" id="faqAccordion">
      <!-- Certificate FAQ -->
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingCertificateOne">
          <button
            class="accordion-button"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseCertificateOne"
            aria-expanded="true"
            aria-controls="collapseCertificateOne"
          >
            What is a Shine Events Certificate?
          </button>
        </h2>
        <div
          id="collapseCertificateOne"
          class="accordion-collapse collapse show"
          aria-labelledby="headingCertificateOne"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            A Shine Events Certificate is an official document that verifies
            your participation and role in major events managed by Shine. It
            showcases your achievements, experience, and the specific projects
            you’ve been part of.
          </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingCertificateTwo">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseCertificateTwo"
            aria-expanded="false"
            aria-controls="collapseCertificateTwo"
          >
            How can I obtain my Certificate?
          </button>
        </h2>
        <div
          id="collapseCertificateTwo"
          class="accordion-collapse collapse"
          aria-labelledby="headingCertificateTwo"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            Once you have successfully completed your event or project, your
            certificate will be generated automatically. You can search for and
            download your certificate using the Certificate ID provided to you
            via email.
          </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingCertificateThree">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseCertificateThree"
            aria-expanded="false"
            aria-controls="collapseCertificateThree"
          >
            Can I share my Certificate with others?
          </button>
        </h2>
        <div
          id="collapseCertificateThree"
          class="accordion-collapse collapse"
          aria-labelledby="headingCertificateThree"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            Absolutely! Your certificate comes with a unique URL that you can
            share on LinkedIn, your resume, or with potential employers to
            showcase your experience and achievements.
          </div>
        </div>
      </div>

      <!-- Badge FAQ -->
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingBadgeOne">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseBadgeOne"
            aria-expanded="false"
            aria-controls="collapseBadgeOne"
          >
            What is a Shine Events Badge?
          </button>
        </h2>
        <div
          id="collapseBadgeOne"
          class="accordion-collapse collapse"
          aria-labelledby="headingBadgeOne"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            A Shine Events Badge is a digital recognition of your skills and
            accomplishments. It represents your expertise in specific areas,
            such as event management, technical skills, or leadership roles
            within Shine Events.
          </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingBadgeTwo">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseBadgeTwo"
            aria-expanded="false"
            aria-controls="collapseBadgeTwo"
          >
            How do I earn a Shine Events Badge?
          </button>
        </h2>
        <div
          id="collapseBadgeTwo"
          class="accordion-collapse collapse"
          aria-labelledby="headingBadgeTwo"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            Badges are awarded based on your performance and participation in
            specific events or training programs. They are automatically issued
            once you meet the criteria set for each badge.
          </div>
        </div>
      </div>

      <div class="accordion-item">
        <h2 class="accordion-header" id="headingBadgeThree">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#collapseBadgeThree"
            aria-expanded="false"
            aria-controls="collapseBadgeThree"
          >
            Where can I view and share my Badge?
          </button>
        </h2>
        <div
          id="collapseBadgeThree"
          class="accordion-collapse collapse"
          aria-labelledby="headingBadgeThree"
          data-bs-parent="#faqAccordion"
        >
          <div class="accordion-body">
            You can view your earned badges in your Shine Events profile. Each
            badge comes with a unique link that you can share on social media,
            your portfolio, or with employers to highlight your skills and
            accomplishments.
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Ends -->

<!-- brand content section starts -->
<div class="text-center my-5 container pb-5">
  <h2 class="fw-bold">Empowering Events with Exceptional Talent</h2>
  <p class="lead">
    At Shine, we're dedicated to bringing together top-notch freelancers and
    businesses to create outstanding event experiences across Saudi Arabia. We
    understand the importance of quality and professionalism, which is why we
    collaborate with the best in the industry to ensure every event is a
    success. Our platform not only connects you with the right talent but also
    offers certifications and badges that recognize and reward the skills and
    achievements of our freelancers. Whether you're looking to host a corporate
    gathering, a cultural festival, or any other event, Shine is here to ensure
    it's executed with excellence, helping you engage more participants and
    create memorable experiences.
  </p>
</div>

<!-- brand content section ends -->
 <h2 class="mb-4 text-center">Create Support Ticket</h2>

<div class="container my-5">
  <div class="">
    <div class="">
      <form
        id="ticketForm"
        enctype="multipart/form-data"
        class="p-4 border rounded shadow-sm bg-white"
      >
       
        <input
          type="hidden"
          id="businessId"
          name="businessId"
          value="<%= businessId %>"
        />

        <div class="mb-3">
          <label for="title" class="form-label"
            >Title <span class="text-danger">*</span></label
          >
          <input
            type="text"
            class="form-control"
            id="title"
            name="title"
            required
          />
        </div>

        <div class="mb-3">
          <label for="description" class="form-label">Description</label>
          <textarea
            class="form-control"
            id="description"
            name="description"
            rows="3"
          ></textarea>
        </div>

        <div class="mb-3">
          <label class="form-label d-block">Priority</label>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              name="priority"
              id="priorityLow"
              value="Low"
              required
            />
            <label class="form-check-label" for="priorityLow">Low</label>
          </div>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              name="priority"
              id="priorityMedium"
              value="Medium"
            />
            <label class="form-check-label" for="priorityMedium">Medium</label>
          </div>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              name="priority"
              id="priorityHigh"
              value="High"
            />
            <label class="form-check-label" for="priorityHigh">High</label>
          </div>
          <div class="form-check form-check-inline">
            <input
              class="form-check-input"
              type="radio"
              name="priority"
              id="priorityUrgent"
              value="Urgent"
            />
            <label class="form-check-label" for="priorityUrgent">Urgent</label>
          </div>
        </div>

        <div class="mb-3">
          <label for="type" class="form-label">Type</label>
          <select class="form-select" id="type" name="type">
            <option value="">Select Type</option>
            <option value="Bug">Bug</option>
            <option value="Feature Request">Feature Request</option>
            <option value="Support">Support</option>
            <option value="Task">Task</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="tagInput" class="form-label">Tags</label>
          <input
            type="text"
            class="form-control"
            id="tagInput"
            placeholder="Type tag and press Enter"
          />
          <div id="tagContainer" class="mt-2"></div>
        </div>

        <div class="mb-3">
          <label for="attachments" class="form-label">Attachments</label>
          <input
            type="file"
            class="form-control"
            id="attachments"
            name="attachments"
            multiple
          />
        </div>

        <button type="submit" class="btn btn-primary w-100">
          Create Ticket
        </button>
      </form>
    </div>
  </div>
</div>

<!-- footer content starts -->
<section class="bg-dark text-white py-4 pb-5">
  <div class="container text-center py-5">
    <h5 class="text-white">Connect with Shine</h5>
    <p>
      Follow us on social media and stay updated with our latest news and
      opportunities.
    </p>
    <div class="d-flex justify-content-center my-3 pb-5">
      <a href="#" class="text-white me-3" aria-label="Facebook">
        <i class="fab fa-facebook-f fa-lg"></i>
      </a>
      <a href="#" class="text-white me-3" aria-label="Twitter">
        <i class="fab fa-twitter fa-lg"></i>
      </a>
      <a
        href="https://sa.linkedin.com/company/shine-event-staffing"
        class="text-white me-3"
        aria-label="LinkedIn"
      >
        <i class="fab fa-linkedin-in fa-lg"></i>
      </a>
      <a
        href="https://www.instagram.com/shine.arabia/?hl=en"
        class="text-white"
        aria-label="Instagram"
      >
        <i class="fab fa-instagram fa-lg"></i>
      </a>
    </div>
  </div>
</section>

<!-- footer content ends -->

<!-- Include Font Awesome for icons -->
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
/>

<!-- Custom styles for search results and tickets -->
<style>
  /* Card hover effect */
  .result-card {
    transition: all 0.3s ease;
  }

  .result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  /* Animation for search results */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  #search-results > * {
    animation: fadeIn 0.5s ease;
  }

  /* Custom radio button styling */
  .btn-check:checked + .btn {
    background-color: #5156be !important;
    color: white !important;
    border-color: #5156be !important;
  }

  /* Single Ticket Container */
  .single-ticket-container {
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 10px;
  }

  .single-ticket-container::-webkit-scrollbar {
    width: 6px;
  }

  .single-ticket-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .single-ticket-container::-webkit-scrollbar-thumb {
    background: #5156be;
    border-radius: 3px;
  }

  .single-ticket-container::-webkit-scrollbar-thumb:hover {
    background: #4c51b8;
  }

  /* Ticket Styles */
  .ticket-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
    border-radius: 1rem;
  }

  .ticket-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .ticket-header {
    background: linear-gradient(135deg, #5156be 0%, #6366f1 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
  }

  .ticket-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
  }

  .status-open {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  .status-in-progress {
    background-color: #fff3e0;
    color: #f57c00;
  }
  .status-resolved {
    background-color: #e8f5e8;
    color: #388e3c;
  }
  .status-closed {
    background-color: #fce4ec;
    color: #c2185b;
  }

  .priority-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    border-radius: 0.8rem;
    font-weight: 600;
  }

  .priority-low {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
  .priority-medium {
    background-color: #fff3e0;
    color: #ef6c00;
  }
  .priority-high {
    background-color: #ffebee;
    color: #d32f2f;
  }
  .priority-urgent {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }

  .type-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
    border-radius: 0.8rem;
    font-weight: 500;
  }

  .type-bug {
    background-color: #ffebee;
    color: #c62828;
  }
  .type-feature {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
  .type-support {
    background-color: #e3f2fd;
    color: #1565c0;
  }
  .type-task {
    background-color: #fff3e0;
    color: #ef6c00;
  }

  .comment-section {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
  }

  .comment-item {
    background: white;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-left: 3px solid #5156be;
  }

  .comment-user {
    font-weight: 600;
    color: #5156be;
    font-size: 0.85rem;
  }

  .comment-date {
    font-size: 0.75rem;
    color: #6c757d;
  }

  .comment-message {
    color: #495057;
    font-size: 0.9rem;
    margin: 0.5rem 0 0 0;
    line-height: 1.4;
  }

  .comment-input {
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    transition: border-color 0.3s ease;
  }

  .comment-input:focus {
    border-color: #5156be;
    box-shadow: 0 0 0 0.2rem rgba(81, 86, 190, 0.25);
  }

  .comment-btn {
    background: #5156be;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
  }

  .comment-btn:hover {
    background: #4c51b8;
    transform: translateY(-1px);
  }

  .comment-btn:disabled {
    background: #6c757d;
    transform: none;
  }

  .comments-container {
    max-height: 300px;
    overflow-y: auto;
  }

  .comments-container::-webkit-scrollbar {
    width: 4px;
  }

  .comments-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  .comments-container::-webkit-scrollbar-thumb {
    background: #5156be;
    border-radius: 2px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .detail-row {
      flex-direction: column;
    }

    .detail-label {
      width: 100% !important;
      margin-bottom: 0.25rem;
    }

    .ticket-card {
      margin-bottom: 15px;
    }

    .comments-container {
      max-height: 200px;
    }
  }

  @media (max-width: 576px) {
    .ticket-header {
      padding: 1rem !important;
    }

    .ticket-header h5 {
      font-size: 1rem;
    }
  }
</style>

<!-- Include jQuery -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script>
  $(document).ready(function () {
    const currentUrl = window.location.pathname;
    const paths = currentUrl.split("/").filter(Boolean);
    const nameSpaceCode = paths[0];
    $("#search-form").on("submit", function (e) {
      e.preventDefault();

      var query = $("#search-query").val().trim();
      var searchType = $('input[name="searchType"]:checked').val();

      if (query) {
        // Show loading indicator
        $("#search-results").html(
          '<div class="text-center my-4">' +
            '<div class="spinner-border text-primary" role="status">' +
            '<span class="visually-hidden">Loading...</span>' +
            "</div>" +
            '<p class="mt-2 text-muted">Searching for ' +
            searchType +
            "...</p>" +
            "</div>"
        );

        if (searchType === "certificate") {
          searchCertificate(query);
        } else if (searchType === "badge") {
          searchBadge(query);
        }
      } else {
        $("#search-results").html(
          '<div class="alert alert-warning" role="alert">' +
            '<i class="fas fa-exclamation-triangle me-2"></i>' +
            "Please enter a valid ID to search." +
            "</div>"
        );
      }
    });
    function searchCertificate(query) {
      var endpoint = `/${nameSpaceCode}/certificate/api/certificate/${query}`;

      $.ajax({
        url: endpoint,
        method: "GET",
        success: function (response) {
          if (response.success) {
            var certificate = response.certificate;

            var resultHtml =
              '<div class="card border-0 shadow result-card">' +
              '<div class="card-header d-flex align-items-center py-3" style="background-color: #5156be; color: white;">' +
              '<i class="fas fa-certificate me-2"></i>' +
              '<h5 style="color: #ffffff;" class="mb-0">Certificate Found</h5>' +
              "</div>" +
              '<div class="card-body p-4">' +
              '<div class="row">' +
              '<div class="col-md-8">' +
              '<h4  class="card-title">' +
              certificate.certificateName +
              "</h4>" +
              '<p class="card-text text-muted mb-4">ID: ' +
              query +
              "</p>" +
              '<div class="mb-3">' +
              '<h6 class="fw-bold mb-3">Recipient Details</h6>' +
              '<div class="d-flex mb-2 detail-row">' +
              '<div class="text-muted detail-label" style="width: 120px;"><i class="fas fa-user me-2"></i>Name:</div>' +
              '<div class="fw-medium">' +
              certificate.recipientName +
              "</div>" +
              "</div>" +
              '<div class="d-flex mb-2 detail-row">' +
              '<div class="text-muted detail-label" style="width: 120px;"><i class="fas fa-briefcase me-2"></i>Position:</div>' +
              '<div class="fw-medium">' +
              certificate.position +
              "</div>" +
              "</div>" +
              '<div class="d-flex mb-2 detail-row">' +
              '<div class="text-muted detail-label" style="width: 120px;"><i class="fas fa-map-marker-alt me-2"></i>Location:</div>' +
              '<div class="fw-medium">' +
              certificate.location +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              '<div class="col-md-4 text-center d-flex align-items-center justify-content-center">' +
              "<div>" +
              '<div class="bg-light rounded-circle p-3 d-inline-block mb-3">' +
              '<i class="fas fa-award" style="font-size: 3rem; color: #5156be;"></i>' +
              "</div>" +
              "<div>" +
              '<a href="/certificate/' +
              certificate._id +
              '" target="_blank" class="btn" style="background-color: #5156be; color: white;">' +
              '<i class="fas fa-external-link-alt me-2"></i>View Certificate' +
              "</a>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>";

            $("#search-results").html(resultHtml);
          } else {
            $("#search-results").html(
              '<div class="alert alert-danger d-flex align-items-center" role="alert">' +
                '<i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem;"></i>' +
                "<div>" +
                '<h5 class="alert-heading mb-1">Certificate Not Found</h5>' +
                '<p class="mb-0">We couldn\'t find any certificate with ID: ' +
                query +
                "</p>" +
                "</div>" +
                "</div>"
            );
          }
        },
        error: function () {
          $("#search-results").html(
            '<div class="alert alert-danger d-flex align-items-center" role="alert">' +
              '<i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem;"></i>' +
              "<div>" +
              '<h5 class="alert-heading mb-1">Certificate Not Found</h5>' +
              '<p class="mb-0">We couldn\'t find any certificate with ID: ' +
              query +
              "</p>" +
              "</div>" +
              "</div>"
          );
        },
      });
    }

    function searchBadge(query) {
      var endpoint = `/${nameSpaceCode}/badge/api/${query}`;

      $.ajax({
        url: endpoint,
        method: "GET",
        success: function (response) {
          if (response.verified) {
            var badge = response;

            // Format date if available
            var formattedDate = badge.dateOfIssue
              ? new Date(badge.dateOfIssue).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })
              : "Not specified";

            var resultHtml =
              '<div class="card border-0 shadow result-card">' +
              '<div class="card-header d-flex align-items-center py-3" style="background-color: #5156be; color: white;">' +
              '<i class="fas fa-shield-alt me-2"></i>' +
              '<h5 style="color: #ffffff;" class="mb-0">Badge Found</h5>' +
              "</div>" +
              '<div class="card-body p-4">' +
              '<div class="row">' +
              '<div class="col-md-8">' +
              '<h4 class="card-title">' +
              badge.title +
              "</h4>" +
              '<p class="card-text text-muted mb-4">ID: ' +
              query +
              "</p>" +
              '<div class="mb-3">' +
              '<h6 class="fw-bold mb-3">Badge Details</h6>' +
              '<div class="d-flex mb-2 detail-row">' +
              '<div class="text-muted detail-label" style="width: 120px;"><i class="fas fa-info-circle me-2"></i>Description:</div>' +
              '<div class="fw-medium">' +
              badge.description +
              "</div>" +
              "</div>" +
              '<div class="d-flex mb-2 detail-row">' +
              '<div class="text-muted detail-label" style="width: 120px;"><i class="fas fa-calendar-alt me-2"></i>Issued On:</div>' +
              '<div class="fw-medium">' +
              formattedDate +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              '<div class="col-md-4 text-center d-flex align-items-center justify-content-center">' +
              "<div>" +
              '<div class="bg-light rounded-circle p-3 d-inline-block mb-3">' +
              '<i class="fas fa-medal" style="font-size: 3rem; color: #5156be;"></i>' +
              "</div>" +
              "<div>" +
              '<a href="/badge/' +
              badge._id +
              '" target="_blank" class="btn" style="background-color: #5156be; color: white;">' +
              '<i class="fas fa-external-link-alt me-2"></i>View Badge' +
              "</a>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>" +
              "</div>";

            $("#search-results").html(resultHtml);
          } else {
            $("#search-results").html(
              '<div class="alert alert-danger d-flex align-items-center" role="alert">' +
                '<i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem;"></i>' +
                "<div>" +
                '<h5 class="alert-heading mb-1">Badge Not Found</h5>' +
                '<p class="mb-0">We couldn\'t find any badge with ID: ' +
                query +
                "</p>" +
                "</div>" +
                "</div>"
            );
          }
        },
        error: function () {
          $("#search-results").html(
            '<div class="alert alert-danger d-flex align-items-center" role="alert">' +
              '<i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem;"></i>' +
              "<div>" +
              '<h5 class="alert-heading mb-1">Badge Not Found</h5>' +
              '<p class="mb-0">We couldn\'t find any badge with ID: ' +
              query +
              "</p>" +
              "</div>" +
              "</div>"
          );
        },
      });
    }
  });
</script>



<script>
  $(document).ready(function () {
    const tags = [];
    $("#tagInput").on("keypress", function (e) {
      if (e.which === 13) {
        e.preventDefault();
        const tag = $(this).val().trim();
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
          $("#tagContainer").append(
            `<span class="badge bg-secondary me-1 mb-1">${tag}</span>`
          );
        }
        $(this).val("");
      }
    });
    $("#ticketForm").on("submit", function (e) {
      e.preventDefault();

      const formData = new FormData(this);

      formData.delete("tags");

      formData.append("tags", JSON.stringify(tags));

      $.ajax({
        url: "/api/tickets",
        type: "POST",
        data: formData,
        contentType: false,
        processData: false,
        success: function (response) {
          alert("Ticket created successfully!");
          $("#ticketForm")[0].reset();
          $("#tagContainer").empty();
          tags.length = 0;
        },
        error: function (xhr) {
          alert("Failed to create ticket.");
          console.error(xhr.responseText);
        },
      });
    });
  });
</script>
