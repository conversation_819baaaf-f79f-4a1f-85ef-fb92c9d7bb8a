const express = require("express");
const router = express.Router();
const logController = require("../controllers/logController");
const { checkPermissions } = require("../middleware/middleware");
const { rateLimit } = require("express-rate-limit");

const logsRateLimiter = rateLimit({
  windowMs: 30 * 60 * 1000,
  max: 100,
  message: {
    success: false,
    message: "Too many requests, please try again later.",
  },
  standardHeaders: true,
  // legacyHeaders: false,
});

router.get(
  "/paginated",
  logsRateLimiter,
  checkPermissions("Log", ["read"]),
  logController.getLogs
);

module.exports = router;
